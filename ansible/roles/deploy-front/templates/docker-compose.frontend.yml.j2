version: "3.7"
services:
  taa_front:
    image: "{{ lookup('env','IMAGE_NAME') }}"
    container_name: "{{ project_name }}"
    restart: always
    environment:
      VIRTUAL_HOST: "{{ host_name }}"
      LETSENCRYPT_HOST: "{{ host_name }}"
      LETSENCRYPT_EMAIL: "{{ letsencrypt_email }}"
      VITE_MODE: "dev"
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=nginx-proxy"
      - "traefik.http.routers.{{ project_name }}-{{project_version}}-web.entrypoints=https"
      - "traefik.http.routers.{{ project_name }}-{{project_version}}-web.rule=Host(`{{ host_name }}`)"
      - "traefik.http.routers.{{ project_name }}-{{project_version}}-web.tls=true"
      - "traefik.http.routers.{{ project_name }}-{{project_version}}-web.tls.certresolver=letsencrypt"
      - "traefik.http.routers.{{ project_name }}-{{project_version}}-web.middlewares=security-headers"
      - "traefik.http.services.{{ project_name }}-{{project_version}}-web.loadbalancer.server.port=80"
      - "traefik.http.middlewares.{{ project_name }}-{{project_version}}-web.headers.customRequestHeaders.X-Forwarded-Host={{ host_name }}"
    networks:
      - default
networks:
  default:
    external:
      name: "{{ docker_network }}"
volumes:
      postgres_data: {}
