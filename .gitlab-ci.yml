image: docker:stable

stages:
  - package
  - build-sonar
  - deploy

package:
  stage: package
  image: docker:24.0
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - echo "$CI_JOB_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - export IMAGE_NAME="$CI_REGISTRY_IMAGE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHA"
  after_script:
    - docker logout "$CI_REGISTRY"
  script:
    - docker build --pull -t "$IMAGE_NAME" -f Dockerfile .
    - docker push $IMAGE_NAME
  only:
    - develop

# ---------------------
# Sonar Scanner
# ---------------------
build-sonar:
  stage: build-sonar
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: ['']
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar' 
    GIT_DEPTH: '0' 
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "develop"'

ansible-deploy:
  stage: deploy
  image: alpine/ansible:2.17.0
  before_script:
    - chmod 600 $SSH_KEY
    - export IMAGE_NAME="$CI_REGISTRY_IMAGE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHA"
    - export DEPLOY_TARGET=dev
    - export ENV_DEV=$ENV_DEV
    - cd ansible
  script:
    - ansible-playbook --key-file $SSH_KEY playbook.yml
  after_script:
    - cd ..
  only:
     - develop
