// src/APIS/axios.js
import axios from "axios";
import router from "@/router";
import { getErrorMessage } from "@/utils/errorHandler";

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

console.log("API Base URL:", import.meta.env.VITE_API_BASE_URL);

const openRoutes = ["/signin", "/create_entreprise", "/forgot_password"];

// Request interceptor to add the Authorization header for protected routes
apiClient.interceptors.request.use(
  (config) => {
    const isProtectedRoute = !openRoutes.some((route) =>
      config.url.includes(route)
    );
    if (isProtectedRoute) {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle errors such as 401 and 403
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    const { response, config } = error;

    // Handle authentication errors (401, 403)
    if (response && [401, 403].includes(response.status)) {
      // Check if the URL is not an open route before forcing a redirect.
      if (!openRoutes.some((route) => config.url.includes(route))) {
        localStorage.removeItem("token");
        // Optionally, log a message or show a notification
        console.warn(
          `Access denied (status: ${response.status}). Redirecting to signin...`
        );
        router.push("/signin");
      }
    }

    // Process the error message to make it more user-friendly
    if (response && response.data) {
      // Transform the error message
      const userFriendlyMessage = getErrorMessage(error);

      // Update the error object with the user-friendly message
      if (userFriendlyMessage) {
        error.response.data.errorMessage = userFriendlyMessage;
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
