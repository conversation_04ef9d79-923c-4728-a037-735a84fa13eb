import apiClient from "../APIS/axios";

export default {
  getOrganization(organizationId) {
    return apiClient.get(`/organizations/${organizationId}/general`);
  },
  getOrganizations(filters = {}) {
    return apiClient.get("/organizations", { params: filters });
  },
  createOrganization(payload) {
    return apiClient.post("/organizations", payload);
  },
  updateOrganization(organizationId, payload) {
    return apiClient.put(`/organizations/${organizationId}/general`, payload);
  },
};
