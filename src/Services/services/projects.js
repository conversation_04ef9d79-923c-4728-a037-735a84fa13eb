import api from "../api";

export default {
  /**
   * Get R&D projects for an organization
   * @param {string} organizationId - The organization ID
   * @returns {Promise} - The API response
   */
  getRdProjects(organizationId) {
    return api.get(`/organizations/${organizationId}/rd-projects`);
  },

  /**
   * Create a new R&D project
   * @param {string} organizationId - The organization ID
   * @param {Object} project - The project data
   * @returns {Promise} - The API response
   */
  createRdProject(organizationId, project) {
    return api.post(`/organizations/${organizationId}/rd-projects`, project);
  },

  /**
   * Update an existing R&D project
   * @param {string} organizationId - The organization ID
   * @param {string} projectId - The project ID
   * @param {Object} project - The updated project data
   * @returns {Promise} - The API response
   */
  updateRdProject(organizationId, projectId, project) {
    return api.put(`/organizations/${organizationId}/rd-projects/${projectId}`, project);
  },

  /**
   * Delete an R&D project
   * @param {string} organizationId - The organization ID
   * @param {string} projectId - The project ID
   * @returns {Promise} - The API response
   */
  deleteRdProject(organizationId, projectId) {
    return api.delete(`/organizations/${organizationId}/rd-projects/${projectId}`);
  },

  /**
   * Batch update R&D projects (create, update, delete)
   * @param {string} organizationId - The organization ID
   * @param {Array} projects - The projects data
   * @returns {Promise} - The API response
   */
  batchUpdateRdProjects(organizationId, projects) {
    return api.put(`/organizations/${organizationId}/rd-projects/batch`, { projects });
  },

  /**
   * Get initiatives for an organization
   * @param {string} organizationId - The organization ID
   * @returns {Promise} - The API response
   */
  getInitiatives(organizationId) {
    return api.get(`/organizations/${organizationId}/initiatives`);
  },

  /**
   * Create a new initiative
   * @param {string} organizationId - The organization ID
   * @param {Object} initiative - The initiative data
   * @returns {Promise} - The API response
   */
  createInitiative(organizationId, initiative) {
    return api.post(`/organizations/${organizationId}/initiatives`, initiative);
  },

  /**
   * Update an existing initiative
   * @param {string} organizationId - The organization ID
   * @param {string} initiativeId - The initiative ID
   * @param {Object} initiative - The updated initiative data
   * @returns {Promise} - The API response
   */
  updateInitiative(organizationId, initiativeId, initiative) {
    return api.put(`/organizations/${organizationId}/initiatives/${initiativeId}`, initiative);
  },

  /**
   * Delete an initiative
   * @param {string} organizationId - The organization ID
   * @param {string} initiativeId - The initiative ID
   * @returns {Promise} - The API response
   */
  deleteInitiative(organizationId, initiativeId) {
    return api.delete(`/organizations/${organizationId}/initiatives/${initiativeId}`);
  },

  /**
   * Batch update initiatives (create, update, delete)
   * @param {string} organizationId - The organization ID
   * @param {Array} initiatives - The initiatives data
   * @returns {Promise} - The API response
   */
  batchUpdateInitiatives(organizationId, initiatives) {
    return api.put(`/organizations/${organizationId}/initiatives/batch`, { initiatives });
  }
};
