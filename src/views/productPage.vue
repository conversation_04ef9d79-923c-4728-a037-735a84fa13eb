<template>
  <div>
    <!-- Toast component for error messages -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Toast component for success messages -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progress" />

    <div class="row mb-3">
      <!-- 1) Sites en Tunisie -->
      <div class="col-md-4">
        <SitesCard
          title="Sites en Tunisie"
          :sites="tunisieSites"
          @updateSites="handleSitesUpdate"
        />
      </div>

      <!-- 2) Pays d'implantations (first) -->
      <div class="col-md-4">
        <SitesCard
          title="Pays d'implantations sites étrangers"
          :sites="etrangerSites1"
          @updateSites="handleImportSitesUpdate"
        />
      </div>

      <!-- 3) Pays d'implantations (second) -->
      <div class="col-md-4">
        <SitesCard
          title="Pays d'exportations sites étrangers"
          :sites="etrangerSites2"
          @updateSites="handleExportSitesUpdate"
        />
      </div>
    </div>
    <div class="bg-white flex shadow p-12 relative">
      <button class="absolute top-4 right-4" @click="showModal = true">
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <div class="w-1/3 -mt-10 -ml-12 px-4">
        <ActivityChecklist
          :primaryActivities="products?.primaryActivities || []"
          :secondaryActivities="products?.secondaryActivities || []"
          @updateActivities="handleActivitiesUpdate"
        />
      </div>
      <div class="w-2/3 ml-12 px-4 border-l-2 border-gray-200">
        <ProductTable :products="productsData" />
      </div>
    </div>

    <EditProductModal
      v-if="showModal"
      title="Liste Produits"
      :products="productsData"
      @save="handleSave"
      @cancel="showModal = false"
      :showNgpField="true"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, computed } from "vue";
import productsService from "../Services/services/products";
import { useRoute } from "vue-router";
import EditRequest from "@/components/EditRequest.vue";

import ProductTable from "@/components/ProductTable.vue";
import ActivityChecklist from "@/components/ActivityChecklist.vue";
import EditProductModal from "@/components/EditProductModal.vue";
import SitesCard from "@/components/SitesCard.vue";
import Toast from "@/components/Toast.vue";
const route = useRoute();
const products = ref(null);
const showModal = ref(false);
const errorMessage = ref("");
const successMessage = ref("");

// Add props definition
const props = defineProps({
  organizationId: {
    type: String,
    required: false,
  },
});

// Use computed property for organizationId
const organizationId = computed(
  () => props.organizationId || route.params.organizationId
);

// Load initial data
async function loadProducts() {
  if (!organizationId.value) return;

  try {
    const response = await productsService.getProducts(organizationId.value);
    products.value = response.data;
  } catch (error) {
    console.error("Error loading products:", error);
  }
}

// Call loadProducts on component mount
onMounted(() => {
  loadProducts();
});
const tunisieSites = computed(() => {
  if (products.value && products.value.localSites) {
    return products.value.localSites.map((site) => ({
      name: site.name,
      capacity: site.capacity,
    }));
  }
  return [];
});
const etrangerSites1 = computed(() => {
  if (products.value && products.value.foreignImplantationSites) {
    return products.value.foreignImplantationSites.map((site) => ({
      name: site.name,
      capacity: site.capacity,
      flagUrl: site.flagUrl || "",
    }));
  }
  return [];
});
const progress = computed(() => {
  if (products.value && products.value.completion) {
    return products.value.completion;
  }
  return 0;
});

const etrangerSites2 = computed(() => {
  if (products.value && products.value.foreignExportationSites) {
    return products.value.foreignExportationSites.map((site) => ({
      name: site.name,
      capacity: site.capacity,
      flagUrl: site.flagUrl || "",
    }));
  }
  return [];
});
const productsData = computed(() => {
  if (products.value && products.value.products) {
    return products.value.products;
  }
  return [];
});
async function handleSitesUpdate(updatedSites) {
  if (!organizationId.value) return;

  const payload = {
    localSites: updatedSites,
  };

  try {
    const response = await productsService.updateProducts(
      organizationId.value,
      payload
    );
    products.value = response.data;
    successMessage.value = "Sites en Tunisie mis à jour avec succès";
  } catch (error) {
    console.error("Error updating local sites:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des sites en Tunisie";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}
async function handleImportSitesUpdate(updatedSites) {
  if (!organizationId.value) return;

  const payload = {
    foreignImplantationSites: updatedSites,
  };

  try {
    const response = await productsService.updateProducts(
      organizationId.value,
      payload
    );
    products.value = response.data;
    successMessage.value = "Pays d'implantations mis à jour avec succès";
  } catch (error) {
    console.error("Error updating importation sites:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des pays d'implantations";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}
async function handleExportSitesUpdate(updatedSites) {
  if (!organizationId.value) return;

  const payload = {
    foreignExportationSites: updatedSites,
  };

  try {
    const response = await productsService.updateProducts(
      organizationId.value,
      payload
    );
    products.value = response.data;
    successMessage.value = "Pays d'exportations mis à jour avec succès";
  } catch (error) {
    console.error("Error updating exportation sites:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des pays d'exportations";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}

async function handleSave(updatedProducts) {
  if (!organizationId.value) return;

  try {
    const payload = {
      products: updatedProducts,
    };

    const response = await productsService.updateProducts(
      organizationId.value,
      payload
    );
    products.value = response.data;
    successMessage.value = "Produits mis à jour avec succès";
    showModal.value = false;
  } catch (error) {
    console.error("Error updating products:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des produits";
    // Don't close the modal on error
    throw new Error(errorMessage.value);
  }
}
async function handleActivitiesUpdate({
  primaryActivities,
  secondaryActivities,
}) {
  if (!organizationId.value) return;

  try {
    const response = await productsService.updateProducts(
      organizationId.value,
      {
        primaryActivities,
        secondaryActivities,
      }
    );
    products.value = response.data;
    successMessage.value = "Activités mises à jour avec succès";
  } catch (error) {
    console.error("Error updating activities:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des activités";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}
</script>
