<template>
  <div>
    <!-- Toast component for error messages -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Toast component for success messages -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progress" />

    <div class="flex flex-wrap gap-4">
      <!-- Répartition du chiffre d'affaires Chart Section -->
      <div class="card mb-6 shadow rounded-xl p-4 w-[40%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">
            Répartition du chiffre d'affaires
          </h3>
          <!-- Edit button -->
          <button @click.prevent="openPopup('turnoverDistribution')">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <apexchart
          class="apex-charts"
          height="350"
          :series="turnoverDistributionChart.series"
          :options="turnoverDistributionChart.chartOptions"
        ></apexchart>
      </div>
      <!-- Types de clients et segments Section -->
      <div class="card mb-6 shadow rounded-xl p-4 w-[55%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">Types de clients et segments</h3>
          <!-- Edit button -->
          <button @click.prevent="openClientTypesModal">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <ClientTypesTable :data="clientTypesData" />
      </div>
    </div>

    <div class="flex flex-nowrap gap-4">
      <!-- Section Cards for Turnover Data -->
      <div class="mb-6 w-[60%]">
        <!-- Chiffre d'affaires en Euros (2024) Section Card -->
        <SectionCard
          title="Chiffre D'affire & Croissance"
          :items="turnoverItems"
          @update="handleTurnoverUpdate"
          :inlineLabels="true"
        />

        <!-- Autres Indicateurs financiers et economiques Section Card -->
        <SectionCard
          title="Autres Indicateurs financiers et economiques"
          :items="financialIndicatorsItems"
          @update="handleFinancialIndicatorsUpdate"
          :inlineLabels="true"
        />
      </div>
      <!-- Pourcentage de participation par pays Section -->
      <div class="card mb-6 shadow rounded-xl p-4 w-[40%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">
            Pourcentage de participation par pays
          </h3>
          <!-- Edit button -->
          <button @click.prevent="openCountryParticipationModal">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <CountryParticipationTable :data="countryParticipationData" />
      </div>
    </div>
    <!-- Edit Popup for Turnover Distribution -->
    <EditPopup
      v-if="showTurnoverDistributionPopup"
      :items="popupItems"
      :title="popupTitle"
      @save="handlePopupSave"
      @cancel="showTurnoverDistributionPopup = false"
    />

    <!-- Edit Modal for Client Types -->
    <EditClientTypesModal
      v-if="showClientTypesModal"
      title="Type de Clients et Segments"
      :clientTypes="clientTypesData"
      @save="handleClientTypesSave"
      @cancel="showClientTypesModal = false"
    />

    <!-- Edit Modal for Country Participation -->
    <EditCountryParticipationModal
      v-if="showCountryParticipationModal"
      title="Pourcentage de participation par pays"
      :countries="countryParticipationData"
      @save="handleCountryParticipationSave"
      @cancel="showCountryParticipationModal = false"
      @error="handleCountryParticipationError"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import Toast from "@/components/Toast.vue";
import EditPopup from "@/components/EditPopup.vue";
import ClientTypesTable from "@/components/ClientTypesTable.vue";
import CountryParticipationTable from "@/components/CountryParticipationTable.vue";
import EditClientTypesModal from "@/components/EditClientTypesModal.vue";
import EditCountryParticipationModal from "@/components/EditCountryParticipationModal.vue";
import SectionCard from "@/components/SectionCard.vue";
import revenueService from "../Services/services/revenue";
import EditRequest from "@/components/EditRequest.vue";

// Refs for data and state management
const route = useRoute();
const organization = ref(null);
const errorMessage = ref("");
const successMessage = ref("");
const showTurnoverDistributionPopup = ref(false);
const showClientTypesModal = ref(false);
const showCountryParticipationModal = ref(false);
const popupTitle = ref("");
const popupItems = ref([]);
const editingType = ref(null);

// Client types data - initialize with empty array instead of default data
const clientTypesData = ref([]);

// Turnover data structure based on OrganizationTurnoverRequest/Response
const turnoverData = ref({});

// Computed property for turnover items to display in the first section card
const turnoverItems = computed(() => {
  return [
    {
      label: "Chiffre d'affaires en Euros (2024)",
      value: turnoverData.value.revenue2024
        ? `${turnoverData.value.revenue2024} TND`
        : "N/A",
    },
    {
      label: "Evolution par rapport à 2023",
      value: turnoverData.value.hasGrowthComparedTo2023 ? "Oui" : "Non",
    },
    {
      label: "taux",
      value: turnoverData.value.growthRate
        ? `+${turnoverData.value.growthRate}%`
        : "N/A",
    },
  ];
});

// Computed property for financial indicators items to display in the second section card
const financialIndicatorsItems = computed(() => {
  return [
    {
      label: "Montant investi en R&D en 2023",
      value: turnoverData.value.rAndDInvestment2023
        ? `${turnoverData.value.rAndDInvestment2023} TND`
        : "N/A",
    },
    {
      label: "aides reçues",
      value: turnoverData.value.grantsReceived
        ? `${turnoverData.value.grantsReceived} TND`
        : "N/A",
    },
    {
      label: "Volume de production (Unités/an)",
      value: turnoverData.value.productionVolume
        ? `${turnoverData.value.productionVolume}`
        : "N/A",
    },
  ];
});

// Props definition for organization ID
const props = defineProps({
  organizationId: {
    type: String,
    required: false,
  },
});

// Use computed property for organizationId
const organizationId = computed(
  () => props.organizationId || route.params.organizationId
);
const progress = computed(() => {
  return organization.value ? organization.value.completion : 0;
});

// Colors for the chart
const colors = ["#C62027"];

// Turnover distribution data structure - keep static labels but set percentages to 0 by default
const turnoverDistributionData = ref([
  { type: "Automobile", count: 0 },
  { type: "aéronautique", count: 0 },
  { type: "l'export", count: 0 },
  { type: "R&D", count: 0 },
]);

// Country participation data structure - initialize with empty array instead of default data
const countryParticipationData = ref([]);

// Turnover distribution chart configuration
const turnoverDistributionChart = ref({
  series: [
    {
      data: [],
    },
  ],
  chartOptions: {
    chart: {
      height: 350,
      type: "bar",
    },
    colors: colors,
    plotOptions: {
      bar: {
        columnWidth: "1%",
        distributed: true,
      },
    },
    dataLabels: {
      enabled: false,
    },
    legend: {
      show: false,
    },
    xaxis: {
      categories: [],
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      labels: {
        formatter: function (val) {
          return val + "%";
        },
      },
    },
  },
});

// Load revenue data on component mount
onMounted(async () => {
  await loadRevenueData();
  // Initialize turnover distribution chart
  updateTurnoverDistributionChart();
});

// Function to load revenue data from the backend
async function loadRevenueData() {
  if (!organizationId.value) return;

  try {
    const response = await revenueService.getRevenues(organizationId.value);
    organization.value = response.data;

    // Load turnover data if available
    if (response.data && response.data.turnover) {
      console.log("Turnover data from backend:", response.data.turnover);
      turnoverData.value = response.data.turnover;
    }

    // Load turnover distribution data if available
    if (
      response.data &&
      response.data.turnoverDistribution &&
      response.data.turnoverDistribution.length > 0
    ) {
      console.log(
        "Turnover distribution from backend:",
        response.data.turnoverDistribution
      );

      // Update with data from backend
      turnoverDistributionData.value = response.data.turnoverDistribution;
    }

    // Load client types data if available
    if (
      response.data &&
      response.data.clientsTypes &&
      response.data.clientsTypes.length > 0
    ) {
      console.log("Client types from backend:", response.data.clientsTypes);

      // Update with data from backend
      clientTypesData.value = response.data.clientsTypes;
    }

    // Load country participation data if available
    if (
      response.data &&
      response.data.countriesParticipation &&
      response.data.countriesParticipation.length > 0
    ) {
      console.log(
        "Country participation from backend:",
        response.data.countriesParticipation
      );

      // Update with data from backend
      countryParticipationData.value = response.data.countriesParticipation.map(
        (item) => ({
          country: item.country,
          count: item.count,
          flagUrl: getCountryFlagUrl(item.country),
        })
      );
    }

    // Always update the chart, whether we got data from the backend or not
    updateTurnoverDistributionChart();
  } catch (error) {
    console.error("Failed to fetch revenue data:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors du chargement des données de revenus";
  }
}

// Function to update turnover distribution chart data
function updateTurnoverDistributionChart() {
  // Create a deep copy of the chart to trigger reactivity
  const updatedChart = JSON.parse(
    JSON.stringify(turnoverDistributionChart.value)
  );

  // Extract data for the chart
  const categories = turnoverDistributionData.value.map((item) => item.type);
  const data = turnoverDistributionData.value.map((item) => item.count);

  // Update series data
  updatedChart.series[0].data = data;

  // Update categories
  updatedChart.chartOptions.xaxis.categories = categories;

  // Update chart options to ensure no white lines
  updatedChart.chartOptions = {
    ...updatedChart.chartOptions,
    stroke: {
      width: 0,
      colors: ["transparent"],
    },
    plotOptions: {
      ...updatedChart.chartOptions.plotOptions,
      bar: {
        ...updatedChart.chartOptions.plotOptions.bar,
        columnWidth: "55%",
        borderRadius: 0,
      },
    },
  };

  // Update the chart reference to trigger a re-render
  turnoverDistributionChart.value = updatedChart;
}

// Function to handle turnover update from the first section card
async function handleTurnoverUpdate(updatedItems) {
  console.log("Handling turnover update:", updatedItems);

  try {
    // Validate revenue2024 (must be a number)
    if (
      !updatedItems[0].value ||
      updatedItems[0].value === "N/A" ||
      isNaN(parseInt(updatedItems[0].value.toString().replace(/\D/g, "")))
    ) {
      errorMessage.value =
        "Veuillez fournir un nombre valide pour le chiffre d'affaires";
      throw new Error("Invalid revenue value");
    }

    // Validate hasGrowthComparedTo2023 (must be "Oui" or "Non")
    if (updatedItems[1].value !== "Oui" && updatedItems[1].value !== "Non") {
      errorMessage.value =
        "Veuillez sélectionner 'Oui' ou 'Non' pour l'évolution par rapport à 2023";
      throw new Error("Invalid growth comparison value");
    }

    // Validate growthRate (must be a number if hasGrowthComparedTo2023 is "Oui")
    if (
      updatedItems[1].value === "Oui" &&
      (updatedItems[2].value === "N/A" ||
        isNaN(
          parseFloat(updatedItems[2].value.toString().replace(/[^0-9.]/g, ""))
        ))
    ) {
      errorMessage.value = "Veuillez fournir un taux de croissance valide";
      throw new Error("Invalid growth rate value");
    }

    // Parse the updated values
    const revenue2024 = parseInt(
      updatedItems[0].value.toString().replace(/\D/g, "")
    );
    const hasGrowthComparedTo2023 = updatedItems[1].value === "Oui";
    const growthRate = hasGrowthComparedTo2023
      ? parseFloat(updatedItems[2].value.toString().replace(/[^0-9.]/g, ""))
      : 0;

    // Update local data
    turnoverData.value = {
      ...turnoverData.value,
      revenue2024,
      hasGrowthComparedTo2023,
      growthRate,
    };

    // Prepare payload for backend
    const payload = {
      turnover: {
        revenue2024,
        hasGrowthComparedTo2023,
        growthRate,
        rAndDInvestment2023: turnoverData.value.rAndDInvestment2023,
        grantsReceived: turnoverData.value.grantsReceived,
        productionVolume: turnoverData.value.productionVolume,
      },
    };

    console.log("Sending turnover payload:", payload);

    // Send to backend
    const { data } = await revenueService.updateRevenues(
      organizationId.value,
      payload
    );
    organization.value = data;

    successMessage.value = "Chiffre d'affaires mis à jour avec succès.";
  } catch (err) {
    console.error("Error updating turnover data:", err);
    if (!errorMessage.value) {
      errorMessage.value =
        err.response?.data?.errorMessage ||
        "Erreur lors de la mise à jour des données de chiffre d'affaires.";
    }
    throw err; // Rethrow to keep popup open
  }
}

// Function to handle financial indicators update from the second section card
async function handleFinancialIndicatorsUpdate(updatedItems) {
  console.log("Handling financial indicators update:", updatedItems);

  try {
    // Validate rAndDInvestment2023 (must be a number)
    if (
      !updatedItems[0].value ||
      updatedItems[0].value === "N/A" ||
      isNaN(parseInt(updatedItems[0].value.toString().replace(/\D/g, "")))
    ) {
      errorMessage.value =
        "Veuillez fournir un montant valide pour l'investissement en R&D";
      throw new Error("Invalid R&D investment value");
    }

    // Validate grantsReceived (must be a number)
    if (
      !updatedItems[1].value ||
      updatedItems[1].value === "N/A" ||
      isNaN(parseInt(updatedItems[1].value.toString().replace(/\D/g, "")))
    ) {
      errorMessage.value =
        "Veuillez fournir un montant valide pour les aides reçues";
      throw new Error("Invalid grants received value");
    }

    // Validate productionVolume (must be a number)
    if (
      !updatedItems[2].value ||
      updatedItems[2].value === "N/A" ||
      isNaN(parseInt(updatedItems[2].value.toString().replace(/\D/g, "")))
    ) {
      errorMessage.value =
        "Veuillez fournir un nombre valide pour le volume de production";
      throw new Error("Invalid production volume value");
    }

    // Parse the updated values
    const rAndDInvestment2023 = parseInt(
      updatedItems[0].value.toString().replace(/\D/g, "")
    );
    const grantsReceived = parseInt(
      updatedItems[1].value.toString().replace(/\D/g, "")
    );
    const productionVolume = parseInt(
      updatedItems[2].value.toString().replace(/\D/g, "")
    );

    // Update local data
    turnoverData.value = {
      ...turnoverData.value,
      rAndDInvestment2023,
      grantsReceived,
      productionVolume,
    };

    // Prepare payload for backend
    const payload = {
      turnover: {
        revenue2024: turnoverData.value.revenue2024,
        hasGrowthComparedTo2023: turnoverData.value.hasGrowthComparedTo2023,
        growthRate: turnoverData.value.growthRate,
        rAndDInvestment2023,
        grantsReceived,
        productionVolume,
      },
    };

    console.log("Sending financial indicators payload:", payload);

    // Send to backend
    const { data } = await revenueService.updateRevenues(
      organizationId.value,
      payload
    );
    organization.value = data;

    successMessage.value = "Indicateurs financiers mis à jour avec succès.";
  } catch (err) {
    console.error("Error updating financial indicators data:", err);
    if (!errorMessage.value) {
      errorMessage.value =
        err.response?.data?.errorMessage ||
        "Erreur lors de la mise à jour des indicateurs financiers.";
    }
    throw err; // Rethrow to keep popup open
  }
}

// Function to open the edit popup
function openPopup(type) {
  console.log("openPopup called with type:", type);

  // Make sure organization exists
  if (!organization.value) {
    console.error("Organization is null or undefined");
    return;
  }

  // Handle turnover distribution popup
  if (type === "turnoverDistribution") {
    popupItems.value = turnoverDistributionData.value.map((item) => ({
      label: item.type,
      value: `${item.count}%`,
    }));

    popupTitle.value = "Répartition du chiffre d'affaires";
    editingType.value = type;
    showTurnoverDistributionPopup.value = true;
    return;
  }
}

// Function to open the client types modal
function openClientTypesModal() {
  console.log("Opening client types modal");
  showClientTypesModal.value = true;
}

// Function to open the country participation modal
function openCountryParticipationModal() {
  console.log("Opening country participation modal");
  showCountryParticipationModal.value = true;
}

// Function to handle country participation save
async function handleCountryParticipationSave(updatedCountries) {
  console.log("Saving country participation:", updatedCountries);

  try {
    // Prepare payload for backend
    const payload = {
      countriesParticipation: updatedCountries,
    };

    console.log("Sending country participation payload:", payload);

    // Send to backend
    const { data } = await revenueService.updateRevenues(
      organizationId.value,
      payload
    );
    organization.value = data;

    // Update local data
    countryParticipationData.value = updatedCountries.map((item) => ({
      ...item,
      // Add flag URLs based on country name
      flagUrl: getCountryFlagUrl(item.country),
    }));

    successMessage.value =
      "Pourcentage de participation par pays mis à jour avec succès.";
    showCountryParticipationModal.value = false;
  } catch (err) {
    console.error("Error updating country participation:", err);
    errorMessage.value =
      err.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des pourcentages de participation par pays.";
    // Keep the modal open on error
  }
}

// Helper function to get flag URL based on country name
function getCountryFlagUrl(country) {
  const countryToFlagCode = {
    Tunisie: "tn",
    France: "fr",
    "Etats Unis": "us",
    Allemagne: "de",
    Algerie: "dz",
    // Add more mappings as needed
  };

  return countryToFlagCode[country]
    ? `https://flagcdn.com/w40/${countryToFlagCode[country]}.png`
    : null;
}

// Function to handle country participation validation errors
function handleCountryParticipationError(message) {
  console.error("Country participation validation error:", message);
  errorMessage.value = message;
}

// Function to handle client types save
async function handleClientTypesSave(updatedClientTypes) {
  console.log("Saving client types:", updatedClientTypes);

  try {
    // Prepare payload for backend
    const payload = {
      clientsTypes: updatedClientTypes,
    };

    console.log("Sending client types payload:", payload);

    // Send to backend
    const { data } = await revenueService.updateRevenues(
      organizationId.value,
      payload
    );
    organization.value = data;

    // Update local data
    clientTypesData.value = updatedClientTypes;

    successMessage.value =
      "Types de clients et segments mis à jour avec succès.";
    showClientTypesModal.value = false;
  } catch (err) {
    console.error("Error updating client types:", err);
    errorMessage.value =
      err.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des types de clients et segments.";
    // Keep the modal open on error
  }
}

// Function to handle popup save
async function handlePopupSave(updated) {
  console.log("handlePopupSave called with type:", editingType.value);
  console.log("Updated items:", updated);

  // Handle turnover distribution data
  if (editingType.value === "turnoverDistribution") {
    try {
      // Parse the updated values
      const updatedDistribution = updated.map((item) => ({
        type: item.label,
        count: parseInt(item.value.toString().replace("%", "")) || 0,
      }));

      // Update local data
      turnoverDistributionData.value = updatedDistribution;

      // Update the chart with new data
      updateTurnoverDistributionChart();

      // Prepare payload for backend
      const payload = {
        turnoverDistribution: updatedDistribution,
      };

      console.log("Sending turnover distribution payload:", payload);

      // Send to backend
      const { data } = await revenueService.updateRevenues(
        organizationId.value,
        payload
      );
      organization.value = data;

      successMessage.value =
        "Répartition du chiffre d'affaires mise à jour avec succès.";
      showTurnoverDistributionPopup.value = false;
    } catch (err) {
      console.error("Error updating turnover distribution data:", err);
      errorMessage.value =
        err.response?.data?.errorMessage ||
        "Erreur lors de la mise à jour des données de répartition du chiffre d'affaires.";
      // Keep the popup open on error
    }
    return;
  }
}
</script>
