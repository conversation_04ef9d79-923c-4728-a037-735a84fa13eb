<template>
  <div>
    <!-- Toast component for error messages -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Toast component for success messages -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progress" />

    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <div v-else>
      <!-- Opportunities Table -->
      <OpportunityTable :opportunities="opportunitiesData" @edit="handleEdit" />
    </div>

    <!-- Edit Opportunity Modal -->
    <EditOpportunityModal
      v-if="showEditModal"
      :opportunities="opportunitiesData"
      @save="handleSave"
      @cancel="showEditModal = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import Toast from "@/components/Toast.vue";
import OpportunityTable from "@/components/OpportunityTable.vue";
import EditOpportunityModal from "@/components/EditOpportunityModal.vue";
import opportunityService from "../Services/services/opportunity";
import EditRequest from "@/components/EditRequest.vue";

import {
  toBackendPayload,
  fromBackendResponse,
} from "@/utils/opportunityMapper";

const route = useRoute();
const organizationId = ref(route.params.organizationId);
const opportunitiesData = ref([]);
const errorMessage = ref("");
const successMessage = ref("");
const isLoading = ref(true);
const showEditModal = ref(false);
const progress = ref(0);

// Load opportunities data
async function loadOpportunitiesData() {
  isLoading.value = true;
  try {
    // Make API call to get opportunities data
    const response = await opportunityService.getOpportunities(
      organizationId.value
    );

    // Convert backend response to frontend format
    opportunitiesData.value = fromBackendResponse(response.data);

    progress.value = response.data.completion || 0;
    console.log("Loaded opportunities data:", opportunitiesData.value);
  } catch (error) {
    console.error("Error loading opportunities data:", error);

    // Use the error message from the response if available
    if (
      error.response &&
      error.response.data &&
      error.response.data.errorMessage
    ) {
      errorMessage.value = error.response.data.errorMessage;
    } else {
      errorMessage.value =
        "Erreur lors du chargement des données d'opportunités";
    }

    // No longer using mock data in development mode
    console.warn("API failed to load opportunities data");
  } finally {
    isLoading.value = false;
  }
}

// Handle edit button click
function handleEdit() {
  showEditModal.value = true;
}

// Handle save from modal
async function handleSave(updatedOpportunities) {
  try {
    // Check if we have any opportunities to save
    if (!updatedOpportunities || updatedOpportunities.length === 0) {
      errorMessage.value = "Aucune opportunité à sauvegarder";
      return;
    }

    // Convert frontend data to backend format
    const payload = toBackendPayload(updatedOpportunities);

    // Make API call to update opportunities
    await opportunityService.updateOpportunities(organizationId.value, payload);

    // Reload data from the server to ensure we have the latest version
    await loadOpportunitiesData();

    // Show success message
    successMessage.value = "Opportunités mises à jour avec succès";

    // Close the modal
    showEditModal.value = false;
  } catch (error) {
    console.error("Error saving opportunities:", error);

    // Use the error message from the response if available
    if (
      error.response &&
      error.response.data &&
      error.response.data.errorMessage
    ) {
      errorMessage.value = error.response.data.errorMessage;
    } else {
      errorMessage.value = "Erreur lors de la sauvegarde des opportunités";
    }

    // Modal stays open when there's an error
  }
}

// Call loadOpportunitiesData on component mount
onMounted(() => {
  loadOpportunitiesData();
});
</script>
