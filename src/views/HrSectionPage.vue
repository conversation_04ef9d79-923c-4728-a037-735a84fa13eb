<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { useRoute } from "vue-router";
import HrTable from "@/components/HrTable.vue";
import Toast from "@/components/Toast.vue";
import EditContractsModal from "@/components/EditContractsModal.vue";
import SectionCard from "@/components/SectionCard.vue";
import hrService from "../Services/services/hr";
import EditPopup from "@/components/EditPopup.vue";
import EditRequest from "@/components/EditRequest.vue";

// Refs for data and state management
const route = useRoute();
const organization = ref(null);
const errorMessage = ref("");
const successMessage = ref("");
const showEditModal = ref(false);
const showKpiPopup = ref(false);
const showAgePopup = ref(false);
const popupTitle = ref("");

// Static contract types
const contractTypes = [
  "Contrats à durée indéterminée (CDI)",
  "Contrats à durée déterminée (CDD)",
  "Programmes SIVP / Karama / Stages",
  "Autres Contrats",
];

const colors = ["#C62027", "#3AA422"];

// Revenue growth data structure - keep static labels but set percentages to 0 by default
const revenueGrowthData = ref({
  cadres: { men: 0, women: 0 },
  techniciens: { men: 0, women: 0 },
  ouvriers: { men: 0, women: 0 },
  production: { men: 0, women: 0 },
  commercial: { men: 0, women: 0 },
  administratif: { men: 0, women: 0 },
});

// Revenue growth chart configuration
const revenueGrowthChart = ref({
  series: [
    {
      name: "Men",
      data: [],
    },
    {
      name: "Women",
      data: [],
    },
  ],
  chartOptions: {
    chart: {
      type: "bar",
      height: 350,
      stacked: true,
    },
    colors: colors,
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "55%",
        borderRadius: 0,
        colors: {
          backgroundBarColors: [],
          backgroundBarOpacity: 1,
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: false,
      width: 0,
      colors: ["transparent"],
    },
    xaxis: {
      categories: [
        "Cadres",
        "techniciens",
        "Ouvriers",
        "Production",
        "Commercial",
        "Administratif",
      ],
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      max: 100,
      labels: {
        formatter: function (val) {
          return val + "%";
        },
      },
    },
    legend: {
      position: "top",
      offsetY: 0,
      markers: {
        radius: 12, // Makes the legend markers rounded
        width: 12,
        height: 12,
      },
    },
    fill: {
      opacity: 1,
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + "%";
        },
      },
    },
  },
});
// Function to update revenue growth chart data
function updateRevenueGrowthChart() {
  // Create a deep copy of the chart to trigger reactivity
  const updatedChart = JSON.parse(JSON.stringify(revenueGrowthChart.value));

  updatedChart.series = [
    {
      name: "Hommes",
      data: [
        revenueGrowthData.value.cadres.men,
        revenueGrowthData.value.techniciens.men,
        revenueGrowthData.value.ouvriers.men,
        revenueGrowthData.value.production.men,
        revenueGrowthData.value.commercial.men,
        revenueGrowthData.value.administratif.men,
      ],
    },
    {
      name: "Femmes",
      data: [
        revenueGrowthData.value.cadres.women,
        revenueGrowthData.value.techniciens.women,
        revenueGrowthData.value.ouvriers.women,
        revenueGrowthData.value.production.women,
        revenueGrowthData.value.commercial.women,
        revenueGrowthData.value.administratif.women,
      ],
    },
  ];

  // Update chart options to ensure no white lines and rounded legend markers
  updatedChart.chartOptions = {
    ...updatedChart.chartOptions,
    stroke: {
      width: 0,
      colors: ["transparent"],
    },
    plotOptions: {
      ...updatedChart.chartOptions.plotOptions,
      bar: {
        ...updatedChart.chartOptions.plotOptions.bar,
        columnWidth: "55%",
        borderRadius: 0,
      },
    },
    legend: {
      position: "top",
      offsetY: 0,
      markers: {
        radius: 12, // Makes the legend markers rounded
        width: 12,
        height: 12,
      },
    },
  };

  // Update the chart reference to trigger a re-render
  revenueGrowthChart.value = updatedChart;
}

const showRevenueGrowthPopup = ref(false);

const distributedColumnchart = {
  series: [
    {
      data: [76, 24],
    },
  ],
  chartOptions: {
    chart: {
      height: 350,
      type: "bar",
      events: {
        click: function () {
          // console.log(chart, w, e)
        },
      },
    },
    colors: colors,
    plotOptions: {
      bar: {
        columnWidth: "45%",
        distributed: true,
      },
    },
    dataLabels: {
      enabled: false,
    },
    legend: {
      show: false,
    },
    xaxis: {
      categories: [["Hommes"], ["Femmes"]],
      labels: {
        style: {
          colors: colors,
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      max: 100, // Set maximum value to 100%
      labels: {
        formatter: function (val) {
          return val + "%";
        },
      },
    },
  },
};

const distributedColumnchartRef = ref(
  JSON.parse(JSON.stringify(distributedColumnchart))
);
// 3) Expose deep-cloned options & series as computed
const distributedColumnchartOptions = computed(() =>
  JSON.parse(JSON.stringify(distributedColumnchartRef.value.chartOptions))
);
const distributedColumnchartSeries = computed(() =>
  JSON.parse(JSON.stringify(distributedColumnchartRef.value.series))
);

// Props definition for organization ID
const props = defineProps({
  organizationId: {
    type: String,
    required: false,
  },
});

// Use computed property for organizationId
const organizationId = computed(
  () => props.organizationId || route.params.organizationId
);

// Load HR data on component mount
onMounted(async () => {
  await loadHrData();
  // Initialize revenue growth chart
  updateRevenueGrowthChart();
});
const progress = computed(() => {
  return organization.value ? organization.value.completion : 0;
});
const directKpi = computed(() => {
  // First check if we have directEmployees in the response
  if (organization.value?.directEmployees) {
    return {
      men: organization.value.directEmployees.men || 0,
      women: organization.value.directEmployees.women || 0,
      type: "direct",
    };
  }

  // Fallback to employeesKpis if directEmployees is not available
  return (
    organization.value?.employeesKpis?.find((k) => k.type === "direct") || {
      men: 0,
      women: 0,
      type: "direct",
    }
  );
});

const indirectKpi = computed(() => {
  // First check if we have indirectEmployees in the response
  if (organization.value?.indirectEmployees) {
    return {
      men: organization.value.indirectEmployees.men || 0,
      women: organization.value.indirectEmployees.women || 0,
      type: "indirect",
    };
  }

  // Fallback to employeesKpis if indirectEmployees is not available
  return (
    organization.value?.employeesKpis?.find((k) => k.type === "indirect") || {
      men: 0,
      women: 0,
      type: "indirect",
    }
  );
});
// Function to load HR data from the backend
async function loadHrData() {
  if (!organizationId.value) return;

  try {
    const response = await hrService.getHr(organizationId.value);
    organization.value = response.data;

    // Log the loaded data
    if (response.data && response.data.contracts) {
      console.log("HR data loaded:", response.data);
    }

    // Load revenue growth data if available from revenueKpis
    if (
      response.data &&
      response.data.revenueKpis &&
      response.data.revenueKpis.length > 0
    ) {
      console.log("Revenue KPIs from backend:", response.data.revenueKpis);

      // Reset the data structure with default values
      revenueGrowthData.value = {
        cadres: { men: 0, women: 0 },
        techniciens: { men: 0, women: 0 },
        ouvriers: { men: 0, women: 0 },
        production: { men: 0, women: 0 },
        commercial: { men: 0, women: 0 },
        administratif: { men: 0, women: 0 },
      };

      // Update with data from backend
      response.data.revenueKpis.forEach((kpi) => {
        if (revenueGrowthData.value[kpi.type]) {
          revenueGrowthData.value[kpi.type] = {
            men: kpi.men,
            women: kpi.women,
          };
        }
      });
    }

    // Always update the chart, whether we got data from the backend or not
    updateRevenueGrowthChart();
  } catch (error) {
    console.error("Failed to fetch HR data:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors du chargement des données RH";
  }
}

// Computed property to transform contract data for the table
const hrData = computed(() => {
  if (!organization.value) return [];

  // Map backend types to display types

  // Create a reverse mapping for saving data
  const reverseTypeMapping = {
    [contractTypes[0]]: "cdi",
    [contractTypes[1]]: "cdd",
    [contractTypes[2]]: "internship",
    [contractTypes[3]]: "other",
  };

  // Initialize with all contract types
  return contractTypes.map((type) => {
    // Find the corresponding contract in the data
    const backendType = reverseTypeMapping[type];
    const contract = organization.value.contracts?.find(
      (c) => c.type === backendType
    );

    if (contract) {
      return {
        type: type,
        femmes: `${calculatePercentage(
          contract.women,
          contract.women + contract.men
        )}%`,
        hommes: `${calculatePercentage(
          contract.men,
          contract.women + contract.men
        )}%`,
        // Store raw values and backend type for editing
        _raw: {
          men: contract.men,
          women: contract.women,
          backendType: contract.type,
        },
      };
    } else {
      // Return default values if contract doesn't exist
      return {
        type: type,
        femmes: "0%",
        hommes: "0%",
        _raw: {
          men: 0,
          women: 0,
          backendType: reverseTypeMapping[type],
        },
      };
    }
  });
});

// Helper function to calculate percentage
function calculatePercentage(value, total) {
  if (total === 0) return 0;
  return Math.round((value / total) * 100);
}

// Computed property to transform formation data for the SectionCard
const formationItems = computed(() => {
  if (!organization.value || !organization.value.formationKpi) return [];

  const formation = organization.value.formationKpi;

  return [
    {
      label: "Nombre heures de formation par an:",
      value: [
        "Hommes: " + (formation.menHours || 0) + "H",
        "Femmes: " + (formation.womenHours || 0) + "H",
      ],
    },
    {
      label: "Formations principales:",
      value: formation.mainFormation || "",
    },
    {
      label: "Lieu Formations:",
      value: formation.location || "",
    },
    {
      label: "Type de Formation:",
      value: formation.type || "",
    },
    {
      label: "Employés formés annuellement:",
      value: formation.employeesTrained || "",
    },
    {
      label: "Part du CA investi en formation:",
      value: formation.revenueInvestment || "",
    },
  ];
});
/* const directs_men = organization.value.employeeskpis.men;
const directs_women = organization.value.employeeskpis.women;
const undirects_men=organization.value.employeeskpis.men;
const undirects_women=organization.value.employeeskpis.women;
 */
const editingType = ref(null); // this will hold "direct", "indirect", or "age" as a string

// Computed property for age data
const ageData = computed(() => {
  if (!organization.value || !organization.value.ageKpis) {
    return {
      count18_24: 0,
      count25_30: 0,
      count31_36: 0,
      count37Plus: 0,
    };
  }

  return organization.value.ageKpis;
});

// Function to create the age chart
function makeAgeChart(data) {
  // Ensure we have valid numbers for the chart
  const count18_24 = parseInt(data.count18_24) || 0;
  const count25_30 = parseInt(data.count25_30) || 0;
  const count31_36 = parseInt(data.count31_36) || 0;
  const count37Plus = parseInt(data.count37Plus) || 0;

  console.log("Making age chart with data:", {
    count18_24,
    count25_30,
    count31_36,
    count37Plus,
  });

  return {
    series: [{ data: [count18_24, count25_30, count31_36, count37Plus] }],
    chartOptions: {
      chart: { height: 350, type: "bar" },
      colors: ["#C62027", "#C62027", "#C62027", "#C62027"],
      plotOptions: { bar: { columnWidth: "45%", distributed: true } },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val + "%";
        },
      },
      legend: { show: false },
      xaxis: {
        categories: ["18-24", "25-30", "31-36", "+37"],
        labels: { style: { fontSize: "12px" } },
      },
      yaxis: {
        max: 100, // Set maximum value to 100%
        labels: {
          formatter: function (val) {
            return val + "%";
          },
        },
      },
    },
  };
}

// Function to handle contract updates
async function handleContractsUpdate(updatedContracts) {
  if (!organizationId.value) return;

  try {
    // Transform the updated contracts to the format expected by the backend
    const payload = {
      contracts: updatedContracts.map((contract) => ({
        // Use the backend type stored in _raw
        type: contract._raw.backendType,
        men: parseInt(contract._raw.men) || 0,
        women: parseInt(contract._raw.women) || 0,
      })),
    };

    console.log("Sending contract update payload:", payload);
    const response = await hrService.updateHr(organizationId.value, payload);
    organization.value = response.data;

    successMessage.value = "Données des contrats mises à jour avec succès";
    showEditModal.value = false;
  } catch (error) {
    console.error("Error updating contracts:", error);

    // Check if we have validation errors in the response
    if (error.response?.data?.data) {
      // Extract validation messages from the data object
      const validationErrors = [];
      const errorData = error.response.data.data;

      // Loop through the data object to find validation errors
      Object.keys(errorData).forEach((key) => {
        if (key.includes("validateTotal")) {
          validationErrors.push(errorData[key]);
        }
      });

      // If we found validation errors, display them
      if (validationErrors.length > 0) {
        errorMessage.value = validationErrors.join("\n");
      } else {
        // Fallback to the general error message
        errorMessage.value =
          error.response?.data?.errorMessage ||
          "Erreur lors de la mise à jour des contrats";
      }
    } else {
      // Fallback to the general error message
      errorMessage.value =
        error.response?.data?.errorMessage ||
        "Erreur lors de la mise à jour des contrats";
    }

    // Don't close the modal on error
    throw new Error(errorMessage.value);
  }
}

// Function to handle formation data updates
async function handleFormationUpdate(updatedItems) {
  if (!organizationId.value) return;

  try {
    // Transform the updated items back to the format expected by the API
    const formationData = {};

    updatedItems.forEach((item) => {
      if (item.label.includes("Nombre heures de formation par an")) {
        // Handle array of values for men and women hours
        if (Array.isArray(item.value)) {
          // Extract hours from strings like "Hommes: 232H" and "Femmes: 322H"
          item.value.forEach((val) => {
            if (val.includes("Hommes:")) {
              formationData.menHours =
                parseInt(val.replace(/[^0-9]/g, "")) || 0;
            } else if (val.includes("Femmes:")) {
              formationData.womenHours =
                parseInt(val.replace(/[^0-9]/g, "")) || 0;
            }
          });
        }
      } else if (item.label.includes("Formations principales")) {
        formationData.mainFormation = item.value;
      } else if (item.label.includes("Lieu Formations")) {
        formationData.location = item.value;
      } else if (item.label.includes("Type de Formation")) {
        formationData.type = item.value;
      } else if (item.label.includes("Employés formés annuellement")) {
        formationData.employeesTrained = item.value;
      } else if (item.label.includes("Part du CA investi en formation")) {
        formationData.revenueInvestment = item.value;
      }
    });

    const payload = {
      formationKpi: formationData,
    };

    const response = await hrService.updateHr(organizationId.value, payload);
    organization.value = response.data;
    successMessage.value = "Données de formation mises à jour avec succès";
  } catch (error) {
    console.error("Error updating formation data:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des données de formation";
    throw new Error(errorMessage.value);
  }
}
function makeChart(kpi) {
  // Ensure we have valid numbers for the chart
  const men = parseInt(kpi.men) || 0;
  const women = parseInt(kpi.women) || 0;

  console.log("Making chart with data:", { men, women });

  return {
    series: [{ data: [men, women] }],
    chartOptions: {
      chart: { height: 350, type: "bar" },
      colors,
      plotOptions: { bar: { columnWidth: "45%", distributed: true } },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val + "%";
        },
      },
      legend: { show: false },
      xaxis: {
        categories: ["Hommes", "Femmes"],
        labels: { style: { colors, fontSize: "12px" } },
      },
      yaxis: {
        max: 100, // Set maximum value to 100%
        labels: {
          formatter: function (val) {
            return val + "%";
          },
        },
      },
    },
  };
}

const directChart = ref(makeChart(directKpi.value));
const indirectChart = ref(makeChart(indirectKpi.value));
const ageChart = ref(makeAgeChart(ageData.value));

// Watch for KPI changes to refresh charts
watch(directKpi, (v) => (directChart.value = makeChart(v)));
watch(indirectKpi, (v) => (indirectChart.value = makeChart(v)));
watch(ageData, (v) => (ageChart.value = makeAgeChart(v)));
const popupItems = ref([]);
function openPopup(type) {
  console.log("openPopup called with type:", type);

  // Make sure organization exists
  if (!organization.value) {
    console.error("Organization is null or undefined");
    return;
  }

  // Handle revenue growth popup
  if (type === "revenueGrowth") {
    const categories = [
      "Cadres",
      "techniciens",
      "Ouvriers",
      "Production",
      "Commercial",
      "Administratif",
    ];

    popupItems.value = [];

    categories.forEach((category) => {
      const key = category.toLowerCase();
      const dataKey = key === "techniciens" ? "techniciens" : key;

      popupItems.value.push({
        label: category,
        value: [
          `Men: ${revenueGrowthData.value[dataKey].men}%`,
          `Women: ${revenueGrowthData.value[dataKey].women}%`,
        ],
      });
    });

    popupTitle.value = "Croissance des revenus au fil des ans";
    editingType.value = type;
    showRevenueGrowthPopup.value = true;
    return;
  }

  // Handle age popup separately
  if (type === "age") {
    const ageKpiData = organization.value.ageKpis || {
      count18_24: 0,
      count25_30: 0,
      count31_36: 0,
      count37Plus: 0,
    };

    console.log("Found age KPI data:", ageKpiData);

    popupItems.value = [
      {
        label: "18-24",
        value: `${ageKpiData.count18_24}%`,
      },
      {
        label: "25-30",
        value: `${ageKpiData.count25_30}%`,
      },
      {
        label: "31-36",
        value: `${ageKpiData.count31_36}%`,
      },
      {
        label: "37",
        value: `${ageKpiData.count37Plus}%`,
      },
    ];

    popupTitle.value = "Tableau des âges";
    editingType.value = type;

    console.log("About to show age popup, data:", {
      popupItems: popupItems.value,
      editingType: editingType.value,
      popupTitle: popupTitle.value,
    });

    showAgePopup.value = true;
    return;
  }

  // Handle employee KPI popups (direct/indirect)
  let kpiData = { men: 0, women: 0 };

  // Get the KPI data based on the type
  if (type === "direct") {
    if (organization.value.directEmployees) {
      kpiData = {
        men: organization.value.directEmployees.men || 0,
        women: organization.value.directEmployees.women || 0,
      };
    } else if (organization.value.employeesKpis) {
      const kpi = organization.value.employeesKpis.find(
        (k) => k.type === "direct"
      );
      if (kpi) {
        kpiData = { men: kpi.men, women: kpi.women };
      }
    }
  } else if (type === "indirect") {
    if (organization.value.indirectEmployees) {
      kpiData = {
        men: organization.value.indirectEmployees.men || 0,
        women: organization.value.indirectEmployees.women || 0,
      };
    } else if (organization.value.employeesKpis) {
      const kpi = organization.value.employeesKpis.find(
        (k) => k.type === "indirect"
      );
      if (kpi) {
        kpiData = { men: kpi.men, women: kpi.women };
      }
    }
  }

  console.log(`Found ${type} KPI data:`, kpiData);

  popupItems.value = [
    {
      label: "Hommes",
      value: `${kpiData.men}%`,
    },
    {
      label: "Femmes",
      value: `${kpiData.women}%`,
    },
  ];

  popupTitle.value = `Employés ${type === "direct" ? "directs" : "indirects"}`;
  editingType.value = type;

  console.log("About to show popup, data:", {
    popupItems: popupItems.value,
    editingType: editingType.value,
    popupTitle: popupTitle.value,
  });

  showKpiPopup.value = true;
  console.log("showKpiPopup set to:", showKpiPopup.value);
}
async function handlePopupSave(updated) {
  console.log("handlePopupSave called with:", updated);

  const type = editingType.value;
  if (!type) return;

  // Handle revenue growth data
  if (type === "revenueGrowth") {
    try {
      // Parse the updated values
      updated.forEach((item) => {
        const category = item.label.toLowerCase();
        const dataKey = category === "techniciens" ? "techniciens" : category;

        // Extract men and women percentages from the array values
        if (Array.isArray(item.value)) {
          item.value.forEach((val) => {
            if (val.toLowerCase().startsWith("men:")) {
              revenueGrowthData.value[dataKey].men =
                parseInt(val.replace(/men:/i, "").replace("%", "").trim()) || 0;
            } else if (val.toLowerCase().startsWith("women:")) {
              revenueGrowthData.value[dataKey].women =
                parseInt(
                  val
                    .replace(/women:/i, "")
                    .replace("%", "")
                    .trim()
                ) || 0;
            }
          });
        }
      });

      // Update the chart with new data
      updateRevenueGrowthChart();

      // Prepare payload for backend using the revenues field
      // Transform our data structure to match the backend's expected format
      const payload = {
        revenues: [
          {
            type: "cadres",
            men: revenueGrowthData.value.cadres.men,
            women: revenueGrowthData.value.cadres.women,
          },
          {
            type: "techniciens",
            men: revenueGrowthData.value.techniciens.men,
            women: revenueGrowthData.value.techniciens.women,
          },
          {
            type: "ouvriers",
            men: revenueGrowthData.value.ouvriers.men,
            women: revenueGrowthData.value.ouvriers.women,
          },
          {
            type: "production",
            men: revenueGrowthData.value.production.men,
            women: revenueGrowthData.value.production.women,
          },
          {
            type: "commercial",
            men: revenueGrowthData.value.commercial.men,
            women: revenueGrowthData.value.commercial.women,
          },
          {
            type: "administratif",
            men: revenueGrowthData.value.administratif.men,
            women: revenueGrowthData.value.administratif.women,
          },
        ],
      };

      console.log("Sending revenue growth payload:", payload);

      // Send to backend
      const { data } = await hrService.updateHr(organizationId.value, payload);
      organization.value = data;

      successMessage.value = "Croissance des revenus mise à jour avec succès.";
      showRevenueGrowthPopup.value = false;
    } catch (err) {
      console.error("Error updating revenue growth data:", err);
      errorMessage.value =
        err.response?.data?.errorMessage ||
        "Erreur lors de la mise à jour des données de croissance des revenus.";
      // Keep the popup open on error
    }
    return;
  }

  // Handle age data separately
  if (type === "age") {
    // Parse values, ensuring we're getting numbers
    const count18_24 = parseFloat(
      updated
        .find((i) => i.label === "18-24")
        ?.value?.toString()
        .replace("%", "") || "0"
    );
    const count25_30 = parseFloat(
      updated
        .find((i) => i.label === "25-30")
        ?.value?.toString()
        .replace("%", "") || "0"
    );
    const count31_36 = parseFloat(
      updated
        .find((i) => i.label === "31-36")
        ?.value?.toString()
        .replace("%", "") || "0"
    );
    const count37Plus = parseFloat(
      updated
        .find((i) => i.label === "37")
        ?.value?.toString()
        .replace("%", "") || "0"
    );

    console.log("Parsed age values:", {
      count18_24,
      count25_30,
      count31_36,
      count37Plus,
    });

    const payload = {
      ageKpis: {
        count18_24,
        count25_30,
        count31_36,
        count37Plus,
      },
    };

    console.log("Sending age payload:", payload);

    try {
      const { data } = await hrService.updateHr(organizationId.value, payload);
      organization.value = data;
      successMessage.value = "Tableau des âges mis à jour avec succès.";
      // Only close the popup after successful update
      showAgePopup.value = false;
    } catch (err) {
      console.error("Error updating age data:", err);

      // Check if we have validation errors in the response
      if (err.response?.data?.data) {
        // Extract validation messages from the data object
        const validationErrors = [];
        const errorData = err.response.data.data;

        // Loop through the data object to find validation errors
        Object.keys(errorData).forEach((key) => {
          if (key.includes("validateTotal")) {
            validationErrors.push(errorData[key]);
          }
        });

        // If we found validation errors, display them
        if (validationErrors.length > 0) {
          errorMessage.value = validationErrors.join("\n");
        } else {
          // Fallback to the general error message
          errorMessage.value =
            err.response?.data?.errorMessage ||
            "Erreur lors de la mise à jour du tableau des âges";
        }
      } else {
        // Fallback to the general error message
        errorMessage.value =
          err.response?.data?.errorMessage ||
          "Erreur lors de la mise à jour du tableau des âges";
      }

      // Keep the popup open on error
    }
    return;
  }

  // Handle employee KPI data (direct/indirect)
  // Parse values, ensuring we're getting numbers
  const men = parseFloat(
    updated
      .find((i) => i.label === "Hommes")
      ?.value?.toString()
      .replace("%", "") || "0"
  );
  const women = parseFloat(
    updated
      .find((i) => i.label === "Femmes")
      ?.value?.toString()
      .replace("%", "") || "0"
  );

  console.log("Parsed values:", { men, women, type });

  // Based on the backend structure, we need to use directEmployees or indirectEmployees
  // instead of sending the employeesKpis array directly
  let payload = {};

  if (type === "direct") {
    payload = {
      directEmployees: {
        men: men,
        women: women,
      },
    };
  } else if (type === "indirect") {
    payload = {
      indirectEmployees: {
        men: men,
        women: women,
      },
    };
  }

  console.log("Sending payload:", payload);

  try {
    const { data } = await hrService.updateHr(organizationId.value, payload);
    organization.value = data;
    successMessage.value = "KPI mis à jour avec succès.";
    // Only close the popup after successful update
    showKpiPopup.value = false;
  } catch (err) {
    console.error("Error updating KPI:", err);
    errorMessage.value =
      err.response?.data?.errorMessage || "Erreur mise à jour KPI.";
    // Keep the popup open on error
  }
}
</script>

<template>
  <div>
    <!-- Toast component for error messages -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Toast component for success messages -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progress" />

    <div class="flex flex-wrap gap-10">
      <div class="card mb-6 shadow rounded-xl p-4 w-[23%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">Employés directs</h3>
          <!-- Edit button -->
          <button @click.prevent="openPopup('direct')">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <apexchart
          class="apex-charts"
          height="350"
          :series="directChart.series"
          :options="directChart.chartOptions"
        />
      </div>
      <div class="card mb-6 shadow rounded-xl p-4 w-[23%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">Employés indirects</h3>
          <!-- Edit button -->
          <button @click.prevent="openPopup('indirect')">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <apexchart
          class="apex-charts"
          height="350"
          :series="indirectChart.series"
          :options="indirectChart.chartOptions"
        ></apexchart>
      </div>

      <!-- Contracts Section -->
      <div class="card mb-6 shadow rounded-xl p-4 w-[48%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">Type de contrat par genre</h3>
          <!-- Edit button -->
          <button @click="showEditModal = true">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <HrTable :data="hrData" />
      </div>
    </div>
    <div class="flex flex-wrap gap-4">
      <!-- Revenue Growth Chart Section -->
      <div class="card mb-6 shadow rounded-xl p-4 w-[40%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">
            Croissance des revenus au fil des ans
          </h3>
          <!-- Edit button -->
          <button @click.prevent="openPopup('revenueGrowth')">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <apexchart
          class="apex-charts"
          height="350"
          :series="revenueGrowthChart.series"
          :options="revenueGrowthChart.chartOptions"
        ></apexchart>
      </div>
      <!-- Age Chart Section -->
      <div class="card mb-6 shadow rounded-xl p-4 w-[55%]">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-semibold !text-lg">Tableau des âges</h3>
          <!-- Edit button -->
          <button @click.prevent="openPopup('age')">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <apexchart
          class="apex-charts"
          height="350"
          :series="ageChart.series"
          :options="ageChart.chartOptions"
        ></apexchart>
      </div>
    </div>
    <SectionCard
      v-if="organization && organization.formationKpi"
      title="Données de formation"
      :items="formationItems"
      @update="handleFormationUpdate"
      inline-labels="true"
    />
    <!-- Edit Contracts Modal -->
    <EditContractsModal
      v-if="showEditModal"
      title="Type de Contrat par Genre"
      :contracts="hrData"
      :contract-types="contractTypes"
      @save="handleContractsUpdate"
      @cancel="showEditModal = false"
    />
  </div>
  <EditPopup
    v-if="showKpiPopup"
    :items="popupItems"
    :title="popupTitle"
    @save="handlePopupSave"
    @cancel="showKpiPopup = false"
  />
  <EditPopup
    v-if="showAgePopup"
    :items="popupItems"
    :title="popupTitle"
    @save="handlePopupSave"
    @cancel="showAgePopup = false"
  />
  <EditPopup
    v-if="showRevenueGrowthPopup"
    :items="popupItems"
    :title="popupTitle"
    @save="handlePopupSave"
    @cancel="showRevenueGrowthPopup = false"
  />
</template>
