<template class="!bg-[#EEF0F4]">
  <!-- Outer flex container to reserve 10% for future menu -->
  <div class="flex min-h-screen">
    <!-- 10% for menu (empty placeholder for now) -->
    <!-- <NavBar /> -->
    <aside class="w-[10%]">
      <SideMenu :menuItems="customMenuItems" />
    </aside>

    <main class="w-[90%] px-4 py-6">
      <CompanyHeader
        :imageUrl="imageUrl"
        :companyName="companyName"
        :country="country"
        :foundationYear="foundationYear"
        :website="website"
        :progressValue="progressValue"
        @tab-changed="handleTabChange"
      />

      <EditRequest :progressValue="72" />
      <div v-if="activeTab === 'General'">
        <SectionCard
          v-if="organization"
          title="À propos de la société"
          :items="generalInfoItems"
          :inlineLabels="true"
          @update="handleGeneralInfoUpdate"
        />

        <div class="flex space-x-4 mt-6">
          <div class="w-1/3">
            <SectionCard
              v-if="organization"
              title="Localisations géographiques:"
              :items="locationItems"
              @update="handleLocationUpdate"
            />
          </div>
          <div class="w-2/3">
            <SectionCard
              v-if="organization"
              title="Contact et communication"
              :items="contactItems"
              @update="handleContactUpdate"
            />
          </div>
        </div>
      </div>

      <div v-else-if="activeTab === 'Products'">
        <!-- Show content for "Products" tab -->

        <div class="row mb-3">
          <!-- 1) Sites en Tunisie -->
          <div class="col-md-4">
            <SitesCard
              title="Sites en Tunisie"
              :sites="tunisieSites"
              @updateSites="handleSitesUpdate"
            />
          </div>

          <!-- 2) Pays d'implantations (first) -->
          <div class="col-md-4">
            <SitesCard
              title="Pays d'implantations sites étrangers"
              :sites="etrangerSites1"
              @updateSites="handleImportSitesUpdate"
            />
          </div>

          <!-- 3) Pays d'implantations (second) -->
          <div class="col-md-4">
            <SitesCard
              title="Pays d'exportations sites étrangers"
              :sites="etrangerSites2"
              @updateSites="handleExportSitesUpdate"
            />
          </div>
        </div>
        <div class="bg-white flex shadow p-12 relative">
          <button class="absolute top-4 right-4" @click="showModal = true">
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                stroke="black"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <div class="w-1/3 -mt-10 -ml-12 px-4">
            <ActivityChecklist
              :initialPrimary="organization.value?.primaryActivities || []"
              :initialSecondary="organization.value?.secondaryActivities || []"
              @updateActivities="handleActivitiesUpdate"
            />
          </div>
          <div class="w-2/3 ml-12 px-4 border-l-2 border-gray-200">
            <ProductTable :products="productsData" />
          </div>
        </div>
      </div>

      <div v-else-if="activeTab === 'Human Resources'">
        <!-- Show content for "Human Resources" tab -->
        <h3 class="text-xl font-semibold mb-2">Human Resources</h3>
        <!-- ... -->
      </div>
    </main>
    <EditProductModal
      v-if="showModal"
      title="Liste Produits"
      :products="productsData"
      @save="handleSave"
      @cancel="showModal = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import organizationService from "../Services/services/organisation";
import userService from "../Services/services/users";
import EditRequest from "@/components/EditRequest.vue";
import { useRoute } from "vue-router";
import SectionCard from "@/components/SectionCard.vue";
import CompanyHeader from "@/components/CompanyHeader.vue";
import ProductTable from "@/components/ProductTable.vue";
import ActivityChecklist from "@/components/ActivityChecklist.vue";
import EditProductModal from "@/components/EditProductModal.vue";
import SideMenu from "@/components/SideMenu.vue";
const route = useRoute();
const organization = ref(null);
const organizationId = route.params.organizationId;
const showModal = ref(false);
onMounted(async () => {
  try {
    const response = await organizationService.getOrganization(organizationId);

    organization.value = response.data;
  } catch (error) {
    console.error("Failed to fetch organization data:", error);
  }
});
const companyName = computed(() => {
  return organization.value ? organization.value.fullName : "Loading...";
});

const imageUrl = computed(() => {
  return organization.value && organization.value.logoUrl
    ? organization.value.logoUrl
    : "https://via.placeholder.com/150";
});
const country = computed(() => {
  return organization.value ? organization.value.country : "N/A";
});
const foundationYear = computed(() => {
  return organization.value ? organization.value.foundingYear : "N/A";
});
const website = computed(() => {
  return organization.value ? organization.value.websiteUrl : "#";
});
const progressValue = 50;

const generalInfoItems = computed(() => {
  if (!organization.value) return [];
  return [
    {
      label: "Nom complet de l'entreprise:",
      value: organization.value.fullName || organization.value.name,
    },
    { label: "Forme juridique:", value: organization.value.legalStatus },
    { label: "Année de création:", value: organization.value.foundingYear },
    {
      label: "Affiliation du groupe:",
      value: organization.value.groupAffiliation,
    },
  ];
});

const locationItems = computed(() => {
  if (!organization.value) return [];
  return [
    { label: "Siège social:", value: organization.value.headOffice },
    {
      label: "Sites de R&D:",
      value: organization.value.rAndDSites?.map((site) => site.name),
    },
    {
      label: "Autres emplacements:",
      value:
        organization.value.otherLocations?.map((loc) => loc.name).join(", ") ||
        "",
    },
    { label: "Ville:", value: organization.value.city },
    { label: "Pays:", value: organization.value.country },
  ];
});

const contactItems = computed(() => {
  if (!organization.value) return [];

  // Build an array for social links with labels
  const socialLinks = [];
  if (organization.value.linkedin) {
    socialLinks.push(`LinkedIn: ${organization.value.linkedin}`);
  }
  if (organization.value.facebook) {
    socialLinks.push(`Facebook: ${organization.value.facebook}`);
  }
  if (organization.value.twitter) {
    socialLinks.push(`Twitter: ${organization.value.twitter}`);
  }

  // Build an array for the primary contact with labels
  let contactArray = [];
  if (organization.value.adherent) {
    contactArray.push(`Nom: ${organization.value.adherent.name || "N/A"}`);
    contactArray.push(
      `Position: ${organization.value.adherent.position || "N/A"}`
    );
    contactArray.push(`Contact: ${organization.value.adherent.phone || "N/A"}`);
  }

  return [
    {
      label: "Numéro de téléphone principal:",
      value: organization.value.phone,
    },
    {
      label: "Personne à contacter principale:",
      value: contactArray,
    },
    {
      label: "Comptes officiels des réseaux sociaux",
      value: socialLinks,
    },
    {
      label: "Website:",
      value: organization.value.websiteUrl,
    },
    {
      label: "Adresse e-mail:",
      value: organization.value.email,
    },
  ];
});

async function handleGeneralInfoUpdate(updatedItems) {
  // Build the payload by mapping the updated items to the expected keys.
  // Adjust the mapping logic according to your actual field labels.
  const payload = {
    fullName: updatedItems.find((item) =>
      item.label.includes("Nom complet de l'entreprise")
    )?.value,
    legalStatus: updatedItems.find((item) =>
      item.label.includes("Forme juridique")
    )?.value,
    foundingYear: updatedItems.find((item) =>
      item.label.includes("Année de création")
    )?.value,
    groupAffiliation: updatedItems.find((item) =>
      item.label.includes("Affiliation du groupe")
    )?.value,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update the local organization state with the new data
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating contact information:", error);
  }
}
async function handleLocationUpdate(updatedItems) {
  // Get raw values from updatedItems
  const headOffice = updatedItems.find((item) =>
    item.label.includes("Siège social")
  )?.value;
  const rawRAndDSites = updatedItems.find((item) =>
    item.label.includes("Sites de R&D")
  )?.value;
  const rawOtherLocations = updatedItems.find((item) =>
    item.label.includes("Autres emplacements")
  )?.value;
  const city = updatedItems.find((item) => item.label.includes("Ville"))?.value;
  const country = updatedItems.find((item) =>
    item.label.includes("Pays")
  )?.value;

  // Transform raw R&D sites into array of objects with a "name" property.
  // Assume rawRAndDSites is an array of strings.
  const rAndDSites = Array.isArray(rawRAndDSites)
    ? rawRAndDSites.map((site) => ({ name: site }))
    : [];

  // For other locations, if it's a string (comma-separated), split it; otherwise assume an array.
  let otherLocations = [];
  if (typeof rawOtherLocations === "string") {
    otherLocations = rawOtherLocations
      .split(",")
      .map((loc) => loc.trim())
      .filter(Boolean)
      .map((loc) => ({ name: loc }));
  } else if (Array.isArray(rawOtherLocations)) {
    otherLocations = rawOtherLocations.map((loc) => ({ name: loc }));
  }

  // Build the payload based on the backend's expected structure
  const payload = {
    headOffice,
    rAndDSites, // Should be an array of objects with { name: string }
    otherLocations, // Should be an array of objects with { name: string }
    city,
    country,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update local organization data with the response data
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating contact information:", error);
  }
}

async function handleContactUpdate(updatedItems) {
  // Extract main organization phone
  const phoneItem = updatedItems.find((item) =>
    item.label.includes("Numéro de téléphone principal")
  );
  const phone = phoneItem ? phoneItem.value : undefined;

  // Extract primary contact info
  const contactItem = updatedItems.find((item) =>
    item.label.includes("Personne à contacter principale")
  );

  let contactName = "";
  let position = "";
  let contactPhone = "";

  if (contactItem && Array.isArray(contactItem.value)) {
    contactItem.value.forEach((line) => {
      const lowerLine = line.toLowerCase();
      if (lowerLine.startsWith("nom:")) {
        contactName = line.replace(/nom:/i, "").trim();
      } else if (lowerLine.startsWith("position:")) {
        position = line.replace(/position:/i, "").trim();
      } else if (lowerLine.startsWith("contact:")) {
        contactPhone = line.replace(/contact:/i, "").trim();
      }
    });
  }

  // Extract social media accounts
  const socialItem = updatedItems.find((item) =>
    item.label.includes("Comptes officiels")
  );
  let linkedin = undefined,
    facebook = undefined,
    twitter = undefined;
  if (socialItem && Array.isArray(socialItem.value)) {
    socialItem.value.forEach((line) => {
      const lowerLine = line.toLowerCase();
      if (lowerLine.startsWith("linkedin:")) {
        linkedin = line.replace(/linkedin:/i, "").trim();
      } else if (lowerLine.startsWith("facebook:")) {
        facebook = line.replace(/facebook:/i, "").trim();
      } else if (lowerLine.startsWith("twitter:")) {
        twitter = line.replace(/twitter:/i, "").trim();
      }
    });
  }

  // Extract website
  const websiteItem = updatedItems.find((item) =>
    item.label.includes("Website")
  );
  const websiteUrl = websiteItem ? websiteItem.value : undefined;

  // Extract email
  const emailItem = updatedItems.find((item) =>
    item.label.includes("Adresse e-mail")
  );
  const email = emailItem ? emailItem.value : undefined;

  // Payload for organization update
  const orgPayload = {
    phone,
    linkedin,
    facebook,
    twitter,
    websiteUrl,
    email,
  };
  const currentUserId = organization.value.adherentId;
  try {
    // First update the user (primary contact)
    if (currentUserId && contactName && email) {
      const userPayload = {
        name: contactName,
        phone: contactPhone || phone,
        position,
        email,
      };
      await userService.updateUser(currentUserId, userPayload);
    }

    // Then update the organization
    const response = await organizationService.updateOrganization(
      organizationId,
      orgPayload
    );
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating contact information:", error);
  }
}
async function handleSitesUpdate(updatedSites) {
  // Build payload for updating local sites only.
  // If your backend expects localSites as an array of objects,
  // you can send the updated sites array directly.
  const payload = {
    localSites: updatedSites, // updatedSites is the array provided from the modal
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update your local organization state with the new data.
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating local sites:", error);
  }
}
async function handleSave(updatedProducts) {
  showModal.value = false;
  try {
    // Build the payload for updating products.
    // The UpdateOrganizationRequest accepts a "products" field which is an array.
    const payload = {
      products: updatedProducts,
    };

    // Call the update organization endpoint with the new products list.
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update local organization state based on returned data.
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating products:", error);
  }
}
// Computed property for importation sites (étrangerSites1)
const etrangerSites1 = computed(() => {
  if (organization.value && organization.value.foreignImplantationSites) {
    return organization.value.foreignImplantationSites.map((site) => ({
      name: site.name,
      capacity: site.capacity,
      flagUrl: site.flagUrl || "", // Adjust if flagUrl exists on site objects
    }));
  }
  return [];
});

// Computed property for exportation sites (étrangerSites2)
const etrangerSites2 = computed(() => {
  if (organization.value && organization.value.foreignExportationSites) {
    return organization.value.foreignExportationSites.map((site) => ({
      name: site.name,
      capacity: site.capacity,
      flagUrl: site.flagUrl || "",
    }));
  }
  return [];
});
async function handleImportSitesUpdate(updatedSites) {
  // Build a payload with the updated importation sites.
  const payload = {
    foreignImplantationSites: updatedSites, // Expecting an array of objects e.g. [{ name, capacity, flagUrl }, ...]
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update local organization data with the new response
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating importation sites:", error);
  }
}
async function handleExportSitesUpdate(updatedSites) {
  // Build a payload with the updated exportation sites.
  const payload = {
    foreignExportationSites: updatedSites, // Expecting an array of objects e.g. [{ name, capacity, flagUrl }, ...]
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update local organization data with the new response
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating exportation sites:", error);
  }
}

const activeTab = ref("General");

function handleTabChange(newTab) {
  activeTab.value = newTab;
}
const tunisieSites = computed(() => {
  // organization.value.localSites should already be an array of OrganizationSiteResponse objects.
  if (organization.value && organization.value.localSites) {
    // Map them to the format expected by SitesCard.
    return organization.value.localSites.map((site) => ({
      name: site.name,
      capacity: site.capacity,
    }));
  }
  return [];
});
const productsData = computed(() => {
  if (organization.value && organization.value.products) {
    return organization.value.products;
  }
  return [];
});

async function handleActivitiesUpdate({ primary, secondary }) {
  // Build a payload using the selected ids.
  const payload = {
    primaryActivities: primary,
    secondaryActivities: secondary,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId,
      payload
    );
    // Update your local organization state with the updated organization data.
    organization.value = response.data;
  } catch (error) {
    console.error("Error updating activities:", error);
  }
}

const customMenuItems = [
  {
    name: "EntreprisesList",
    label: "Entreprises List",
    path: "/entreprises",
    icon: '<path d="M3 3h18v2H3V3zm0 4h18v2H3V7zm0 4h18v2H3v-2zm0 4h18v2H3v-2z" fill="#C62027"/>',
  },
  {
    name: "EntrepriseInfo",
    label: "Entreprise Info",
    path: `/generalpage/${organizationId}`,
    icon: '<path d="M8.33333 10.8333H3.33333C3.11232 10.8333 2.90036 10.9211 2.74408 11.0774C2.5878 11.2337 2.5 11.4457 2.5 11.6667V16.6667C2.5 16.8877 2.5878 17.0996 2.74408 17.2559C2.90036 17.4122 3.11232 17.5 3.33333 17.5H8.33333C8.55435 17.5 8.76631 17.4122 8.92259 17.2559C9.07887 17.0996 9.16667 16.8877 9.16667 16.6667V11.6667C9.16667 11.4457 9.07887 11.2337 8.92259 11.0774C8.76631 10.9211 8.55435 10.8333 8.33333 10.8333ZM7.5 15.8333H4.16667V12.5H7.5V15.8333ZM16.6667 2.5H11.6667C11.4457 2.5 11.2337 2.5878 11.0774 2.74408C10.9211 2.90036 10.8333 3.11232 10.8333 3.33333V8.33333C10.8333 8.55435 10.9211 8.76631 11.0774 8.92259C11.2337 9.07887 11.4457 9.16667 11.6667 9.16667H16.6667C16.8877 9.16667 17.0996 9.07887 17.2559 8.92259C17.4122 8.76631 17.5 8.55435 17.5 8.33333V3.33333C17.5 3.11232 17.4122 2.90036 17.2559 2.74408C17.0996 2.5878 16.8877 2.5 16.6667 2.5V2.5ZM15.8333 7.5H12.5V4.16667H15.8333V7.5ZM16.6667 13.3333H15V11.6667C15 11.4457 14.9122 11.2337 14.7559 11.0774C14.5996 10.9211 14.3877 10.8333 14.1667 10.8333C13.9457 10.8333 13.7337 10.9211 13.5774 11.0774C13.4211 11.2337 13.3333 11.4457 13.3333 11.6667V13.3333H11.6667C11.4457 13.3333 11.2337 13.4211 11.0774 13.5774C10.9211 13.7337 10.8333 13.9457 10.8333 14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15H13.3333V16.6667C13.3333 16.8877 13.4211 17.0996 13.5774 17.2559C13.7337 17.4122 13.9457 17.5 14.1667 17.5C14.3877 17.5 14.5996 17.4122 14.7559 17.2559C14.9122 17.0996 15 16.8877 15 16.6667V15H16.6667C16.8877 15 17.0996 14.9122 17.2559 14.7559C17.4122 14.5996 17.5 14.3877 17.5 14.1667C17.5 13.9457 17.4122 13.7337 17.2559 13.5774C17.0996 13.4211 16.8877 13.3333 16.6667 13.3333ZM8.33333 2.5H3.33333C3.11232 2.5 2.90036 2.5878 2.74408 2.74408C2.5878 2.90036 2.5 3.11232 2.5 3.33333V8.33333C2.5 8.55435 2.5878 8.76631 2.74408 8.92259C2.90036 9.07887 3.11232 9.16667 3.33333 9.16667H8.33333C8.55435 9.16667 8.76631 9.07887 8.92259 8.92259C9.07887 8.76631 9.16667 8.55435 9.16667 8.33333V3.33333C9.16667 3.11232 9.07887 2.90036 8.92259 2.74408C8.76631 2.5878 8.55435 2.5 8.33333 2.5V2.5ZM7.5 7.5H4.16667V4.16667H7.5V7.5Z" fill="#C62027"/>',
  },
];
</script>
