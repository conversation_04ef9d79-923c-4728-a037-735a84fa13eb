<template>
  <div>
    <!-- Error toast -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Success toast -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progress" />

    <!-- Loading indicator -->
    <div v-if="isLoading" class="flex justify-center items-center py-10">
      <div
        class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"
      ></div>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Innovation & New Products Section -->
      <div>
        <div class="flex items-center mb-4 w-[25%]">
          <div
            class="bg-[rgba(198,32,39,0.2)] text-[#C62027] !rounded-xl px-4 py-2 flex-grow"
          >
            <h2 class="!text-sm font-semibold">
              Innovation & Nouveaux Produits
            </h2>
          </div>
          <div
            class="bg-white !rounded-xl px-3 ml-2 py-2 shadow flex items-center"
          >
            <button @click="onEditProducts">
              <svg
                width="25"
                height="24"
                viewBox="0 0 25 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.5 4H6.5C5.39543 4 4.5 4.89543 4.5 6V18C4.5 19.1046 5.39543 20 6.5 20H18.5C19.6046 20 20.5 19.1046 20.5 18V12M9.5 15V12.5L18.25 3.75C18.9404 3.05964 20.0596 3.05964 20.75 3.75V3.75C21.4404 4.44036 21.4404 5.55964 20.75 6.25L16 11L12 15H9.5Z"
                  stroke="black"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <!-- Products Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div
            v-for="(product, index) in products"
            :key="index"
            class="bg-white shadow rounded-xl p-4"
          >
            <div class="flex">
              <!-- Product Image -->
              <div class="mr-3 flex-shrink-0">
                <img
                  :src="
                    product.imageUrl ||
                    'https://images.unsplash.com/photo-1611921059084-64ac6b6e200c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=880&q=80'
                  "
                  :alt="product.name"
                  class="w-16 h-16 object-cover rounded-md"
                />
              </div>

              <div class="flex-1">
                <!-- Product Name -->
                <h3 class="font-bold text-sm mb-1">
                  {{ product.name || "Produit " + (index + 1) + ":" }}
                </h3>

                <!-- Product Description -->
                <p class="text-xs text-gray-600 line-clamp-4">
                  {{
                    product.description ||
                    "Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry's"
                  }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Investment Projects Section -->
      <div class="flex flex-nowrap gap-8 mt-6">
        <div class="w-[45%]">
          <InvestmentTable :data="investmentsData" @edit="onEditInvestments" />
          <!-- ESG Initiatives Section -->
        </div>
        <div class="w-[55%]">
          <!-- R&D Projects Section -->

          <ProjectsInitiativesCard
            title="Projets R&D"
            type="rd-project"
            :items="rdProjects"
            @update="handleRdProjectsUpdate"
          />
        </div>
      </div>
      <!-- Research & Development Section -->
      <div class="mt-6">
        <SectionCard
          title="Recherche & Développement"
          :items="researchDevelopmentItems"
          @update="handleResearchDevelopmentUpdate"
          :inlineLabels="true"
        />
      </div>

      <div class="mt-6 flex flex-nowrap gap-8">
        <div class="w-[45%]">
          <!-- ESG Indicators Section -->
          <ESGTable :data="esgData" @edit="onEditESG" />
        </div>
        <div class="w-[55%]">
          <ProjectsInitiativesCard
            title="Initiatives ESG"
            type="initiative"
            :items="initiatives"
            @update="handleInitiativesUpdate"
          />
        </div>
      </div>

      <!-- Partnerships Section -->
      <div class="flex flex-nowrap gap-8 mt-6">
        <div class="w-[60%]">
          <PartnershipsTable
            :data="partnershipsData"
            @edit="onEditPartnerships"
          />
        </div>
        <div class="w-[40%]">
          <!-- Certifications Section -->

          <section class="relative bg-white shadow rounded-lg p-6">
            <!-- Header -->
            <div class="px-4 mb-4">
              <div class="flex items-center justify-between">
                <h2 class="!text-lg text-gray-800 !font-bold">
                  Certifications
                </h2>
                <button
                  @click="onEditCertifications"
                  class="text-gray-500 hover:text-gray-700"
                >
                  <svg
                    width="25"
                    height="24"
                    viewBox="0 0 25 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                      stroke="black"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Certifications Grid -->
            <div class="grid grid-cols-3 gap-4 px-4 mt-4">
              <div
                v-for="(certification, index) in certificationsData"
                :key="index"
                class="bg-[#C62027] text-white py-3 px-4 rounded-md text-center font-medium"
              >
                {{ certification.name }}
              </div>
            </div>
          </section>
        </div>
      </div>

      <!-- Technologies Section -->
      <div class="mt-6 w-[70%]">
        <TechnologiesTable
          :data="technologiesData"
          @edit="onEditTechnologies"
        />
      </div>

      <!-- Edit Modals -->
      <EditProductModal
        v-if="showEditModal"
        title="Liste Nouveau Produits"
        :products="editProducts"
        @save="handleSaveProducts"
        @cancel="showEditModal = false"
        :showNgpField="false"
      />

      <EditInvestmentsModal
        v-if="showInvestmentsModal"
        title="Projets d'investissement prévus pour 2024"
        :items="editInvestments"
        @save="handleSaveInvestments"
        @cancel="showInvestmentsModal = false"
      />

      <EditESGModal
        v-if="showESGModal"
        title="Indicateur ESG"
        :items="editESG"
        @save="handleSaveESG"
        @cancel="showESGModal = false"
      />

      <EditPartnershipsModal
        v-if="showPartnershipsModal"
        title="Partenariats et Co-dév stratégiques"
        :items="editPartnerships"
        @save="handleSavePartnerships"
        @cancel="showPartnershipsModal = false"
      />

      <EditTechnologiesModal
        v-if="showTechnologiesModal"
        title="Technologies Clés et Digitalisation"
        :items="editTechnologies"
        @save="handleSaveTechnologies"
        @cancel="showTechnologiesModal = false"
      />

      <!-- Certifications Modal -->
      <EditCertificationsModal
        v-if="showCertificationsModal"
        title="Certifications"
        :certifications="editCertifications"
        @save="handleSaveCertifications"
        @cancel="showCertificationsModal = false"
      />

      <!-- We're not using this popup anymore since we're using SectionCard directly -->
      <!-- <EditResearchDevelopmentPopup
        v-if="showResearchDevelopmentModal"
        title="Recherche & Développement"
        :researchData="editResearchDevelopment"
        @save="handleSaveResearchDevelopment"
        @cancel="showResearchDevelopmentModal = false"
      /> -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import Toast from "@/components/Toast.vue";
import EditProductModal from "@/components/EditProductModal.vue";
import EditInvestmentsModal from "@/components/EditInvestmentsModal.vue";
import InvestmentTable from "@/components/InvestmentTable.vue";
import ESGTable from "@/components/ESGTable.vue";
import PartnershipsTable from "@/components/PartnershipsTable.vue";
import TechnologiesTable from "@/components/TechnologiesTable.vue";
import EditESGModal from "@/components/EditESGModal.vue";
import EditPartnershipsModal from "@/components/EditPartnershipsModal.vue";
import EditTechnologiesModal from "@/components/EditTechnologiesModal.vue";
import ProjectsInitiativesCard from "@/components/ProjectsInitiativesCard.vue";
import EditCertificationsModal from "@/components/EditCertificationsModal.vue";
import extrasService from "@/Services/services/extras";
import EditRequest from "@/components/EditRequest.vue";

// State
const isLoading = ref(true);
const errorMessage = ref("");
const successMessage = ref("");
const extras = ref(null);

// Modal state for products
const showEditModal = ref(false);
const editProducts = ref([]);

// Modal state for investments
const showInvestmentsModal = ref(false);
const editInvestments = ref([]);

// Modal state for ESG
const showESGModal = ref(false);
const editESG = ref([]);

// Modal state for Partnerships
const showPartnershipsModal = ref(false);
const editPartnerships = ref([]);

// Modal state for Technologies
const showTechnologiesModal = ref(false);
const editTechnologies = ref([]);

// Modal state for Certifications
const showCertificationsModal = ref(false);
const editCertifications = ref([]);

// Get organizationId from route params
const route = useRoute();
const organizationId = route.params.organizationId;

// Fetch extras from backend
async function loadExtras() {
  if (!organizationId) return;
  isLoading.value = true;
  try {
    const response = await extrasService.getExtras(organizationId);
    extras.value = response.data;
    console.log("Extras data:", extras.value);
  } catch (err) {
    console.error("Failed to load organization extras", err);
    errorMessage.value =
      "Erreur lors du chargement des innovations et nouveaux produits";
  } finally {
    isLoading.value = false;
  }
}

onMounted(loadExtras);

const progress = computed(() => {
  if (!extras.value || !extras.value.completion) {
    return 0;
  }
  return extras.value.completion;
});

// Get products from extras data
const products = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.products)) {
    return [];
  }
  return extras.value.products;
});

// Get investments from extras data
const investmentsData = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.investments)) {
    return [];
  }
  return extras.value.investments;
});

// Get ESG data from extras
const esgData = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.esgs)) {
    return [];
  }
  return extras.value.esgs;
});

// Get partnerships data from extras
const partnershipsData = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.partnerships)) {
    return [];
  }
  return extras.value.partnerships;
});

// Get technologies data from extras
const technologiesData = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.technologies)) {
    return [];
  }
  return extras.value.technologies;
});

// Get R&D projects from extras data
const rdProjects = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.rAndDProjects)) {
    return [];
  }
  return extras.value.rAndDProjects;
});

// Get initiatives from extras data
const initiatives = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.initiatives)) {
    return [];
  }
  return extras.value.initiatives;
});

// Get certifications from extras data
const certificationsData = computed(() => {
  if (!extras.value || !Array.isArray(extras.value.certifications)) {
    return [];
  }
  return extras.value.certifications;
});

// Get research & development items for SectionCard
const researchDevelopmentItems = computed(() => {
  if (!extras.value || !extras.value.researchDevelopment) {
    return [];
  }

  const data = extras.value.researchDevelopment;

  return [
    {
      label: "Budget alloué à la R&D en 2024 :",
      value: data.budget2024 ? `${data.budget2024} M` : "N/A",
      type: "number", // Add type for validation
      placeholder: "Entrez un nombre (ex: 10)",
    },
    {
      label: "Pourcentage du CA dédié à la R&D :",
      value: data.revenuePercentage ? `${data.revenuePercentage}%` : "N/A",
      type: "number", // Add type for validation
      placeholder: "Entrez un pourcentage (ex: 10)",
    },
    {
      label: "Nombre de projets R&D en cours :",
      value: data.projectsInProgress ? data.projectsInProgress : "N/A",
      type: "number", // Add type for validation
      placeholder: "Entrez un nombre entier",
    },
    {
      label: "Nombre de brevets déposés en 2023 – 2024 :",
      value: data.patentsCount ? data.patentsCount : "N/A",
      type: "number", // Add type for validation
      placeholder: "Entrez un nombre entier",
    },
    {
      label: "Partenariats avec des universités ou laboratoires :",
      value: data.universityPartnerships ? "Oui" : "Non",
      type: "select", // Use select for boolean values
      options: ["Oui", "Non"], // Provide options for the dropdown
    },
  ];
});

// No longer needed with the new EditCertificationsModal

// Open edit modal
function onEditProducts() {
  // Create a copy of the products for editing
  if (extras.value && Array.isArray(extras.value.products)) {
    editProducts.value = JSON.parse(JSON.stringify(extras.value.products));
  } else {
    // If no products exist yet, create an empty array
    editProducts.value = [];
  }
  showEditModal.value = true;
}

// Open investments edit modal
function onEditInvestments() {
  // Create a copy of the investments for editing
  if (extras.value && Array.isArray(extras.value.investments)) {
    editInvestments.value = JSON.parse(
      JSON.stringify(extras.value.investments)
    );
  } else {
    // If no investments exist yet, create an empty array
    editInvestments.value = [];
  }
  showInvestmentsModal.value = true;
}

// Handle save from products modal
async function handleSaveProducts(updatedProducts) {
  try {
    // Build payload for the backend
    const payload = {
      products: updatedProducts.map((p) => ({
        name: p.name,
        description: p.description,
        ngp: p.ngp || "",
        // Note: In a real implementation, you would need to handle image uploads differently
        // This is just a placeholder for the UI demonstration
        imageUrl: p.imageUrl,
      })),
    };

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.products = payload.products;
    }

    // Show success message and close modal
    successMessage.value = "Produits mis à jour avec succès";
    showEditModal.value = false;
  } catch (err) {
    console.error("Error updating products:", err);
    errorMessage.value = "Erreur lors de la mise à jour des produits";
    // Keep modal open on error
  }
}

// Handle save from investments modal
async function handleSaveInvestments(updatedInvestments) {
  try {
    // Build payload for the backend
    const payload = {
      investments: updatedInvestments.map((item) => ({
        name: item.name,
        value: item.value,
        type: "investment",
      })),
    };

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.investments = payload.investments;
    }

    // Show success message and close modal
    successMessage.value = "Projets d'investissement mis à jour avec succès";
    showInvestmentsModal.value = false;
  } catch (err) {
    console.error("Error updating investments:", err);
    errorMessage.value =
      "Erreur lors de la mise à jour des projets d'investissement";
    // Keep modal open on error
  }
}

// Open ESG edit modal
function onEditESG() {
  // Create a copy of the ESG data for editing
  if (extras.value && Array.isArray(extras.value.esgs)) {
    editESG.value = JSON.parse(JSON.stringify(extras.value.esgs));
  } else {
    // If no ESG data exists yet, create an empty array
    editESG.value = [];
  }
  showESGModal.value = true;
}

// Handle save from ESG modal
async function handleSaveESG(updatedESG) {
  try {
    // Build payload for the backend
    const payload = {
      esgs: updatedESG.map((item) => ({
        name: item.name,
        value: item.value,
        type: "esg", // Must match OrganizationAttributeType.ESG
      })),
    };

    console.log("Sending ESG payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.esgs = payload.esgs;
    }

    // Show success message
    successMessage.value = "Indicateurs ESG mis à jour avec succès";

    // Close modal
    showESGModal.value = false;
  } catch (error) {
    console.error("Error updating ESG data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des indicateurs ESG";
    // Keep modal open on error
  }
}

// Open Partnerships edit modal
function onEditPartnerships() {
  // Create a copy of the partnerships data for editing
  if (extras.value && Array.isArray(extras.value.partnerships)) {
    editPartnerships.value = JSON.parse(
      JSON.stringify(extras.value.partnerships)
    );
  } else {
    // If no partnerships data exists yet, create an empty array
    editPartnerships.value = [];
  }
  showPartnershipsModal.value = true;
}

// Handle save from Partnerships modal
async function handleSavePartnerships(updatedPartnerships) {
  try {
    // Build payload for the backend
    const payload = {
      partnerships: updatedPartnerships.map((item) => ({
        name: item.name,
        value: item.value,
        type: "partnerships", // Must match OrganizationAttributeType.Partnerships
      })),
    };

    console.log("Sending Partnerships payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.partnerships = payload.partnerships;
    }

    // Show success message
    successMessage.value = "Partenariats mis à jour avec succès";

    // Close modal
    showPartnershipsModal.value = false;
  } catch (error) {
    console.error("Error updating Partnerships data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des partenariats";
    // Keep modal open on error
  }
}

// Open Technologies edit modal
function onEditTechnologies() {
  // Create a copy of the technologies data for editing
  if (extras.value && Array.isArray(extras.value.technologies)) {
    editTechnologies.value = JSON.parse(
      JSON.stringify(extras.value.technologies)
    );
  } else {
    // If no technologies data exists yet, create an empty array
    editTechnologies.value = [];
  }
  showTechnologiesModal.value = true;
}

// Handle save from Technologies modal
async function handleSaveTechnologies(updatedTechnologies) {
  try {
    // Build payload for the backend
    const payload = {
      technologies: updatedTechnologies.map((item) => ({
        name: item.name,
        value: item.value,
        type: "technologies", // Must match OrganizationAttributeType.Technologies
      })),
    };

    console.log("Sending Technologies payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.technologies = payload.technologies;
    }

    // Show success message
    successMessage.value = "Technologies mises à jour avec succès";

    // Close modal
    showTechnologiesModal.value = false;
  } catch (error) {
    console.error("Error updating Technologies data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des technologies";
    // Keep modal open on error
  }
}

// We're now using SectionCard directly, so we don't need this function anymore

// Handle Research & Development update from SectionCard
async function handleResearchDevelopmentUpdate(updatedItems) {
  try {
    // Convert the updated items back to the format expected by the backend
    const updatedResearchData = {
      budget2024: parseNumberValue(updatedItems[0].value),
      revenuePercentage: parsePercentageValue(updatedItems[1].value),
      projectsInProgress: parseNumberValue(updatedItems[2].value),
      patentsCount: parseNumberValue(updatedItems[3].value),
      universityPartnerships: updatedItems[4].value === "Oui",
    };

    // Build payload for the backend
    const payload = {
      researchDevelopment: updatedResearchData,
    };

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.researchDevelopment = payload.researchDevelopment;
    }

    // Show success message
    successMessage.value = "Recherche & Développement mis à jour avec succès";
  } catch (error) {
    errorMessage.value =
      "Erreur lors de la mise à jour de la recherche & développement";
    // Re-throw to keep the popup open
    throw error;
  }
}

// Helper function to parse a number value from a string
function parseNumberValue(value) {
  if (value === "N/A" || value === null || value === undefined) return null;

  // If value is already a number, return it
  if (typeof value === "number") return value;

  // Convert to string to ensure we can use match
  const valueStr = String(value);

  // Extract the number from the string (e.g., "10 M" -> 10)
  const match = valueStr.match(/(\d+(\.\d+)?)/);
  return match ? parseFloat(match[0]) : null;
}

// Helper function to parse a percentage value from a string
function parsePercentageValue(value) {
  if (value === "N/A" || value === null || value === undefined) return null;

  // If value is already a number, return it
  if (typeof value === "number") return value;

  // Convert to string to ensure we can use match
  const valueStr = String(value);

  // Extract the number from the string (e.g., "10%" -> 10)
  const match = valueStr.match(/(\d+(\.\d+)?)/);
  return match ? parseFloat(match[0]) : null;
}

// Handle R&D Projects update
async function handleRdProjectsUpdate(updatedProjects) {
  try {
    // Build payload for the backend
    const payload = {
      rAndDProjects: updatedProjects,
    };

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.rAndDProjects = payload.rAndDProjects;
    }

    // Show success message
    successMessage.value = "Projets R&D mis à jour avec succès";
  } catch (error) {
    console.error("Error updating R&D projects:", error);
    errorMessage.value = "Erreur lors de la mise à jour des projets R&D";
    // Re-throw to keep the popup open
    throw error;
  }
}

// Handle Initiatives update
async function handleInitiativesUpdate(updatedInitiatives) {
  try {
    // Build payload for the backend
    const payload = {
      initiatives: updatedInitiatives,
    };

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.initiatives = payload.initiatives;
    }

    // Show success message
    successMessage.value = "Initiatives ESG mises à jour avec succès";
  } catch (error) {
    console.error("Error updating ESG initiatives:", error);
    errorMessage.value = "Erreur lors de la mise à jour des initiatives ESG";
    // Re-throw to keep the popup open
    throw error;
  }
}

// Open Certifications edit modal
function onEditCertifications() {
  // Create a copy of the certifications data for editing
  if (extras.value && Array.isArray(extras.value.certifications)) {
    editCertifications.value = JSON.parse(
      JSON.stringify(extras.value.certifications)
    );
  } else {
    // If no certifications data exists yet, create an empty array
    editCertifications.value = [];
  }
  showCertificationsModal.value = true;
}

// Handle save from Certifications modal
async function handleSaveCertifications(updatedCertifications) {
  try {
    // Build payload for the backend
    const payload = {
      certifications: updatedCertifications,
    };

    console.log("Sending Certifications payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.certifications = payload.certifications;
    }

    // Show success message
    successMessage.value = "Certifications mises à jour avec succès";

    // Close modal
    showCertificationsModal.value = false;
  } catch (error) {
    console.error("Error updating Certifications data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des certifications";
    // Keep modal open on error
    throw error;
  }
}
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
