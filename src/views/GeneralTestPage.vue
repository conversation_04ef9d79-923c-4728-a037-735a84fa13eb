<template>
  <div>
    <!-- Toast component for error messages -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Toast component for success messages -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progress" />

    <SectionCard
      v-if="organization"
      title="À propos de la société"
      :items="generalInfoItems"
      :inlineLabels="true"
      @update="handleGeneralInfoUpdate"
    />

    <div class="flex space-x-4 mt-6">
      <div class="w-1/3">
        <SectionCard
          v-if="organization"
          title="Localisations géographiques:"
          :items="locationItems"
          @update="handleLocationUpdate"
        />
      </div>
      <div class="w-2/3">
        <SectionCard
          v-if="organization"
          title="Contact et communication"
          :items="contactItems"
          @update="handleContactUpdate"
        />
      </div>
    </div>

    <!-- Vue extérieure and Vue intérieure Sections -->
    <div class="flex space-x-4 mt-6">
      <div class="w-1/2">
        <SectionCard
          v-if="organization"
          title="Vue extérieure"
          :items="externalViewsItems"
          @update="handleExternalViewsUpdate"
        />
      </div>
      <div class="w-1/2">
        <SectionCard
          v-if="organization"
          title="Vue intérieure"
          :items="internalViewsItems"
          @update="handleInternalViewsUpdate"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, defineProps } from "vue";
import organizationService from "../Services/services/organisation";
import userService from "../Services/services/users";
import { useRoute } from "vue-router";
import SectionCard from "@/components/SectionCard.vue";
import Toast from "@/components/Toast.vue";
import EditRequest from "@/components/EditRequest.vue";

const route = useRoute();
const organization = ref(null);
const errorMessage = ref("");
const successMessage = ref("");
const props = defineProps({
  organizationId: {
    type: String,
    required: false,
  },
});

const organizationId = computed(
  () => props.organizationId || route.params.organizationId
);
console.log(organizationId.value);
onMounted(async () => {
  if (organizationId.value) {
    try {
      const response = await organizationService.getOrganization(
        organizationId.value
      );
      organization.value = response.data;
    } catch (error) {
      console.error("Failed to fetch organization data:", error);
    }
  }
});

const generalInfoItems = computed(() => {
  if (!organization.value) return [];
  return [
    {
      label: "Nom complet de l'entreprise:",
      value: organization.value.name,
    },
    { label: "Forme juridique:", value: organization.value.legalStatus },
    { label: "Année de création:", value: organization.value.foundingYear },
    {
      label: "Affiliation du groupe:",
      value: organization.value.groupAffiliation,
    },
  ];
});
const progress = computed(() => {
  if (!organization.value) return 0;
  return organization.value.completion || 0;
});
const locationItems = computed(() => {
  if (!organization.value) return [];
  return [
    { label: "Siège social:", value: organization.value.headOffice },
    {
      label: "Sites de R&D:",
      value: organization.value.rAndDSites?.map((site) => site.name),
    },
    {
      label: "Autres emplacements:",
      value:
        organization.value.otherLocations?.map((loc) => loc.name).join(", ") ||
        "",
    },
    { label: "Ville:", value: organization.value.city },
    { label: "Pays:", value: organization.value.country },
  ];
});

const contactItems = computed(() => {
  if (!organization.value) return [];

  // Build an array for social links with labels
  const socialLinks = [];
  if (organization.value.linkedin) {
    socialLinks.push(`LinkedIn: ${organization.value.linkedin}`);
  }
  if (organization.value.facebook) {
    socialLinks.push(`Facebook: ${organization.value.facebook}`);
  }
  if (organization.value.twitter) {
    socialLinks.push(`Twitter: ${organization.value.twitter}`);
  }

  // Build an array for the primary contact with labels
  let contactArray = [];
  if (organization.value.adherent) {
    contactArray.push(`Nom: ${organization.value.adherent.name || ""}`);
    contactArray.push(
      `Position: ${organization.value.adherent.position || ""}`
    );
    contactArray.push(`Contact: ${organization.value.adherent.phone || ""}`);
  }

  return [
    {
      label: "Numéro de téléphone principal:",
      value: organization.value.phone,
    },
    {
      label: "Personne à contacter principale:",
      value: contactArray,
    },
    {
      label: "Comptes officiels des réseaux sociaux",
      value: socialLinks,
    },
    {
      label: "Website:",
      value: organization.value.websiteUrl,
    },
    {
      label: "Adresse e-mail:",
      value: organization.value.email,
    },
  ];
});

const externalViewsItems = computed(() => {
  if (!organization.value) return [];

  return [
    {
      label: "Photos de l'entreprise:",
      value: organization.value.externalViews || [],
      type: "photos", // Special type to indicate this is a photo gallery
    },
  ];
});

const internalViewsItems = computed(() => {
  if (!organization.value) return [];

  return [
    {
      label: "Photos intérieures:",
      value: organization.value.internalViews || [],
      type: "photos", // Special type to indicate this is a photo gallery
    },
  ];
});

async function handleGeneralInfoUpdate(updatedItems) {
  if (!organizationId.value) return;

  const payload = {
    name: updatedItems.find((item) =>
      item.label.includes("Nom complet de l'entreprise")
    )?.value,
    legalStatus: updatedItems.find((item) =>
      item.label.includes("Forme juridique")
    )?.value,
    foundingYear: updatedItems.find((item) =>
      item.label.includes("Année de création")
    )?.value,
    groupAffiliation: updatedItems.find((item) =>
      item.label.includes("Affiliation du groupe")
    )?.value,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId.value,
      payload
    );
    organization.value = response.data;
    successMessage.value = "Informations générales mises à jour avec succès";
  } catch (error) {
    console.error("Error updating general information:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des informations générales";
    throw new Error(errorMessage.value);
  }
}

async function handleLocationUpdate(updatedItems) {
  if (!organizationId.value) return;

  const headOffice = updatedItems.find((item) =>
    item.label.includes("Siège social")
  )?.value;
  const rawRAndDSites = updatedItems.find((item) =>
    item.label.includes("Sites de R&D")
  )?.value;
  const rawOtherLocations = updatedItems.find((item) =>
    item.label.includes("Autres emplacements")
  )?.value;
  const city = updatedItems.find((item) => item.label.includes("Ville"))?.value;
  const country = updatedItems.find((item) =>
    item.label.includes("Pays")
  )?.value;

  const rAndDSites = Array.isArray(rawRAndDSites)
    ? rawRAndDSites.map((site) => ({ name: site }))
    : [];

  let otherLocations = [];
  if (typeof rawOtherLocations === "string") {
    otherLocations = rawOtherLocations
      .split(",")
      .map((loc) => loc.trim())
      .filter(Boolean)
      .map((loc) => ({ name: loc }));
  } else if (Array.isArray(rawOtherLocations)) {
    otherLocations = rawOtherLocations.map((loc) => ({ name: loc }));
  }

  const payload = {
    headOffice,
    rAndDSites,
    otherLocations,
    city,
    country,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId.value,
      payload
    );
    organization.value = response.data;
    successMessage.value =
      "Informations de localisation mises à jour avec succès";
  } catch (error) {
    console.error("Error updating location information:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des informations de localisation";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}

async function handleContactUpdate(updatedItems) {
  if (!organizationId.value) return;

  // Extract main organization phone
  const phoneItem = updatedItems.find((item) =>
    item.label.includes("Numéro de téléphone principal")
  );
  const phone = phoneItem ? phoneItem.value : undefined;

  // Extract primary contact info
  const contactItem = updatedItems.find((item) =>
    item.label.includes("Personne à contacter principale")
  );

  let contactName = "";
  let position = "";
  let contactPhone = "";

  if (contactItem && Array.isArray(contactItem.value)) {
    contactItem.value.forEach((line) => {
      const lowerLine = line.toLowerCase();
      if (lowerLine.startsWith("nom:")) {
        contactName = line.replace(/nom:/i, "").trim();
      } else if (lowerLine.startsWith("position:")) {
        position = line.replace(/position:/i, "").trim();
      } else if (lowerLine.startsWith("contact:")) {
        contactPhone = line.replace(/contact:/i, "").trim();
      }
    });
  }

  // Extract social media accounts
  const socialItem = updatedItems.find((item) =>
    item.label.includes("Comptes officiels")
  );
  let linkedin = undefined,
    facebook = undefined,
    twitter = undefined;
  if (socialItem && Array.isArray(socialItem.value)) {
    socialItem.value.forEach((line) => {
      const lowerLine = line.toLowerCase();
      if (lowerLine.startsWith("linkedin:")) {
        linkedin = line.replace(/linkedin:/i, "").trim();
      } else if (lowerLine.startsWith("facebook:")) {
        facebook = line.replace(/facebook:/i, "").trim();
      } else if (lowerLine.startsWith("twitter:")) {
        twitter = line.replace(/twitter:/i, "").trim();
      }
    });
  }

  // Extract website
  const websiteItem = updatedItems.find((item) =>
    item.label.includes("Website")
  );
  const websiteUrl = websiteItem ? websiteItem.value : undefined;

  // Extract email
  const emailItem = updatedItems.find((item) =>
    item.label.includes("Adresse e-mail")
  );
  const email = emailItem ? emailItem.value : undefined;

  // Payload for organization update
  const orgPayload = {
    phone,
    linkedin,
    facebook,
    twitter,
    websiteUrl,
    email,
  };

  const currentUserId = organization.value?.adherentId;

  try {
    // First update the user (primary contact)
    if (currentUserId && contactName && email) {
      const userPayload = {
        name: contactName,
        phone: contactPhone || phone,
        position,
        email,
      };
      await userService.updateUser(currentUserId, userPayload);
    }

    // Then update the organization
    const response = await organizationService.updateOrganization(
      organizationId.value,
      orgPayload
    );
    organization.value = response.data;
    successMessage.value = "Informations de contact mises à jour avec succès";
  } catch (error) {
    console.error("Error updating contact information:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des informations de contact";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}

async function handleExternalViewsUpdate(updatedItems) {
  if (!organizationId.value) return;

  // Extract the photos array from the updated items
  const photosItem = updatedItems.find((item) =>
    item.label.includes("Photos de l'entreprise")
  );
  const externalViews = photosItem ? photosItem.value : [];

  const payload = {
    externalViews,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId.value,
      payload
    );
    organization.value = response.data;
    successMessage.value = "Photos extérieures mises à jour avec succès";
  } catch (error) {
    console.error("Error updating external views:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des photos extérieures";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}

async function handleInternalViewsUpdate(updatedItems) {
  if (!organizationId.value) return;

  // Extract the photos array from the updated items
  const photosItem = updatedItems.find((item) =>
    item.label.includes("Photos intérieures")
  );
  const internalViews = photosItem ? photosItem.value : [];

  const payload = {
    internalViews,
  };

  try {
    const response = await organizationService.updateOrganization(
      organizationId.value,
      payload
    );
    organization.value = response.data;
    successMessage.value = "Photos intérieures mises à jour avec succès";
  } catch (error) {
    console.error("Error updating internal views:", error);
    errorMessage.value =
      error.response?.data?.errorMessage ||
      "Erreur lors de la mise à jour des photos intérieures";
    // Throw an error to prevent the popup from closing
    throw new Error(errorMessage.value);
  }
}
</script>
