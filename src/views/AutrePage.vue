<template>
  <div>
    <!-- Toast component for error messages -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Toast component for success messages -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />
    <EditRequest :progressValue="progressValue" />

    <!-- Loading indicator -->
    <div v-if="isLoading" class="flex justify-center items-center py-10">
      <div
        class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"
      ></div>
    </div>

    <div v-else>
      <div class="flex flex-nowrap gap-4">
        <div class="w-[50%]">
          <EnvironmentCard
            title="Innovation"
            :environmentData="innovationData"
            @update="handleInnovationUpdate"
          />
          <!-- Technical Know-How Section -->
          <div class="mt-6 p-6 bg-white rounded-lg shadow-md">
            <div class="flex justify-between items-center mb-4">
              <h2 class="!text-lg !font-semibold text-gray-800">
                Autres infos sur le savoir-faire technique de l'Entreprise
              </h2>
              <button
                @click="openTechnicalKnowHowModal"
                class="text-gray-500 hover:text-gray-700"
              >
                <svg
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
                    stroke="black"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
            <p class="text-gray-600">
              {{
                technicalKnowHow ||
                "Aucune information sur le savoir-faire technique n'est disponible."
              }}
            </p>
          </div>
        </div>

        <!-- Environment Section -->
        <div class="w-[50%]">
          <EnvironmentCard
            :environmentData="environmentData"
            @update="handleEnvironmentUpdate"
          />
        </div>
      </div>
      <!-- Waste Distribution Section -->
      <div class="mt-6">
        <WasteDistributionCard
          :wasteData="wasteDistributionData"
          @update="handleWasteDistributionUpdate"
        />
      </div>

      <!-- Questions Table Section -->
      <div class="mt-6">
        <QuestionsTable
          :questions="questionsData"
          @update="handleQuestionsUpdate"
        />
      </div>

      <!-- Technical Know-How Edit Popup -->
      <EditPopup
        v-if="isEditingTechnicalKnowHow"
        :items="technicalKnowHowItems"
        title="Modifier le savoir-faire technique"
        @save="updateTechnicalKnowHow"
        @cancel="isEditingTechnicalKnowHow = false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import Toast from "@/components/Toast.vue";
import EnvironmentCard from "@/components/EnvironmentCard.vue";
import EditPopup from "@/components/EditPopup.vue";
import WasteDistributionCard from "@/components/WasteDistributionCard.vue";
import QuestionsTable from "@/components/QuestionsTable.vue";
import othersService from "../Services/services/others";
import EditRequest from "@/components/EditRequest.vue";

const route = useRoute();
const organization = ref(null);
const errorMessage = ref("");
const successMessage = ref("");
const isLoading = ref(true);
const isEditingTechnicalKnowHow = ref(false);

// Items for the technical know-how edit popup
const technicalKnowHowItems = ref([
  {
    label: "Savoir-faire technique",
    value: [], // Using an array to trigger textarea rendering
  },
]);

// Add props definition
const props = defineProps({
  organizationId: {
    type: String,
    required: false,
  },
});

// Use computed property for organizationId
const organizationId = computed(
  () => props.organizationId || route.params.organizationId
);

// Computed property for environment data
const environmentData = computed(() => {
  if (!organization.value || !organization.value.environment) {
    return {};
  }
  return organization.value.environment;
});

// Computed property for innovation data (second image)
const innovationData = computed(() => {
  if (!organization.value || !organization.value.environment) {
    return {};
  }

  return {
    productionIntegrationRate:
      organization.value.environment.productionIntegrationRate || "",
    hasDevelopProducts:
      organization.value.environment.hasDevelopProducts || false,
    hasDevelopProcesses:
      organization.value.environment.hasDevelopProcesses || false,
    hasDevelopMarkets:
      organization.value.environment.hasDevelopMarkets || false,
    hasOpenInnovation:
      organization.value.environment.hasOpenInnovation || false,
  };
});

const progressValue = computed(() => {
  if (!organization.value || !organization.value.completion) {
    return 0;
  }
  return organization.value.completion;
});

// Computed property for technical know-how
const technicalKnowHow = computed(() => {
  if (!organization.value || !organization.value.environment) {
    return "";
  }
  return organization.value.environment.technicalKnowHow || "";
});

// Computed property for waste distribution data
const wasteDistributionData = computed(() => {
  if (!organization.value || !organization.value.wasteDistribution) {
    return {};
  }
  return organization.value.wasteDistribution;
});

// Questions data
const questionsData = computed(() => {
  if (!organization.value || !organization.value.questions) {
    return [];
  }
  return organization.value.questions;
});

// Update technicalKnowHowItems when organization data changes
const updateTechnicalKnowHowItems = () => {
  // Split the text by newlines to create an array
  // If there's no text, use an empty array
  const text = technicalKnowHow.value || "";
  technicalKnowHowItems.value[0].value = text ? [text] : [];
};

// Load initial data
async function loadOthersData() {
  if (!organizationId.value) return;

  isLoading.value = true;

  try {
    console.log("Fetching data for organization ID:", organizationId.value);
    const response = await othersService.getOthers(organizationId.value);
    organization.value = response.data;
    console.log("Received organization data:", organization.value);

    // Update technical know-how items
    updateTechnicalKnowHowItems();

    // Log questions data if available
    if (organization.value && organization.value.questions) {
      console.log("Questions data from backend:", organization.value.questions);
    }
  } catch (error) {
    console.error("Error loading others data:", error);
    errorMessage.value =
      "Erreur lors du chargement des données environnementales";
  } finally {
    isLoading.value = false;
  }
}

// Call loadOthersData on component mount
onMounted(() => {
  loadOthersData();
});

// Handle environment update
async function handleEnvironmentUpdate(updatedData) {
  try {
    console.log("Environment update received:", updatedData);

    // Create a payload with the updated environment data
    const payload = {
      environment: {
        ...organization.value.environment,
        electricity: updatedData.electricity,
        electricityConsumption: updatedData.electricityConsumption,
        hasWaterPlant: updatedData.hasWaterPlant,
        waterConsumption: updatedData.waterConsumption,
        recyclablePercentage: updatedData.recyclablePercentage,
        ecoDesigned: updatedData.ecoDesigned,
        internalRevaluation: updatedData.internalRevaluation,
        localRecoveryRate: updatedData.localRecoveryRate,
        exportRate: updatedData.exportRate,
      },
    };

    // Send the update to the backend
    await othersService.updateOthers(organizationId.value, payload);

    // Update the local data
    organization.value = {
      ...organization.value,
      environment: {
        ...organization.value.environment,
        ...updatedData,
      },
    };

    successMessage.value = "Données environnementales mises à jour avec succès";
  } catch (error) {
    console.error("Error updating environment data:", error);
    errorMessage.value =
      "Erreur lors de la mise à jour des données environnementales";
  }
}

// Handle innovation update
async function handleInnovationUpdate(updatedData) {
  try {
    console.log("Innovation update received:", updatedData);

    // Create a payload with the updated innovation data
    const payload = {
      environment: {
        ...organization.value.environment,
        productionIntegrationRate: updatedData.productionIntegrationRate,
        hasDevelopProducts: updatedData.hasDevelopProducts,
        hasDevelopProcesses: updatedData.hasDevelopProcesses,
        hasDevelopMarkets: updatedData.hasDevelopMarkets,
        hasOpenInnovation: updatedData.hasOpenInnovation,
      },
    };

    // Send the update to the backend
    await othersService.updateOthers(organizationId.value, payload);

    // Update the local data
    organization.value = {
      ...organization.value,
      environment: {
        ...organization.value.environment,
        ...updatedData,
      },
    };

    successMessage.value = "Données d'innovation mises à jour avec succès";
  } catch (error) {
    console.error("Error updating innovation data:", error);
    errorMessage.value =
      "Erreur lors de la mise à jour des données d'innovation";
  }
}

// Open technical know-how modal
function openTechnicalKnowHowModal() {
  // Update items with current value before opening the popup
  updateTechnicalKnowHowItems();
  isEditingTechnicalKnowHow.value = true;
}

// Handle technical know-how update
async function updateTechnicalKnowHow(updatedItems) {
  try {
    console.log("Technical know-how update received:", updatedItems);

    // Get the updated value from the items array
    // Join the array elements with newlines to create a single string
    const updatedTechnicalKnowHow = Array.isArray(updatedItems[0].value)
      ? updatedItems[0].value.join("\n")
      : updatedItems[0].value;

    // Create a payload with the updated technical know-how
    const payload = {
      environment: {
        ...organization.value.environment,
        technicalKnowHow: updatedTechnicalKnowHow,
      },
    };

    // Send the update to the backend
    await othersService.updateOthers(organizationId.value, payload);

    // Update the local data
    organization.value = {
      ...organization.value,
      environment: {
        ...organization.value.environment,
        technicalKnowHow: updatedTechnicalKnowHow,
      },
    };

    // Close the edit modal and show success message
    isEditingTechnicalKnowHow.value = false;
    successMessage.value = "Savoir-faire technique mis à jour avec succès";
  } catch (error) {
    console.error("Error updating technical know-how:", error);
    errorMessage.value =
      "Erreur lors de la mise à jour du savoir-faire technique";
  }
}

// Validate that the total is 100%
function validateWasteDistributionTotal(data) {
  const values = [
    data.plastic,
    data.metallic,
    data.textilesAndLeather,
    data.oils,
    data.papersAndCardboard,
    data.hazardous,
    data.others,
  ];

  // Filter out undefined values
  const validNumbers = values.filter((v) => typeof v === "number");

  // Calculate the total
  const total = validNumbers.reduce((sum, val) => sum + val, 0);

  return Math.round(total) === 100;
}

// Handle waste distribution update
async function handleWasteDistributionUpdate(updatedData) {
  try {
    console.log("Waste distribution update received:", updatedData);

    // Validate that the total is 100%
    if (!validateWasteDistributionTotal(updatedData)) {
      errorMessage.value = "La somme des pourcentages doit être égale à 100%";
      throw new Error("La somme des pourcentages doit être égale à 100%");
    }

    // Create a payload with the updated waste distribution data
    const payload = {
      wasteDistribution: updatedData,
    };

    // Send the update to the backend
    await othersService.updateOthers(organizationId.value, payload);

    // Update the local data
    organization.value = {
      ...organization.value,
      wasteDistribution: updatedData,
    };

    successMessage.value = "Répartition des déchets mise à jour avec succès";
  } catch (error) {
    console.error("Error updating waste distribution:", error);
    errorMessage.value =
      error.message ||
      "Erreur lors de la mise à jour de la répartition des déchets";
    // Re-throw the error to keep the popup open
    throw error;
  }
}

// Handle questions update
async function handleQuestionsUpdate(updatedQuestions) {
  try {
    console.log("Questions update received:", updatedQuestions);

    // Create a payload with the updated questions data
    const payload = {
      questions: updatedQuestions,
    };

    // Send the update to the backend
    await othersService.updateOthers(organizationId.value, payload);

    // Update the local data
    organization.value = {
      ...organization.value,
      questions: updatedQuestions,
    };

    successMessage.value = "Questions et réponses mises à jour avec succès";
  } catch (error) {
    console.error("Error updating questions:", error);
    errorMessage.value =
      error.message ||
      "Erreur lors de la mise à jour des questions et réponses";
    // Re-throw the error to keep the popup open
    throw error;
  }
}
</script>
