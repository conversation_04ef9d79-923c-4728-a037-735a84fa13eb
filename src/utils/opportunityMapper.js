/**
 * Utility functions for mapping between frontend and backend opportunity values
 */

// Map frontend category display names to backend enum values
const categoryToBackend = {
  "Prochaine ressource recherchée": "next_resource",
  "Niveau 2 (Tier 2)": "level2",
  "Niveau 1 (Tier 1)": "level1",
  "Constructeurs (OEM)": "oem",
  "Assemblage de Production": "production_assembly",
};

// Map backend enum values to frontend display names
const categoryToFrontend = {
  next_resource: "Prochaine ressource recherchée",
  level2: "Niveau 2 (Tier 2)",
  level1: "Niveau 1 (Tier 1)",
  oem: "Constructeurs (OEM)",
  production_assembly: "Assemblage de Production",
};

// Map frontend priority values to backend enum values
const priorityToBackend = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
};

// Map backend enum values to frontend priority values
const priorityToFrontend = {
  low: "LOW",
  medium: "MEDIUM",
  high: "HIGH",
};

/**
 * Convert a frontend opportunity object to the backend format
 * @param {Object} opportunity - Frontend opportunity object
 * @returns {Object} Backend-formatted opportunity object
 */
export function toBackendOpportunity(opportunity) {
  return {
    id: opportunity.id,
    category: categoryToBackend[opportunity.category] || opportunity.category,
    description: opportunity.description,
    priority: priorityToBackend[opportunity.priority] || opportunity.priority,
  };
}

/**
 * Convert a backend opportunity object to the frontend format
 * @param {Object} opportunity - Backend opportunity object
 * @returns {Object} Frontend-formatted opportunity object
 */
export function toFrontendOpportunity(opportunity) {
  return {
    id: opportunity.id,
    category: categoryToFrontend[opportunity.category] || opportunity.category,
    description: opportunity.description,
    priority: priorityToFrontend[opportunity.priority] || opportunity.priority,
  };
}

/**
 * Convert an array of frontend opportunities to the backend format
 * @param {Array} opportunities - Array of frontend opportunity objects
 * @returns {Object} Backend-formatted request payload
 */
export function toBackendPayload(opportunities) {
  return {
    opportunities: opportunities.map(toBackendOpportunity),
  };
}

/**
 * Convert a backend response to an array of frontend opportunities
 * @param {Object} response - Backend response object
 * @returns {Array} Array of frontend-formatted opportunity objects
 */
export function fromBackendResponse(response) {
  if (!response || !response.opportunities) {
    return [];
  }
  return response.opportunities.map(toFrontendOpportunity);
}
