/**
 * Utility functions for handling API errors and transforming them into user-friendly messages
 */

/**
 * Maps validation error codes to user-friendly messages
 */
const validationErrorMessages = {
  "errors:field.alphabetic": "the name should be alphabetic",
  "errors:field.numeric": "this field must contain only numbers",
  "errors:field.required": "this field is required",
  "errors:field.phone": "the phone number should be numeric and start with +",
  "errors:field.phoneFormat":
    "the phone number should be in international format (e.g., +216XXXXXXXX)",
  // Don't set a generic message for "errors:field.invalid" as it could apply to many fields
  // Add more error mappings as needed
};

/**
 * Field-specific error messages that override the general ones
 */
const fieldSpecificErrorMessages = {
  // For the 'phone' field
  phone: {
    "errors:field.invalid":
      "the phone number should be numeric and start with +",
    "errors:field.required": "phone number is required",
  },
  // For the 'name' field
  name: {
    "errors:field.alphabetic": "the name should be alphabetic",
    "errors:field.required": "name is required",
  },
  // No contract-specific validations
  // Add more field-specific messages as needed
};

/**
 * Transforms an API error response into a user-friendly message
 * @param {Object} error - The error object from axios
 * @returns {String} A user-friendly error message
 */
export function getErrorMessage(error) {
  if (!error.response || !error.response.data) {
    return "Une erreur est survenue.";
  }

  const { errorCode, errorMessage, data } = error.response.data;

  // Log the error for debugging
  console.log("Error response:", { errorCode, errorMessage, data });

  // Handle validation errors
  if (errorCode === "validation_error" && data) {
    // Check if we have any specific field errors
    const fieldErrors = Object.entries(data);
    if (fieldErrors.length > 0) {
      console.log("Field errors:", fieldErrors);

      // Get the first field error
      const [field, errorCode] = fieldErrors[0];

      // No contract-specific error handling

      // First check if we have a field-specific message
      if (
        fieldSpecificErrorMessages[field] &&
        fieldSpecificErrorMessages[field][errorCode]
      ) {
        return fieldSpecificErrorMessages[field][errorCode];
      }

      // Then check if we have a general message for this error code
      if (validationErrorMessages[errorCode]) {
        return validationErrorMessages[errorCode];
      }

      // If no custom message, return the error code or a generic message
      return errorMessage || `Validation error: ${field}`;
    }
  }

  // Return the general error message or a fallback
  return errorMessage || "Une erreur est survenue.";
}
