import "./assets/main.css";
import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import { createBootstrap } from "bootstrap-vue-next";

import VueApexCharts from "vue3-apexcharts";
//import "@/assets/scss/main.scss";

//import "bootstrap/dist/css/bootstrap.min.css";
//import "bootstrap/dist/js/bootstrap.bundle.min.js";
//necessary css
import "bootstrap/dist/css/bootstrap.css";
import "bootstrap-vue-next/dist/bootstrap-vue-next.css";

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.component("apexchart", VueApexCharts);
app.use(createBootstrap());
app.mount("#app");
