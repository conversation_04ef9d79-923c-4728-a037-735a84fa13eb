<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3
      class="!text-[#C62027] border-b border-gray-400 pb-2 font-bold text-lg mb-4"
    >
      {{ title }}
    </h3>

    <!-- Production Data Table -->
    <table class="w-full">
      <thead>
        <tr class="!border-b !border-gray-300">
          <th class="py-4 text-left font-medium text-gray-700">
            Name of the Country
          </th>
          <th class="py-4 text-center font-medium text-gray-700">Capacity</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(site, index) in data"
          :key="index"
          class="!border-b !border-gray-300"
        >
          <td class="py-4 text-left text-gray-800">{{ site.name || "-" }}</td>
          <td class="py-4 text-center text-gray-800">
            {{ site.capacity || "-" }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  data: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each site should have name and capacity properties
      return value.every((site) => "name" in site && "capacity" in site);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
