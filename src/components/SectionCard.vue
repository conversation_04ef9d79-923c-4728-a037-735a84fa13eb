<template>
  <section class="mb-6 relative">
    <div
      class="bg-[rgba(198,32,39,0.2)] text-[#C62027] !rounded-xl px-4 mb-4 py-2"
    >
      <h2 class="!text-xs font-semibold">{{ title }}</h2>
    </div>

    <div class="bg-white p-4 shadow !rounded-xl !text-xs relative">
      <!-- Edit icon button in the corner -->
      <button
        @click="openEditModal"
        class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
      >
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 4H6.5C5.39543 4 4.5 4.89543 4.5 6V18C4.5 19.1046 5.39543 20 6.5 20H18.5C19.6046 20 20.5 19.1046 20.5 18V12M9.5 15V12.5L18.25 3.75C18.9404 3.05964 20.0596 3.05964 20.75 3.75V3.75C21.4404 4.44036 21.4404 5.55964 20.75 6.25L16 11L12 15H9.5Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <div
        v-if="inlineLabels"
        class="flex flex-wrap items-center gap-x-40 space-x-6 mb-4"
      >
        <div v-for="(item, index) in items" :key="index" class="mb-4">
          <div class="font-bold">{{ item.label }}</div>
          <template v-if="Array.isArray(item.value)">
            <ul class="list-disc pl-5">
              <li
                class="text-gray-600"
                v-for="(val, idx) in item.value"
                :key="idx"
              >
                {{ val }}
              </li>
            </ul>
          </template>
          <template v-else class="text-gray-600">
            {{ item.value }}
          </template>
        </div>
      </div>
      <div v-else>
        <div v-for="(item, index) in items" :key="index" class="mb-4">
          <div class="font-bold">{{ item.label }}</div>

          <!-- Special handling for photo galleries -->
          <template v-if="item.type === 'photos'">
            <div
              v-if="Array.isArray(item.value) && item.value.length > 0"
              class="relative mt-2"
            >
              <!-- Photo Slider -->
              <div
                class="relative mx-auto w-full h-80 bg-gray-100 rounded-lg overflow-hidden"
              >
                <img
                  :src="item.value[currentPhotoIndex]"
                  :alt="`Photo ${currentPhotoIndex + 1}`"
                  class="w-full h-full object-fit cursor-pointer"
                  @click="openPhotoModal(item.value[currentPhotoIndex])"
                />

                <!-- Left Arrow -->
                <button
                  v-if="item.value.length > 1"
                  @click="previousPhoto(item.value.length)"
                  class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity"
                >
                  <svg
                    class="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 19l-7-7 7-7"
                    ></path>
                  </svg>
                </button>

                <!-- Right Arrow -->
                <button
                  v-if="item.value.length > 1"
                  @click="nextPhoto(item.value.length)"
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity"
                >
                  <svg
                    class="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </button>

                <!-- Photo Counter -->
                <div
                  class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm"
                >
                  {{ currentPhotoIndex + 1 }} / {{ item.value.length }}
                </div>
              </div>
            </div>
            <div v-else class="text-gray-500 italic">
              Aucune photo disponible
            </div>
          </template>

          <!-- Regular handling for other types -->
          <template v-else-if="Array.isArray(item.value)">
            <ul class="list-disc pl-5">
              <li
                class="text-gray-600"
                v-for="(val, idx) in item.value"
                :key="idx"
              >
                {{ val }}
              </li>
            </ul>
          </template>
          <template v-else class="text-gray-600">
            {{ item.value }}
          </template>
        </div>
      </div>
      <slot />
    </div>

    <EditPopup
      v-if="isEditing"
      :items="items"
      :title="title"
      @save="handleSave"
      @cancel="isEditing = false"
      class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
    />

    <!-- Photo Modal -->
    <div
      v-if="selectedPhoto"
      class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
      @click="closePhotoModal"
    >
      <div class="relative max-w-4xl max-h-full p-4">
        <img
          :src="selectedPhoto"
          alt="Photo agrandie"
          class="max-w-full max-h-full object-contain rounded-lg"
        />
        <button
          @click="closePhotoModal"
          class="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from "vue";
import EditPopup from "./EditPopup.vue";

defineProps({
  title: String,
  items: Array,
  inlineLabels: {
    type: Boolean,
    default: false,
  },
  // New props for error and success messages:
});

const emit = defineEmits(["update"]);
const isEditing = ref(false);
const selectedPhoto = ref(null);
const currentPhotoIndex = ref(0);

function openEditModal() {
  isEditing.value = true;
}

function openPhotoModal(photoUrl) {
  selectedPhoto.value = photoUrl;
}

function closePhotoModal() {
  selectedPhoto.value = null;
}

function nextPhoto(totalPhotos) {
  currentPhotoIndex.value = (currentPhotoIndex.value + 1) % totalPhotos;
}

function previousPhoto(totalPhotos) {
  currentPhotoIndex.value =
    (currentPhotoIndex.value - 1 + totalPhotos) % totalPhotos;
}

function handleSave(updatedItems) {
  try {
    // Create a variable to track if we should close the popup
    let shouldClosePopup = true;

    // Add an event listener for unhandled promise rejections
    const errorHandler = () => {
      // If there's an error, don't close the popup
      shouldClosePopup = false;
      // Remove the event listener
      window.removeEventListener("unhandledrejection", errorHandler);
    };

    // Add the event listener
    window.addEventListener("unhandledrejection", errorHandler);

    // Emit the update event to the parent component
    emit("update", updatedItems);

    // Close the modal after a short delay, but only if there was no error
    setTimeout(() => {
      // Remove the event listener
      window.removeEventListener("unhandledrejection", errorHandler);

      // Only close if no error occurred
      if (shouldClosePopup) {
        isEditing.value = false;
      }
    }, 1000);
  } catch (error) {
    // If there was an error, the modal stays open
    console.error("Error in SectionCard:", error);
  }
}
</script>
