<script setup>
import { defineProps } from "vue";

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  column1Label: { type: String, default: "Pays" },
  column2Label: { type: String, default: "Pourcentage" },
});
</script>

<template>
  <table class="table table-bordered table-nowrap align-middle w-full">
    <thead>
      <tr>
        <th scope="col">{{ column1Label }}</th>
        <th scope="col">{{ column2Label }}</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(row, i) in data" :key="i" class="h-16">
        <td class="flex items-center">
          <img 
            v-if="row.flagUrl" 
            :src="row.flagUrl" 
            :alt="row.country" 
            class="w-8 h-8 rounded-full mr-3"
          />
          {{ row.country }}
        </td>
        <td class="text-center">{{ row.count }}%</td>
      </tr>
    </tbody>
  </table>
</template>
