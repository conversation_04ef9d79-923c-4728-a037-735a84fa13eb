<template>
  <div>
    <!-- Error toast -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Success toast -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />

    <!-- R&D Projects Section -->
    <div class="mb-6">
      <ProjectsInitiativesCard
        title="Principaux projets R&D"
        type="rd-project"
        :items="rdProjects"
        @update="handleRdProjectsUpdate"
      />
    </div>

    <!-- ESG Initiatives Section -->
    <div class="mb-6">
      <ProjectsInitiativesCard
        title="Initiatives ESG spécifiques en 2023 - 2024"
        type="initiative"
        :items="initiatives"
        @update="handleInitiativesUpdate"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import Toast from "./Toast.vue";
import ProjectsInitiativesCard from "./ProjectsInitiativesCard.vue";

// State
const errorMessage = ref("");
const successMessage = ref("");
const rdProjects = ref([]);
const initiatives = ref([]);

// Get organizationId from route params
const route = useRoute();
const organizationId = route.params.organizationId;

// Mock service for demonstration
const mockService = {
  async getRdProjects(orgId) {
    // Simulate API call
    console.log(`Fetching R&D projects for organization ${orgId}`);
    return {
      data: [
        {
          id: "1",
          name: "Projet 1",
          description: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
          objectif: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
        },
        {
          id: "2",
          name: "Projet 2",
          description: "Description du projet 2",
          objectif: "Objectifs du projet 2"
        }
      ]
    };
  },
  
  async getInitiatives(orgId) {
    // Simulate API call
    console.log(`Fetching initiatives for organization ${orgId}`);
    return {
      data: [
        {
          id: "1",
          name: "Initiative 1",
          impact: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
        }
      ]
    };
  },
  
  async updateRdProjects(orgId, projects) {
    // Simulate API call
    console.log(`Updating R&D projects for organization ${orgId}`, projects);
    return { success: true };
  },
  
  async updateInitiatives(orgId, initiatives) {
    // Simulate API call
    console.log(`Updating initiatives for organization ${orgId}`, initiatives);
    return { success: true };
  }
};

// Load data
async function loadData() {
  try {
    // Load R&D projects
    const rdProjectsResponse = await mockService.getRdProjects(organizationId);
    rdProjects.value = rdProjectsResponse.data;
    
    // Load initiatives
    const initiativesResponse = await mockService.getInitiatives(organizationId);
    initiatives.value = initiativesResponse.data;
  } catch (error) {
    console.error("Error loading data:", error);
    errorMessage.value = "Erreur lors du chargement des données";
  }
}

// Handle R&D projects update
async function handleRdProjectsUpdate(updatedProjects) {
  try {
    await mockService.updateRdProjects(organizationId, updatedProjects);
    rdProjects.value = updatedProjects;
    successMessage.value = "Projets R&D mis à jour avec succès";
  } catch (error) {
    console.error("Error updating R&D projects:", error);
    errorMessage.value = "Erreur lors de la mise à jour des projets R&D";
    throw error; // Re-throw to keep the popup open
  }
}

// Handle initiatives update
async function handleInitiativesUpdate(updatedInitiatives) {
  try {
    await mockService.updateInitiatives(organizationId, updatedInitiatives);
    initiatives.value = updatedInitiatives;
    successMessage.value = "Initiatives ESG mises à jour avec succès";
  } catch (error) {
    console.error("Error updating initiatives:", error);
    errorMessage.value = "Erreur lors de la mise à jour des initiatives ESG";
    throw error; // Re-throw to keep the popup open
  }
}

// Load data on component mount
onMounted(loadData);
</script>
