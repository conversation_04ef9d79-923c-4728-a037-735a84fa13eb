<template>
  <!-- Modal Overlay -->
  <div
    class="fixed inset-0 flex items-center justify-center p-12 z-50 bg-black bg-opacity-10"
  >
    <!-- Modal Card -->
    <div class="bg-white shadow-lg rounded-xl max-w-full relative p-6 w-2/3">
      <!-- <PERSON> (Top Right) -->
      <button
        @click="$emit('cancel')"
        class="absolute top-3 right-3 text-gray-600 hover:text-gray-800"
      >
        <!-- X icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414
            1.414L8.586 10l-6.293 6.293a1 1 0
            001.414 1.414L10 11.414l6.293 6.293a1
            1 0 001.414-1.414L11.414 10l6.293-6.293a1
            1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Title -->
      <h2 class="!text-lg font-semibold mb-6">{{ title }}</h2>

      <!-- Form Fields -->
      <div class="space-y-4 max-h-[550px] overflow-y-auto">
        <!-- Text Fields -->
        <div class="grid grid-cols-1 gap-4">
          <!-- Environment Fields -->
          <template v-if="title === 'Environnement'">
            <!-- Electricity Provider -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Fournisseur d'électricité
              </label>
              <input
                v-model="localData.electricity"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
              />
            </div>

            <!-- Electricity Consumption -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Votre consommation d'électricité par an est estimée à
              </label>
              <input
                v-model="localData.electricityConsumption"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                placeholder="Ex: [100,500] MW"
              />
            </div>

            <!-- Water Purification Plant -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Disposez-vous d'une station de purification d'eau
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasWaterPlant"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">Oui</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasWaterPlant"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">Non</span>
                </label>
              </div>
            </div>

            <!-- Water Consumption -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Votre consommation d'eau par an est estimée à
              </label>
              <input
                v-model="localData.waterConsumption"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                placeholder="Ex: [1000,1500] m3"
              />
            </div>

            <!-- Recyclable Percentage -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Votre produit est-il recyclable à
              </label>
              <input
                v-model="localData.recyclablePercentage"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                placeholder="Ex: [20%,50%]"
              />
            </div>

            <!-- Eco-Design -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                La conception de votre produit respect-elle l'aspect de
                l'eco-design ?
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.ecoDesigned"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">Oui</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.ecoDesigned"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">Non</span>
                </label>
              </div>
            </div>

            <!-- Internal Waste Revaluation -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Revalorisation interne des déchets
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.internalRevaluation"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">Oui</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.internalRevaluation"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">Non</span>
                </label>
              </div>
            </div>

            <!-- Local Recovery Rate -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Taux des déchets valorisés localement
              </label>
              <input
                v-model="localData.localRecoveryRate"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                placeholder="Ex: 50%"
              />
            </div>

            <!-- Export Rate -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Taux des déchets réexportés
              </label>
              <input
                v-model="localData.exportRate"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                placeholder="Ex: 20%"
              />
            </div>
          </template>

          <!-- Innovation Fields -->
          <template v-else-if="title === 'Innovation'">
            <!-- Production Integration Rate -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Estimation du taux moyen d'intégration local de production
              </label>
              <input
                v-model="localData.productionIntegrationRate"
                type="text"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                placeholder="Ex: [21-40%]"
              />
            </div>

            <!-- Develop New Products -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Prévoyez- vous de développer de nouveaux produits ?
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasDevelopProducts"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">OUI</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasDevelopProducts"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">NON</span>
                </label>
              </div>
            </div>

            <!-- Develop New Processes -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Prévoyez- vous de développer de nouveaux process ?
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasDevelopProcesses"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">OUI</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasDevelopProcesses"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">NON</span>
                </label>
              </div>
            </div>

            <!-- Develop New Markets -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Prévoyez-vous de développer de nouveaux marchés ?
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasDevelopMarkets"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">OUI</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasDevelopMarkets"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">NON</span>
                </label>
              </div>
            </div>

            <!-- Open Innovation -->
            <div class="mb-4">
              <label class="block text-sm font-semibold text-gray-700 mb-1">
                Pratiquez-vous l'open innovation dans votre entreprise ?
              </label>
              <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasOpenInnovation"
                    :value="true"
                    class="form-radio"
                  />
                  <span class="ml-2">OUI</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    type="radio"
                    v-model="localData.hasOpenInnovation"
                    :value="false"
                    class="form-radio"
                  />
                  <span class="ml-2">NON</span>
                </label>
              </div>
            </div>
          </template>

          <!-- Default case - show all fields -->
          <template v-else>
            <!-- All fields would go here, but we're not using this case -->
            <div class="text-center text-gray-500 italic">
              Please specify a valid title (Environnement or Innovation)
            </div>
          </template>
        </div>
      </div>

      <!-- Save Button -->
      <div class="mt-6 flex justify-end">
        <button
          @click="saveChanges"
          class="bg-[#C62027] text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
        >
          Sauvegarder
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Environnement",
  },
  environmentData: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["save", "cancel"]);

// Create a local copy of the data to avoid mutating props directly
const localData = ref({
  electricity: "",
  electricityConsumption: "",
  hasWaterPlant: false,
  waterConsumption: "",
  recyclablePercentage: "",
  ecoDesigned: false,
  internalRevaluation: false,
  localRecoveryRate: "",
  exportRate: "",
  productionIntegrationRate: "",
  hasDevelopProducts: false,
  hasDevelopProcesses: false,
  hasDevelopMarkets: false,
  hasOpenInnovation: false,
});

// Initialize with props data
onMounted(() => {
  if (props.environmentData) {
    localData.value = { ...props.environmentData };
  }
});

// Save changes and emit to parent
async function saveChanges() {
  try {
    await emit("save", localData.value);
    // The parent component will close the modal on success
  } catch (error) {
    // If an error is thrown from the parent component, keep the modal open
    console.error("Error in save:", error);
  }
}
</script>
