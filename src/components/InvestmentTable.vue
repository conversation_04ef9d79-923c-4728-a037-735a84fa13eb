<template>
  <div class="bg-white rounded-2xl shadow-sm p-6">
    <!-- header -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="!text-lg font-medium !text-gray-700">
        {{ title }}
      </h3>
      <button
        v-if="showEditButton"
        @click="$emit('edit')"
        class="p-2 rounded hover:bg-gray-100 focus:outline-none"
        aria-label="Edit table data"
      >
        <!-- pencil icon -->
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 4H6.5C5.39543 4 4.5 4.89543 4.5 6V18C4.5 19.1046 5.39543 20 6.5 20H18.5C19.6046 20 20.5 19.1046 20.5 18V12M9.5 15V12.5L18.25 3.75C18.9404 3.05964 20.0596 3.05964 20.75 3.75V3.75C21.4404 4.44036 21.4404 5.55964 20.75 6.25L16 11L12 15H9.5Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- table with grid lines like in the screenshot -->
    <div class="table-container">
      <table class="w-full table-auto border-collapse border border-gray-200">
        <thead>
          <tr>
            <th
              class="border border-gray-200 text-left px-4 py-3 font-semibold text-gray-700 w-1/2"
            >
              Projets
            </th>
            <th
              class="border border-gray-200 text-left px-4 py-3 font-semibold text-gray-700 w-1/2"
            >
              Montant
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in data" :key="index">
            <td class="border border-gray-200 px-4 py-3 text-left">
              {{ item.name }}
            </td>
            <td class="border border-gray-200 px-4 py-3 text-left">
              {{ formatCurrency(parseFloat(item.value) || 0) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  /** Array of investment data: { name: string, value: string, type: string } */
  data: {
    type: Array,
    default: () => [],
  },
  /** Card title */
  title: {
    type: String,
    default: "Projets d'investissement prévus pour 2024",
  },
  /** Show edit button */
  showEditButton: {
    type: Boolean,
    default: true,
  },
});

// emit an event when the edit button is clicked
const emit = defineEmits(["edit"]);

/**
 * Format a number as Tunisian Dinar
 */
function formatCurrency(value) {
  if (typeof value === "number") {
    return value.toLocaleString("fr-FR") + " TND";
  }
  return value;
}
</script>

<style scoped>
.table-container {
  width: 100%;
  overflow-x: auto;
}

/* Table styling to match the screenshot */
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  text-align: left;
}

th {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  background-color: #fff;
}

td {
  font-size: 1rem;
  color: #333;
}

/* Remove any background colors */
tr {
  background-color: #fff !important;
}

/* Ensure proper alignment */
.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

/* Make the table match the screenshot */
.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

/* Adjust padding to match screenshot */
.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}
</style>
