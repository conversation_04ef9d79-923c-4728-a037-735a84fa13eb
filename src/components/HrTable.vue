<!-- src/components/HRTable.vue -->
<script setup>
import { defineProps } from "vue";

const props = defineProps({
  /** array of rows: { type: string, femmes: string|number, hommes: string|number } */
  data: {
    type: Array,
    default: () => [],
  },
  /** override column headers if you like */
  column1Label: { type: String, default: "Type de contrat" },
  column2Label: { type: String, default: "Femmes" },
  column3Label: { type: String, default: "Hommes" },
});
</script>

<template>
  <table class="table table-bordered table-nowrap align-middle w-full">
    <thead>
      <tr>
        <th scope="col">{{ column1Label }}</th>
        <th scope="col">{{ column2Label }}</th>
        <th scope="col">{{ column3Label }}</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(row, i) in data" :key="i" class="h-20">
        <td>{{ row.type }}</td>
        <td class="text-center">{{ row.femmes }}</td>
        <td class="text-center">{{ row.hommes }}</td>
      </tr>
    </tbody>
  </table>
</template>
