<!-- src/components/InvestmentTable.vue -->
<template>
  <div class="bg-white rounded-2xl shadow-sm p-6">
    <!-- header -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">
        {{ title }}
      </h3>
      <button
        @click="$emit('edit')"
        class="p-2 rounded hover:bg-gray-100 focus:outline-none"
        aria-label="Edit investment plan"
      >
        <!-- simple pencil icon -->
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- table -->
    <table class="w-full table-auto border-collapse">
      <thead>
        <tr class="bg-gray-50">
          <th class="text-left px-4 py-2 font-semibold text-gray-700">
            {{ column1Label }}
          </th>
          <th class="text-right px-4 py-2 font-semibold text-gray-700">
            {{ column2Label }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(row, idx) in data"
          :key="idx"
          class="border-t hover:bg-gray-50 transition-colors"
        >
          <td class="px-4 py-3">{{ row.project }}</td>
          <td class="px-4 py-3 text-right">
            {{ formatCurrency(row.amount) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  /** Array of rows: { project: string, amount: number } */
  data: {
    type: Array,
    default: () => [],
  },
  /** Card title */
  title: {
    type: String,
    default: "Projets d’investissement prévus pour 2024",
  },
  /** Column headers */
  column1Label: { type: String, default: "Projets" },
  column2Label: { type: String, default: "Montant" },
});

// emit an event when the edit button is clicked
const emit = defineEmits(["edit"]);

/**
 * format a number as Tunisian Dinar
 */
function formatCurrency(value) {
  if (typeof value === "number") {
    return value.toLocaleString("fr-FR") + " TND";
  }
  return value;
}
</script>

<style scoped>
/* you can override or extend further if needed */
</style>
