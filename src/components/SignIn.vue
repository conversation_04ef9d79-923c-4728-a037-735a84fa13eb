<template>
  <BRow class="g-0 min-vh-100">
    <!-- Left Half - Centered Logo -->
    <BCol
      md="6"
      class="d-none d-md-flex align-items-center justify-content-center position-relative"
    >
      <div class="w-100 h-100 d-flex align-items-center justify-content-center">
        <img
          src="/TAA_logo.png"
          alt="TAA Logo"
          class="img-fluid"
          style="max-height: 95%; max-width: 95%"
        />
      </div>
    </BCol>

    <!-- Right Half - Form Container -->
    <BCol
      md="6"
      class="d-flex align-items-center justify-content-center bg-[#1E1E1E] py-5"
    >
      <div class="w-100" style="max-width: 480px">
        <BCard no-body class="shadow-lg !rounded-2xl py-12">
          <div class="text-center mb-5">
            <p class="text-muted mb-0 text-sm">
              Connectez vous pour continuer vers TAA
            </p>
          </div>
          <BCardBody class="p-5 m-0">
            <form @submit.prevent="tryToLogIn">
              <div class="mb-2">
                <p class="font-bold mb-2 text-base">Content de te revoir!</p>
                <!-- Email Field -->
                <div class="mb-2">
                  <label for="email" class="form-label text-xs ml-4"
                    >Email</label
                  >
                  <input
                    type="email"
                    id="email"
                    class="form-control !bg-[#F2F2F2] border-0 placeholder:text-xs"
                    placeholder="Entrez votre adresse professionelle"
                    v-model="formData.email"
                    @input="updateError('email')"
                  />
                  <span v-if="errors.email" class="text-xs text-danger ml-4">
                    {{ errors.email }}
                  </span>
                </div>

                <!-- Password Field -->
                <div class="mb-2">
                  <label for="password-input" class="form-label text-xs ml-4"
                    >Password</label
                  >
                  <div class="position-relative auth-pass-inputgroup">
                    <input
                      type="password"
                      id="password-input"
                      class="form-control pe-5 !bg-[#F2F2F2] border-0 placeholder:text-xs"
                      placeholder="Entez le mot de passe"
                      v-model="formData.password"
                      @input="updateError('password')"
                    />
                    <BButton
                      variant="link"
                      class="position-absolute end-0 top-0 text-decoration-none text-muted"
                      type="button"
                    >
                      <i class="ri-eye-fill align-middle"></i>
                    </BButton>
                  </div>
                  <span v-if="errors.password" class="text-xs text-danger ml-4">
                    {{ errors.password }}
                  </span>
                </div>

                <div
                  class="d-flex justify-content-between align-items-baseline mb-4"
                >
                  <div
                    class="form-check form-switch form-switch-right form-switch-md"
                  >
                    <label
                      for="rememberMe"
                      class="form-label text-xs font-medium"
                    >
                      Souviens-toi de moi
                    </label>
                    <input
                      class="form-check-input code-switcher checked:!bg-[#C62027] checked:!border-[#C62027] focus:!ring-[#C62027] focus:!border-[#C62027]"
                      type="checkbox"
                      id="rememberMe"
                    />
                  </div>
                  <router-link
                    to="/forgot_password"
                    class="text-muted text-decoration-none small !text-xs !text-[#C62027]"
                  >
                    Mot de passe oublié ?
                  </router-link>
                </div>

                <!-- Server or Validation Error Message -->
                <div class="mb-2 ml-4">
                  <span v-if="authError" class="text-danger text-xs">{{
                    authError
                  }}</span>
                  <span v-if="successMessage" class="text-success text-xs">{{
                    successMessage
                  }}</span>
                </div>

                <BButton
                  variant="success"
                  class="w-100 !bg-[#C62027] !font-bold !rounded-[40px] py-2 !border-none mb-4"
                  type="submit"
                  :disabled="!isFormValid || processing"
                >
                  Se connecter
                </BButton>
              </div>
            </form>
          </BCardBody>

          <footer class="mt-4 pt-4 text-center">
            <p class="text-muted small mb-0">
              &copy; {{ new Date().getFullYear() }} Tunisian Automotive
              Association
            </p>
          </footer>
        </BCard>
      </div>
    </BCol>
  </BRow>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import { useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required, email as emailValidator } from "@vuelidate/validators";
import authService from "../Services/services/auth";
import userDetailsService from "../Services/services/userDetails";

const router = useRouter();

const formData = reactive({
  email: "",
  password: "",
});

const errors = reactive({
  email: "",
  password: "",
});

const rules = {
  email: { required, email: emailValidator },
  password: { required },
};

const v$ = useVuelidate(rules, formData);

const errorMessages = {
  email: {
    required: "Email is required",
    email: "Please enter a valid email",
  },
  password: {
    required: "Password is required",
  },
};

function updateError(fieldName) {
  v$.value[fieldName].$touch();
  const fieldState = v$.value[fieldName];
  if (fieldState.$invalid) {
    if (fieldState.$errors.length > 0) {
      const validator = fieldState.$errors[0].$validator;
      errors[fieldName] =
        errorMessages[fieldName][validator] || "Unknown error.";
    }
  } else {
    errors[fieldName] = "";
  }
}

const isFormValid = computed(() => {
  const allFilled = Object.keys(formData).every(
    (key) => formData[key].toString().trim() !== ""
  );
  return allFilled && Object.values(errors).every((msg) => msg === "");
});

const processing = ref(false);
const authError = ref("");
const successMessage = ref("");

async function tryToLogIn() {
  v$.value.$touch();
  Object.keys(formData).forEach((field) => updateError(field));
  if (!isFormValid.value) {
    return;
  }
  processing.value = true;
  authError.value = "";
  successMessage.value = "";

  try {
    const response = await authService.signIn(formData);
    // Assuming response.data.token exists from your SignInResponse
    successMessage.value = " signed in successfully!";
    localStorage.setItem("token", response.data.token.accessToken);
    console.log("Signed in successfully:", response.data);
    const userDetailsResponse = await userDetailsService.getUserDetails();
    console.log("User details fetched successfully:", userDetailsResponse.data);

    // Check if user is superadmin and redirect accordingly
    if (userDetailsResponse.data.user.isSuperAdmin) {
      console.log("User is superadmin, redirecting to entreprises list");
      setTimeout(() => {
        router.push("/entreprises");
      }, 2000);
    } else {
      const organizationId = userDetailsResponse.data.organization.id;
      console.log("Organization ID:", organizationId);
      setTimeout(() => {
        router.push(`/entrepriseinfo/${organizationId}`);
      }, 2000);
    }
    // Redirect after 2 seconds
  } catch (error) {
    authError.value =
      error.response?.data?.errorMessage || "Une erreur est survenue.";
    console.error("Error signing in:", error);
  } finally {
    processing.value = false;
  }
}
</script>

<style scoped>
/* Add any additional styles if needed */
</style>
