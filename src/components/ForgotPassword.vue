<template>
  <BRow class="g-0 min-vh-100">
    <!-- Left Half - Centered Logo -->
    <BCol
      md="6"
      class="d-none d-md-flex align-items-center justify-content-center position-relative"
    >
      <div class="w-100 h-100 d-flex align-items-center justify-content-center">
        <img
          src="/TAA_logo.png"
          alt="TAA Logo"
          class="img-fluid"
          style="max-height: 95%; max-width: 95%"
        />
      </div>
    </BCol>

    <!-- Right Half - Form Container -->
    <BCol
      md="6"
      class="d-flex align-items-center justify-content-center bg-[#1E1E1E] py-5"
    >
      <div class="w-100" style="max-width: 480px">
        <BCard no-body class="shadow-lg !rounded-2xl py-12">
          <div class="text-center mb-5">
            <p class="text-muted mb-0 text-sm">
              Réinitialisez votre mot de passe pour continuer vers TAA
            </p>
          </div>
          <BCardBody class="p-5 m-0">
            <form @submit.prevent="tryToForgotPassword">
              <div class="mb-2">
                <p class="font-bold mb-2 text-base">Entrez votre email</p>
                <!-- Email Field -->
                <div class="mb-2">
                  <label for="email" class="form-label text-xs ml-4">Email</label>
                  <input
                    type="email"
                    id="email"
                    class="form-control !bg-[#F2F2F2] border-0 placeholder:text-xs"
                    placeholder="Entrez votre adresse professionnelle"
                    v-model="formData.email"
                    @input="updateError('email')"
                  />
                  <span v-if="errors.email" class="text-xs text-danger ml-4">
                    {{ errors.email }}
                  </span>
                </div>

                <!-- Server or Validation Error Message -->
                <div class="mb-2 ml-4">
                  <span v-if="authError" class="text-danger text-xs">
                    {{ authError }}
                  </span>
                  <span v-if="successMessage" class="text-success text-xs">
                    {{ successMessage }}
                  </span>
                </div>

                <BButton
                  variant="success"
                  class="w-100 !bg-[#C62027] !font-bold !rounded-[40px] py-2 !border-none !mt-8"
                  type="submit"
                  :disabled="!isFormValid || processing"
                >
                  Continuer
                </BButton>
              </div>
            </form>
          </BCardBody>

          <footer class="mt-4 pt-4 text-center">
            <p class="text-muted small mb-0">
              &copy; {{ new Date().getFullYear() }} Tunisian Automotive Association
            </p>
          </footer>
        </BCard>
      </div>
    </BCol>
  </BRow>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import { useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required, email as emailValidator } from "@vuelidate/validators";
import authService from "../Services/services/auth";

const router = useRouter();

const formData = reactive({
  email: "",
});

const errors = reactive({
  email: "",
});

const rules = {
  email: { required, email: emailValidator },
};

const v$ = useVuelidate(rules, formData);

const errorMessages = {
  email: {
    required: "Email is required",
    email: "Please enter a valid email",
  },
};

function updateError(fieldName) {
  v$.value[fieldName].$touch();
  const fieldState = v$.value[fieldName];
  if (fieldState.$invalid) {
    if (fieldState.$errors.length > 0) {
      const validator = fieldState.$errors[0].$validator;
      errors[fieldName] = errorMessages[fieldName][validator] || "Unknown error.";
    }
  } else {
    errors[fieldName] = "";
  }
}

const isFormValid = computed(() => {
  const allFilled = Object.keys(formData).every(
    (key) => formData[key].toString().trim() !== ""
  );
  return allFilled && Object.values(errors).every((msg) => msg === "");
});

const processing = ref(false);
const authError = ref("");
const successMessage = ref("");

async function tryToForgotPassword() {
  v$.value.$touch();
  Object.keys(formData).forEach((field) => updateError(field));
  if (!isFormValid.value) {
    return;
  }
  processing.value = true;
  authError.value = "";
  successMessage.value = "";

  try {
    await authService.forgotPassword(formData);
    successMessage.value = "Email sent! Please check your inbox.";
    // After 2 seconds, redirect to the reset password page
  } catch (error) {
    authError.value = error.response?.data?.errorMessage || "An error occurred.";
    console.error("Error with forgot password:", error);
  } finally {
    processing.value = false;
  }
}
</script>

<style scoped>
/* You can adjust styles as needed */
</style>
