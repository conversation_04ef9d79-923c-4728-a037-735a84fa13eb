<template>
  <div class="card mb-6 shadow rounded-xl p-4 w-full">
    <div class="mb-4">
      <!-- Edit button -->
      <button
        @click="openEditModal"
        class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
      >
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- Environment Data Table -->
    <table class="table table-bordered table-nowrap align-middle w-full">
      <tbody>
        <tr v-for="(row, index) in tableData" :key="index" class="h-16">
          <td class="w-1/2">{{ row.label }}</td>
          <td class="w-1/2">
            <template v-if="typeof row.value === 'boolean'">
              {{ row.value ? "OUI" : "NON" }}
            </template>
            <template v-else>
              {{ row.value }}
            </template>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Edit Modal -->
    <EditEnvironmentModal
      v-if="showEditModal"
      :title="title"
      :environmentData="environmentData"
      @save="handleSave"
      @cancel="showEditModal = false"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import EditEnvironmentModal from "./EditEnvironmentModal.vue";

const props = defineProps({
  title: {
    type: String,
    default: "Environnement",
  },
  environmentData: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["update"]);
const showEditModal = ref(false);

// Transform the environment data into a format suitable for the table
const tableData = computed(() => {
  if (!props.environmentData) return [];

  // Different fields based on card type
  if (props.title === "Environnement") {
    return [
      {
        label: "Fournisseur d'électricité",
        value: props.environmentData.electricity || "N/A",
      },
      {
        label: "Votre consommation d'électricité par an est estimée à",
        value: props.environmentData.electricityConsumption || "N/A",
      },
      {
        label: "Disposez-vous d'une station de purification d'eau",
        value: props.environmentData.hasWaterPlant,
      },
      {
        label: "Votre consommation d'eau par an est estimée à",
        value: props.environmentData.waterConsumption || "N/A",
      },
      {
        label: "Votre produit est-il recyclable à",
        value: props.environmentData.recyclablePercentage || "N/A",
      },
      {
        label:
          "La conception de votre produit respect-elle l'aspect de l'eco-design ?",
        value: props.environmentData.ecoDesigned,
      },
      {
        label: "Revalorisation interne des déchets",
        value: props.environmentData.internalRevaluation,
      },
      {
        label: "Taux des déchets valorisés localement",
        value: props.environmentData.localRecoveryRate || "N/A",
      },
      {
        label: "Taux des déchets réexportés",
        value: props.environmentData.exportRate || "N/A",
      },
    ];
  } else if (props.title === "Innovation") {
    return [
      {
        label: "Estimation du taux moyen d'intégration local de production",
        value: props.environmentData.productionIntegrationRate || "N/A",
      },
      {
        label: "Prévoyez- vous de développer de nouveaux produits ?",
        value: props.environmentData.hasDevelopProducts,
      },
      {
        label: "Prévoyez- vous de développer de nouveaux process ?",
        value: props.environmentData.hasDevelopProcesses,
      },
      {
        label: "Prévoyez-vous de développer de nouveaux marchés ?",
        value: props.environmentData.hasDevelopMarkets,
      },
      {
        label: "Pratiquez-vous l'open innovation dans votre entreprise ?",
        value: props.environmentData.hasOpenInnovation,
      },
    ];
  } else {
    // Default case - show all fields
    return [
      {
        label: "Fournisseur d'électricité",
        value: props.environmentData.electricity || "N/A",
      },
      {
        label: "Votre consommation d'électricité par an est estimée à",
        value: props.environmentData.electricityConsumption || "N/A",
      },
      {
        label: "Disposez-vous d'une station de purification d'eau",
        value: props.environmentData.hasWaterPlant,
      },
      {
        label: "Votre consommation d'eau par an est estimée à",
        value: props.environmentData.waterConsumption || "N/A",
      },
      {
        label: "Votre produit est-il recyclable à",
        value: props.environmentData.recyclablePercentage || "N/A",
      },
      {
        label:
          "La conception de votre produit respect-elle l'aspect de l'eco-design ?",
        value: props.environmentData.ecoDesigned,
      },
      {
        label: "Revalorisation interne des déchets",
        value: props.environmentData.internalRevaluation,
      },
      {
        label: "Taux des déchets valorisés localement",
        value: props.environmentData.localRecoveryRate || "N/A",
      },
      {
        label: "Taux des déchets réexportés",
        value: props.environmentData.exportRate || "N/A",
      },
      {
        label: "Estimation du taux moyen d'intégration local de production",
        value: props.environmentData.productionIntegrationRate || "N/A",
      },
      {
        label: "Prévoyez- vous de développer de nouveaux produits ?",
        value: props.environmentData.hasDevelopProducts,
      },
      {
        label: "Prévoyez- vous de développer de nouveaux process ?",
        value: props.environmentData.hasDevelopProcesses,
      },
      {
        label: "Prévoyez-vous de développer de nouveaux marchés ?",
        value: props.environmentData.hasDevelopMarkets,
      },
      {
        label: "Pratiquez-vous l'open innovation dans votre entreprise ?",
        value: props.environmentData.hasOpenInnovation,
      },
    ];
  }
});

function openEditModal() {
  showEditModal.value = true;
}

async function handleSave(updatedData) {
  try {
    emit("update", updatedData);
    showEditModal.value = false;
  } catch (error) {
    console.error("Error saving environment data:", error);
  }
}
</script>
