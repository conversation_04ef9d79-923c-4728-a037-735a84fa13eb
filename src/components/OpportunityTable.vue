<template>
  <div class="card mb-3 border-0 bg-white shadow !rounded-2xl">
    <div class="card-body p-4">
      <!-- Card Header -->
      <div class="flex justify-between items-center mb-4">
        <h4 class="card-title mb-0 font-semibold">{{ title }}</h4>
        <button class="edit-button" @click="$emit('edit')">
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.5 4H6.5C5.39543 4 4.5 4.89543 4.5 6V18C4.5 19.1046 5.39543 20 6.5 20H18.5C19.6046 20 20.5 19.1046 20.5 18V12M9.5 15V12.5L18.25 3.75C18.9404 3.05964 20.0596 3.05964 20.75 3.75V3.75C21.4404 4.44036 21.4404 5.55964 20.75 6.25L16 11L12 15H9.5Z"
              stroke="black"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Opportunities Table -->
      <div class="table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th scope="col" class="w-[25%]">Catégorie</th>
              <th scope="col" class="w-[50%]">Description des besoins</th>
              <th scope="col" class="w-[25%] text-center">Priorité</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(opportunity, index) in opportunities" :key="index">
              <td>{{ opportunity.category }}</td>
              <td>{{ opportunity.description }}</td>
              <td class="text-center">
                <span
                  class="badge"
                  :class="{
                    'bg-success': opportunity.priority === 'LOW',
                    'bg-warning': opportunity.priority === 'MEDIUM',
                    'bg-danger': opportunity.priority === 'HIGH',
                  }"
                >
                  {{ formatPriority(opportunity.priority) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Opportunités et besoins futurs",
  },
  opportunities: {
    type: Array,
    default: () => [],
  },
});

// Format priority for display
function formatPriority(priority) {
  const priorityMap = {
    LOW: "LOW",
    MEDIUM: "MEDIUM",
    HIGH: "HIGH",
  };
  return priorityMap[priority] || priority;
}
</script>

<style scoped>
.table {
  width: 100%;
  margin-bottom: 0;
  color: #212529;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.5rem 0.75rem;
  vertical-align: middle;
}

.table-bordered {
  border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
  border: 1px solid #dee2e6;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 1px solid #dee2e6;
  background-color: #ffffff;
  font-weight: 500;
}

/* Removed striped background */

.badge {
  display: inline-block;
  padding: 0.25em 1.5em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  color: white;
  width: 100px;
}

.bg-success {
  background-color: #0ab39c !important;
}

.bg-warning {
  background-color: #f7b84b !important;
}

.bg-danger {
  background-color: #f06548 !important;
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.edit-button {
  background: none;
  border: none;
  cursor: pointer;
}
</style>
