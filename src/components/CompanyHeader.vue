<template>
  <!-- Company Header -->
  <div class="p-8 bg-white shadow rounded mb-4 relative">
    <div class="flex items-center justify-between mb-10">
      <!-- Left: Company Logo + Basic Info -->
      <div class="flex items-center -mt-4">
        <img
          :src="imageSrc"
          alt="company logo"
          class="w-30 h-30 !rounded-full mr-4 border-2"
        />
        <div class="ml-12">
          <h2 class="font-bold !text-lg mb-1 !text-gray-800">
            {{ companyName }}
          </h2>
          <div class="text-xs text-gray-600 space-x-24 mt-4">
            <span
              >Country:
              <span class="font-semibold ml-4"> {{ country }}</span></span
            >
            <span
              >Foundation Year:
              <span class="font-semibold ml-4">{{ foundationYear }}</span></span
            >
            <span>
              Website:
              <a
                :href="website"
                target="_blank"
                class="!text-gray-600 !no-underline !font-semibold ml-4"
              >
                {{ website }}
              </a>
            </span>
          </div>
        </div>
      </div>

      <div class="-mt-4">
        <BProgress
          :value="progressValue"
          variant="danger"
          style="height: 6px"
          class="w-3xs mb-3"
        />
        <span class="text-sm text-gray-700 mt-4">
          Your profile is
          <strong class="text-[#C62027]">{{ progressValue }}%</strong>
          complete
        </span>
      </div>
    </div>
    <nav
      class="flex space-x-6 gap-x-24 text-sm border-t pt-4 mt-4 -mb-8 border-gray-50"
    >
      <button
        v-for="tab in tabs"
        :key="tab.key"
        @click="selectTab(tab)"
        class="relative pb-2"
        :class="[
          'text-sm',
          activeTab === tab.key
            ? 'border-b-2 border-red-500 text-red-500 font-semibold'
            : 'text-gray-600 hover:text-gray-800 ',
        ]"
      >
        {{ tab.label }}
      </button>
    </nav>
    <button
      @click="openPreview"
      class="fix absolute bottom-4 right-4 !text-xs !font-bold !bg-[#C62027] !rounded-full ! text-white px-4 py-2"
    >
      Preview
    </button>
  </div>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
const props = defineProps({
  imageURL: {
    type: String,
  },
  companyName: { type: String },
  country: { type: String },
  foundationYear: { type: Number },
  website: { type: String },
  progressValue: { type: Number },
});

const tabs = [
  { label: "General", key: "General", routeName: "general" },
  { label: "Products", key: "products", routeName: "products" },
  { label: "Human Resources", key: "human-resources", routeName: "hr" },
  { label: "Revenue", key: "revenue", routeName: "revenue" },
  { label: "Extras", key: "Extras", routeName: "extras" },
  { label: "Autre", key: "Autre", routeName: "Autre" },
  { label: "Opportunity", key: "Opportunity", routeName: "opportunity" },
];

const imageSrc = computed(() => {
  // Append timestamp to bypass cache
  return props.imageURL
    ? `${props.imageURL}?t=${Date.now()}`
    : "https://img.freepik.com/vecteurs-libre/logo-fleche-boucles_1043-146.jpg?t=st=1743113378~exp=1743116978~hmac=724382f13387ff6691087f07e5690c7caeb3bb28f643779934fa9b087dc58106&w=740";
});

// Track the currently active tab
const activeTab = ref("General");
const router = useRouter();
const route = useRoute();
// Emit event to parent when a tab changes or preview is opened
const emit = defineEmits(["tab-changed", "open-preview"]);

async function selectTab(tab) {
  try {
    await router.push({
      name: tab.routeName,
      params: { organizationId: route.params.organizationId },
    });
    activeTab.value = tab.key;
  } catch (error) {
    console.error("Navigation error:", error);
  }
}

// Function to open preview
function openPreview() {
  emit("open-preview");
}

// Watch the route to update activeTab when navigation occurs
watch(
  () => route.name,
  (newRouteName) => {
    const matchingTab = tabs.find((tab) => tab.routeName === newRouteName);
    if (matchingTab) {
      activeTab.value = matchingTab.key;
    }
  },
  { immediate: true }
);
</script>
