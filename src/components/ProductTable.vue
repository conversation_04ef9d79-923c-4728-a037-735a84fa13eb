<script setup>
import { defineProps } from "vue";

const props = defineProps({
  products: {
    type: Array,
    default: () => [],
  },
});
</script>

<template>
  <table class="table table-bordered table-nowrap align-middle">
    <thead>
      <tr>
        <th scope="col" class="w-[30%]">Produit</th>
        <th scope="col">Description</th>
        <th scope="col " class="w-[30%]">NGP</th>
      </tr>
    </thead>
    <tbody>
      <!-- Loop through the products prop -->
      <tr v-for="(product, index) in products" :key="index">
        <!-- Produit -->
        <td class="text-center">
          <!-- Product image on top -->
          <img
            :src="product.photoUrl"
            :alt="product.name"
            class="rounded mx-auto"
            style="width: 100px; height: auto"
          />
          <!-- Product name below the image -->
          <h6 class="mt-2 mb-0">{{ product.name }}</h6>
        </td>

        <!-- Description -->
        <td class="w-[40%] !overflow-hidden">
          <p class="mb-0 !text-wrap">
            {{ product.description }}
          </p>
        </td>

        <!-- NGP -->
        <td class="text-center">{{ product.ngp }}</td>

        <!-- Actions (Dropdown) -->
      </tr>
    </tbody>
  </table>
</template>
