<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3
      class="!text-[#C62027] border-b border-gray-400 pb-2 !font-bold !text-2xl mb-6"
    >
      {{ title }}
    </h3>

    <!-- Products Grid -->
    <div
      v-if="products && products.length > 0"
      class="grid grid-cols-3 gap-6 mt-4"
    >
      <div
        v-for="(product, index) in products"
        :key="index"
        class="border border-gray-300 rounded-lg p-4 bg-white"
      >
        <!-- Product Image -->
        <div
          class="mb-4 h-32 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden"
        >
          <img
            v-if="product.image"
            :src="product.image"
            :alt="product.name"
            class="w-full h-full object-cover"
          />
          <div
            v-else
            class="w-16 h-16 bg-gray-300 rounded-lg flex items-center justify-center"
          >
            <svg
              class="w-8 h-8 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              ></path>
            </svg>
          </div>
        </div>

        <!-- Product Name -->
        <h4
          class="font-semibold text-gray-800 border-b border-gray-400 pb-1 mb-2"
        >
          {{ product.name }}
        </h4>

        <!-- Product Description -->
        <p class="text-gray-500 text-sm">
          {{ product.description || "Product Description can be put here" }}
        </p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-gray-500 text-center py-8">
      <p>No products available</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  products: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each product should have name and optionally description and image
      return value.every((product) => "name" in product);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
