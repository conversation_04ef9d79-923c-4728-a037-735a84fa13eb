<template>
  <!-- Modal Overlay -->
  <div class="fixed inset-0 flex items-center justify-center p-12 z-50">
    <!-- Modal Card -->
    <div class="bg-white shadow-lg rounded-xl max-w-full relative p-6 w-1/3">
      <!-- <PERSON> (Top Right) -->
      <button
        @click="$emit('cancel')"
        class="absolute top-3 right-3 text-gray-600 hover:text-gray-800"
      >
        <!-- X icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414
            1.414L8.586 10l-6.293 6.293a1
            1 0 001.414 1.414L10 11.414l6.293 6.293a1
            1 0 001.414-1.414L11.414 10l6.293-6.293a1
            1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Title -->
      <h2 class="!text-xs !font-bold text-gray-700 mb-4">
        {{ title }}
      </h2>

      <!-- Partnerships List -->
      <div class="space-y-4">
        <!-- One row per partnership -->
        <div v-for="(item, index) in localItems" :key="index">
          <!-- Labels -->
          <div class="grid grid-cols-[1.5fr_1fr] gap-x-2 mb-1">
            <label class="block !text-sm !font-semibold text-gray-700"
              >Type de partenariat</label
            >
            <label class="block !text-sm !font-semibold text-gray-700"
              >Description</label
            >
          </div>

          <!-- Inputs -->
          <div class="grid grid-cols-[1.5fr_1fr_auto] gap-x-2 items-start">
            <!-- Partnership Type Input -->
            <input
              v-model="item.name"
              type="text"
              class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
              placeholder="Entrez le type de partenariat"
            />

            <!-- Description Input -->
            <input
              v-model="item.value"
              type="text"
              class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
              placeholder="Entrez la description"
            />

            <!-- Remove Button -->
            <div class="!border !border-[#C62027] p-2 ml-2 bg-red-50">
              <button
                class="text-[#C62027] hover:text-red-700"
                @click="removeItem(index)"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  class="w-5 h-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Add New Row Button -->
      <div class="flex justify-center mt-4 !border !border-[#C62027]">
        <button
          @click="addItem"
          class="bg-red-50 text-red-500 rounded-full px-4 py-2 w-full hover:bg-red-50 transition text-sm font-medium"
        >
          <svg
            width="15"
            height="15"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="mx-auto"
          >
            <path
              d="M14.1308 0.528433C14.4622 0.528358 14.7799 0.659881 15.0141 0.894078C15.2483 1.12828 15.3798 1.44597 15.3797 1.7773L15.3755 12.8977L26.4959 12.8935C26.6618 12.8905 26.8266 12.9206 26.9806 12.9821C27.1347 13.0435 27.275 13.135 27.3933 13.2513C27.5116 13.3675 27.6055 13.5062 27.6696 13.6592C27.7337 13.8122 27.7667 13.9764 27.7667 14.1423C27.7666 14.3082 27.7335 14.4725 27.6693 14.6256C27.6051 14.7786 27.511 14.9173 27.3926 15.0337C27.2742 15.15 27.1339 15.2416 26.9798 15.3032C26.8256 15.3647 26.6608 15.395 26.4949 15.3921L15.3746 15.3963L15.3704 26.5167C15.3646 26.8443 15.2303 27.1566 14.9966 27.3863C14.7628 27.6161 14.4482 27.7449 14.1206 27.745C13.7929 27.7451 13.4785 27.6166 13.2449 27.387C13.0113 27.1574 12.8773 26.8452 12.8717 26.5176L12.8759 15.3973L1.75553 15.4015C1.42795 15.3959 1.11576 15.2619 0.886195 15.0283C0.656632 14.7947 0.528067 14.4802 0.52819 14.1526C0.528315 13.825 0.657116 13.5104 0.886855 13.2766C1.11659 13.0429 1.42889 12.9086 1.75648 12.9028L12.8768 12.8986L12.881 1.77824C12.8812 1.44691 13.013 1.12912 13.2473 0.894745C13.4817 0.660371 13.7995 0.528608 14.1308 0.528433Z"
              fill="#C62027"
            />
          </svg>
        </button>
      </div>

      <!-- Save Button -->
      <div class="flex justify-end mt-6">
        <button
          @click="saveChanges"
          class="bg-[#C62027] !font-bold text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
        >
          Sauvegarder
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Partenariats et Co-dév stratégiques",
  },
  items: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["save", "cancel"]);

// Make a local copy of items so we don't mutate props directly
const localItems = ref([]);

onMounted(() => {
  // Initialize items with data from props
  if (props.items.length > 0) {
    // Make a deep copy and ensure all items have the correct type
    localItems.value = JSON.parse(JSON.stringify(props.items)).map((item) => ({
      ...item,
      type: "partnerships", // Ensure type is set to match OrganizationAttributeType.Partnerships
    }));
  }
});

// Remove an item from the list
function removeItem(index) {
  localItems.value.splice(index, 1);
}

// Add a new empty item
function addItem() {
  localItems.value.push({
    name: "",
    value: "",
    type: "partnerships", // This must match OrganizationAttributeType.Partnerships
  });
}

// Validate items before saving
function validateItems() {
  const errors = [];

  // Check for empty fields
  localItems.value.forEach((item, index) => {
    if (!item.name.trim()) {
      errors.push(`L'élément #${index + 1} doit avoir un type de partenariat.`);
    }
  });

  return errors;
}

// Save changes and emit to parent
function saveChanges() {
  try {
    // Validate before saving
    const validationErrors = validateItems();
    if (validationErrors.length > 0) {
      throw new Error(validationErrors.join("\n"));
    }

    emit("save", localItems.value);
    // The parent component will close the modal on success
  } catch (error) {
    // If an error is thrown from the parent component, keep the modal open
    console.error("Error in save:", error);
  }
}
</script>

<style scoped>
/* Custom scrollbar for the form content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
