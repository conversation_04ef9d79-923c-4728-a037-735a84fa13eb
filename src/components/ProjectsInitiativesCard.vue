<template>
  <div class="bg-white rounded-2xl shadow-sm">
    <!-- Header with title and edit button -->
    <div class="p-6 relative">
      <button
        @click="openEditModal"
        class="absolute top-2 right-2 p-2 rounded hover:bg-gray-100 focus:outline-none"
        aria-label="Edit"
      >
        <!-- Edit icon -->
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 4H6.5C5.39543 4 4.5 4.89543 4.5 6V18C4.5 19.1046 5.39543 20 6.5 20H18.5C19.6046 20 20.5 19.1046 20.5 18V12M9.5 15V12.5L18.25 3.75C18.9404 3.05964 20.0596 3.05964 20.75 3.75V3.75C21.4404 4.44036 21.4404 5.55964 20.75 6.25L16 11L12 15H9.5Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="px-6 pb-6">
      <!-- R&D Projects -->
      <div v-if="type === 'rd-project'">
        <h4 class="!text-lg font-medium !text-gray-700 mb-4">
          principaux projets R&D :
        </h4>

        <div
          v-for="(project, index) in items"
          :key="project.id || index"
          class="mb-8"
        >
          <h5 class="text-base font-medium mb-4">
            Projet {{ index + 1 }} : {{ project.name }}
          </h5>
          <div class="ml-6 mt-4">
            <div v-if="project.description" class="mb-4">
              <h6 class="text-sm font-medium text-gray-700 mb-3">
                Description
              </h6>
              <p class="text-sm text-gray-600">{{ project.description }}</p>
            </div>

            <div v-if="project.objectif">
              <h6 class="text-sm font-medium text-gray-700 mb-3">
                Objectifs ou résultats attendus
              </h6>
              <p class="text-sm text-gray-600">{{ project.objectif }}</p>
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <p
          v-if="!items || items.length === 0"
          class="text-sm text-gray-500 italic"
        >
          Aucun projet R&D n'a été ajouté.
        </p>
      </div>

      <!-- ESG Initiatives -->
      <div v-else-if="type === 'initiative'">
        <h4 class="!text-lg font-medium text-gray-700 mb-4">
          Initiatives ESG spécifiques en 2023 - 2024
        </h4>

        <div
          v-for="(initiative, index) in items"
          :key="initiative.id || index"
          class="mb-8"
        >
          <h5 class="!text-base font-medium mb-2">
            Initiative {{ index + 1 }} : {{ initiative.name }}
          </h5>
          <div class="ml-6 mt-4">
            <div v-if="initiative.impact">
              <h6 class="text-sm font-medium text-gray-700 mb-3">
                Impact ou résultat
              </h6>
              <p class="text-sm text-gray-600">{{ initiative.impact }}</p>
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <p
          v-if="!items || items.length === 0"
          class="text-sm text-gray-500 italic"
        >
          Aucune initiative ESG n'a été ajoutée.
        </p>
      </div>
    </div>

    <!-- Edit Modal -->
    <div
      v-if="isEditing"
      class="fixed inset-0 flex items-center justify-center p-12 z-50 bg-black bg-opacity-10"
    >
      <!-- Modal Card -->
      <div
        class="bg-white shadow-lg rounded-xl max-w-full relative p-6 w-3/4 max-h-[90vh] overflow-y-auto"
      >
        <!-- Close Button (Top Right) -->
        <button
          @click="cancelEdit"
          class="absolute top-3 right-3 text-gray-600 hover:text-gray-800"
        >
          <!-- X icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10 8.586L3.707 2.293a1 1 0 00-1.414 1.414L8.586 10l-6.293 6.293a1 1 0 001.414 1.414L10 11.414l6.293 6.293a1 1 0 001.414-1.414L11.414 10l6.293-6.293a1 1 0 00-1.414-1.414L10 8.586z"
              clip-rule="evenodd"
            />
          </svg>
        </button>

        <!-- Title -->
        <h2 class="!text-lg font-semibold mb-6">
          {{ type === "rd-project" ? "Projets R&D" : "Initiatives ESG" }}
        </h2>

        <!-- Items List -->
        <div class="space-y-6 max-h-[550px] overflow-y-auto">
          <div
            v-for="(item, index) in editItems"
            :key="index"
            class="border-b border-gray-200 pb-6 mb-6"
          >
            <!-- R&D Project or Initiative Info Section -->
            <div class="flex flex-col gap-4">
              <!-- First row: Name and Remove Button -->
              <div class="flex items-start">
                <!-- Name field -->
                <div class="flex-1 mr-4">
                  <label class="block text-sm font-semibold text-gray-700 mb-1">
                    {{
                      type === "rd-project"
                        ? "Nom du projet"
                        : "Nom de l'initiative"
                    }}
                  </label>
                  <input
                    v-model="item.name"
                    type="text"
                    class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                    :placeholder="
                      type === 'rd-project'
                        ? 'Nom du projet'
                        : 'Nom de l\'initiative'
                    "
                  />
                </div>

                <!-- Remove Button -->
                <div class="pt-8 ml-6">
                  <button
                    class="border !border-[#C62027] bg-red-50 py-3 px-3"
                    @click="removeItem(index)"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      class="w-5 h-5"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- R&D Project specific fields -->
              <template v-if="type === 'rd-project'">
                <!-- Second row: Description -->
                <div class="flex">
                  <div class="flex-1 mr-4">
                    <label
                      class="block text-sm font-semibold text-gray-700 mb-1"
                      >Description</label
                    >
                    <textarea
                      v-model="item.description"
                      rows="3"
                      class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                      placeholder="Description du projet"
                    ></textarea>
                  </div>
                  <!-- Empty div to maintain alignment -->
                  <div class="pt-6 w-10"></div>
                </div>

                <!-- Third row: Objectives -->
                <div class="flex">
                  <div class="flex-1 mr-4">
                    <label
                      class="block text-sm font-semibold text-gray-700 mb-1"
                      >Objectifs ou résultats attendus</label
                    >
                    <textarea
                      v-model="item.objectif"
                      rows="3"
                      class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                      placeholder="Objectifs ou résultats attendus"
                    ></textarea>
                  </div>
                  <!-- Empty div to maintain alignment -->
                  <div class="pt-6 w-10"></div>
                </div>
              </template>

              <!-- Initiative specific fields -->
              <template v-else-if="type === 'initiative'">
                <!-- Second row: Impact -->
                <div class="flex">
                  <div class="flex-1 mr-4">
                    <label
                      class="block text-sm font-semibold text-gray-700 mb-1"
                      >Impact ou résultat</label
                    >
                    <textarea
                      v-model="item.impact"
                      rows="3"
                      class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                      placeholder="Impact ou résultat de l'initiative"
                    ></textarea>
                  </div>
                  <!-- Empty div to maintain alignment -->
                  <div class="pt-6 w-10"></div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- Add New Item Button -->
        <div class="flex justify-center mt-4">
          <button
            @click="addItem"
            class="border !border-[#C62027] text-[#C62027] !bg-red-50 rounded-full px-4 py-3 w-full hover:!bg-red-50 transition text-sm font-medium flex items-center justify-center space-x-2"
          >
            <svg
              width="30"
              height="30"
              viewBox="0 0 28 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="inline-block"
            >
              <path
                d="M14 5.83301V22.1663"
                stroke="#C62027"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.83301 14H22.1663"
                stroke="#C62027"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end mt-6">
          <button
            @click="saveChanges"
            class="bg-[#C62027] !font-bold text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
          >
            Sauvegarder
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ["rd-project", "initiative"].includes(value),
  },
  items: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update"]);

const isEditing = ref(false);
const editItems = ref([]);

// Open the edit modal
function openEditModal() {
  // Create a deep copy of the items for editing
  editItems.value = JSON.parse(JSON.stringify(props.items || []));
  isEditing.value = true;
}

// Cancel editing and close the modal
function cancelEdit() {
  isEditing.value = false;
}

// Add a new empty item
function addItem() {
  if (props.type === "rd-project") {
    editItems.value.push({
      name: "",
      description: "",
      objectif: "",
    });
  } else {
    editItems.value.push({
      name: "",
      impact: "",
    });
  }
}

// Remove an item
function removeItem(index) {
  editItems.value.splice(index, 1);
}

// Save changes
function saveChanges() {
  try {
    // Validate items
    const validationErrors = [];

    editItems.value.forEach((item, index) => {
      if (!item.name.trim()) {
        validationErrors.push(
          `Le nom ${
            props.type === "rd-project" ? "du projet" : "de l'initiative"
          } #${index + 1} est requis.`
        );
      }

      if (props.type === "rd-project") {
        // Additional validation for R&D projects if needed
      } else {
        // Additional validation for initiatives if needed
        if (!item.impact.trim()) {
          validationErrors.push(
            `L'impact de l'initiative #${index + 1} est requis.`
          );
        }
      }
    });

    if (validationErrors.length > 0) {
      throw new Error(validationErrors.join("\n"));
    }

    // Emit update event with the edited items
    emit("update", editItems.value);

    // Close the edit popup after a successful update
    isEditing.value = false;
  } catch (error) {
    console.error("Validation error:", error.message);
    // Don't close the popup if there are validation errors
  }
}
</script>
