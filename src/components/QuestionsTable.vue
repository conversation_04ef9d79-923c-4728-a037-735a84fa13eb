<template>
  <section class="relative bg-white shadow rounded-lg">
    <!-- Header -->

    <div class="flex items-center justify-between">
      <
      <button @click="openEditModal" class="text-gray-500 hover:text-gray-700">
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- Questions Table -->
    <div class="w-full px-4 pb-4">
      <table class="table table-bordered table-nowrap align-middle w-full">
        <thead>
          <tr>
            <th scope="col" class="w-[30%]">Question</th>
            <th scope="col" class="w-[30%]">Réponse</th>
            <th scope="col">Détails (si oui)</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(question, i) in questions" :key="i" class="h-16">
            <td>{{ question.question }}</td>
            <td>{{ question.response }}</td>
            <td>{{ question.details || "N/A" }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Edit Modal -->
    <EditQuestionsModal
      v-if="isEditing"
      title="Questions et Réponses"
      :questions="questions"
      @save="handleSave"
      @cancel="isEditing = false"
    />
  </section>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";
import EditQuestionsModal from "./EditQuestionsModal.vue";

const props = defineProps({
  title: {
    type: String,
    default: "Questions et Réponses",
  },
  questions: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update"]);
const isEditing = ref(false);

// Open the edit modal
function openEditModal() {
  isEditing.value = true;
}

// Handle save from the edit modal
function handleSave(updatedQuestions) {
  try {
    // Create a variable to track if we should close the popup
    let shouldClosePopup = true;

    // Add an event listener for unhandled promise rejections
    const errorHandler = () => {
      // If there's an error, don't close the popup
      shouldClosePopup = false;
      // Remove the event listener
      window.removeEventListener("unhandledrejection", errorHandler);
    };

    // Add the event listener
    window.addEventListener("unhandledrejection", errorHandler);

    // Emit the update event to the parent component
    emit("update", updatedQuestions);

    // Close the modal after a short delay, but only if there was no error
    setTimeout(() => {
      // Remove the event listener
      window.removeEventListener("unhandledrejection", errorHandler);

      // Only close if no error occurred
      if (shouldClosePopup) {
        isEditing.value = false;
      }
    }, 1000);
  } catch (error) {
    // If there was an error, the modal stays open
    console.error("Error in QuestionsTable:", error);
    // The parent component will handle the error display with Toast
    throw error;
  }
}
</script>
