<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3
      class="!text-[#C62027] border-b border-gray-400 pb-2 !font-bold !text-2xl mb-6"
    >
      {{ title }}
    </h3>

    <!-- Clients Grid -->
    <div
      v-if="clients && clients.length > 0"
      class="grid grid-cols-2 gap-6 mt-4"
    >
      <div
        v-for="(client, index) in clients"
        :key="index"
        class="border border-gray-300 rounded-lg p-2 bg-[#FCF7F7] flex items-center"
      >
        <!-- Client Avatar -->
        <div
          class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4 flex-shrink-0"
        >
          <svg
            class="w-6 h-6 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            ></path>
          </svg>
        </div>

        <!-- Client Info -->
        <div class="flex-1 mt-2">
          <!-- Client Name -->
          <h4 class="!font-semibold text-gray-800 !text-sm">
            {{ client.name }}
          </h4>

          <!-- Client Description -->
          <p class="text-gray-500 !text-xs">
            {{ client.description }}
          </p>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-gray-500 text-center py-8">
      <p>No clients available</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  clients: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each client should have name and optionally description
      return value.every((client) => "name" in client);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
