<template>
  <section class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <!-- Title -->
    <h3
      class="text-lg font-semibold !text-[#C62027] mb-4 border-b border-gray-300 pb-2"
    >
      {{ title }}
    </h3>

    <!-- Photo Slider -->
    <div v-if="data.images && data.images.length > 0" class="relative mb-6">
      <div class="relative w-full h-90 bg-gray-100 rounded-lg overflow-hidden">
        <img
          :src="data.images[currentImageIndex]"
          :alt="`Vue extérieure ${currentImageIndex + 1}`"
          class="w-full h-full object-fit"
        />

        <!-- Left Arrow -->
        <button
          v-if="data.images.length > 1"
          @click="previousImage"
          class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            ></path>
          </svg>
        </button>

        <!-- Right Arrow -->
        <button
          v-if="data.images.length > 1"
          @click="nextImage"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-opacity"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            ></path>
          </svg>
        </button>

        <!-- Image Counter -->
        <div
          v-if="data.images.length > 1"
          class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm"
        >
          {{ currentImageIndex + 1 }} / {{ data.images.length }}
        </div>
      </div>
    </div>

    <!-- No Images Placeholder -->
    <div
      v-else
      class="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center mb-6"
    >
      <div class="text-center text-gray-500">
        <svg
          class="w-16 h-16 mx-auto mb-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          ></path>
        </svg>
        <p class="text-sm">Aucune image disponible</p>
      </div>
    </div>

    <!-- Company Information -->
    <div class="space-y-3">
      <div
        v-for="(item, index) in data.info"
        :key="index"
        class="flex space-x-18 items-center"
      >
        <span class="text-gray-700 !font-bold">{{ item.label }}</span>
        <span class="text-gray-900">{{ item.value || "N/A" }}</span>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
    validator: (value) => {
      return "images" in value && "info" in value;
    },
  },
});

const currentImageIndex = ref(0);

function nextImage() {
  if (props.data.images && props.data.images.length > 0) {
    currentImageIndex.value =
      (currentImageIndex.value + 1) % props.data.images.length;
  }
}

function previousImage() {
  if (props.data.images && props.data.images.length > 0) {
    currentImageIndex.value =
      (currentImageIndex.value - 1 + props.data.images.length) %
      props.data.images.length;
  }
}
</script>
