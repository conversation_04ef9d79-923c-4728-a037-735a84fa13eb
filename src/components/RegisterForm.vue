<template>
  <div class="min-h-screen bg-[#575956] py-4 px-2 h-full">
    <div class="max-w-[99%] mx-auto h-full">
      <div
        class="bg-white p-8 pb-12 rounded-[80px] h-full shadow-md font-poppins relative min-h-[90vh]"
      >
        <!-- Header: Title & Logo -->
        <div class="flex flex-row items-baseline mx-12">
          <h5 class="text-lg font-medium text-gray-700 mt-20">
            Informations generales de l'entreprise
          </h5>
          <img class="h-16 bg-white -mt-2 ml-100" src="/TAA_logo.png" alt="TAA Logo" />
        </div>
        <p class="text-sm text-gray-500 mb-4 mx-12">
          Veu<PERSON>z remplir toutes les informations ci-dessous
        </p>

        <!-- Form -->
        <form @submit.prevent="onSubmit">
          <div class="relative">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-40 gap-y-2 mx-20">
              <!-- Left Column -->
              <div class="space-y-3">
                <!-- Raison sociale -->
                <div class="space-y-3">
                  <label for="companyName" class="block text-xs font-medium">
                    Raison sociale de l'entreprise
                  </label>
                  <input
                    id="companyName"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter raison sociale de l'entreprise"
                    v-model="formData.companyName"
                    @input="updateError('companyName')"
                  />
                  <span v-if="errors.companyName" class="text-[10px] text-red-500">
                    {{ errors.companyName }}
                  </span>
                </div>
                <!-- Email -->
                <div class="space-y-3">
                  <label for="email" class="block text-xs font-medium">Email</label>
                  <input
                    id="email"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter email"
                    v-model="formData.email"
                    @input="updateError('email')"
                  />
                  <span v-if="errors.email" class="text-[10px] text-red-500">
                    {{ errors.email }}
                  </span>
                </div>
                <!-- Siège social -->
                <div class="space-y-3">
                  <label for="headquarters" class="block text-xs font-medium"
                    >Siège social</label
                  >
                  <input
                    id="headquarters"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter Siège social"
                    v-model="formData.headquarters"
                    @input="updateError('headquarters')"
                  />
                  <span v-if="errors.headquarters" class="text-[10px] text-red-500">
                    {{ errors.headquarters }}
                  </span>
                </div>
                <!-- Matricule Fiscale -->
                <div class="space-y-3">
                  <label for="taxId" class="block text-xs font-medium"
                    >Matricule Fiscale</label
                  >
                  <input
                    id="taxId"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter matricule fiscale (Example:11111111M)"
                    v-model="formData.taxId"
                    @input="updateError('taxId')"
                  />
                  <span v-if="errors.taxId" class="text-[10px] text-red-500">
                    {{ errors.taxId }}
                  </span>
                </div>
                <!-- Site Web -->
                <div class="space-y-3">
                  <label for="website" class="block text-xs font-medium">Site Web</label>
                  <input
                    id="website"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter website (Example:https://site.com)"
                    v-model="formData.website"
                    @input="updateError('website')"
                  />
                  <span v-if="errors.website" class="text-[10px] text-red-500">
                    {{ errors.website }}
                  </span>
                </div>
                <!-- Téléphone -->
                <div class="space-y-3">
                  <label for="phone" class="block text-xs font-medium">Téléphone</label>
                  <input
                    id="phone"
                    type="tel"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter phone no. (Example:+21699999999)"
                    v-model="formData.phone"
                    @input="updateError('phone')"
                  />
                  <span v-if="errors.phone" class="text-[10px] text-red-500">
                    {{ errors.phone }}
                  </span>
                </div>
                <!-- Sites de R&D -->
                <div class="space-y-3">
                  <label for="rdSites" class="block text-xs font-medium"
                    >Sites de R&D</label
                  >
                  <input
                    id="rdSites"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter site de R&D"
                    v-model="formData.rdSites"
                    @input="updateError('rdSites')"
                  />
                  <span v-if="errors.rdSites" class="text-[10px] text-red-500">
                    {{ errors.rdSites }}
                  </span>
                </div>
              </div>

              <!-- Vertical Divider (for md and up) -->
              <div
                class="hidden md:block absolute top-0 left-1/2 border-r-2 h-full border-[#A8B3C1]"
              ></div>

              <!-- Right Column -->
              <div class="space-y-3">
                <!-- Adresse -->
                <div class="space-y-3">
                  <label for="address" class="block text-xs font-medium">Adresse</label>
                  <input
                    id="address"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter adresse"
                    v-model="formData.address"
                    @input="updateError('address')"
                  />
                  <span v-if="errors.address" class="text-[10px] text-red-500">
                    {{ errors.address }}
                  </span>
                </div>
                <!-- Code Postal -->
                <div class="space-y-3">
                  <label for="postalCode" class="block text-xs font-medium"
                    >Code Postal</label
                  >
                  <input
                    id="postalCode"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter code postal"
                    v-model="formData.postalCode"
                    @input="updateError('postalCode')"
                  />
                  <span v-if="errors.postalCode" class="text-[10px] text-red-500">
                    {{ errors.postalCode }}
                  </span>
                </div>
                <!-- Ville -->
                <div class="space-y-3">
                  <label for="city" class="block text-xs font-medium">Ville</label>
                  <select
                    id="city"
                    v-model="formData.city"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    @change="updateError('city')"
                  >
                    <option value="" disabled>Sélectionnez une ville</option>
                    <option v-for="city in cities" :key="city" :value="city">
                      {{ city }}
                    </option>
                  </select>
                  <span v-if="errors.city" class="text-[10px] text-red-500">
                    {{ errors.city }}
                  </span>
                </div>
                <!-- Année de création -->
                <div class="space-y-3">
                  <label for="foundingYear" class="block text-xs font-medium"
                    >Année de création</label
                  >
                  <input
                    id="foundingYear"
                    type="text"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter année de création"
                    v-model="formData.foundingYear"
                    @input="updateError('foundingYear')"
                  />
                  <span v-if="errors.foundingYear" class="text-[10px] text-red-500">
                    {{ errors.foundingYear }}
                  </span>
                </div>
                <!-- Description -->
                <div class="space-y-3">
                  <label for="description" class="block text-xs font-medium"
                    >Description</label
                  >
                  <textarea
                    id="description"
                    rows="4"
                    class="w-full px-2 py-1 border border-gray-300 rounded-md"
                    placeholder="Enter description"
                    v-model="formData.description"
                    @input="updateError('description')"
                  ></textarea>
                  <span v-if="errors.description" class="text-[10px] text-red-500">
                    {{ errors.description }}
                  </span>
                </div>
                <!-- Logo Entreprise -->
                <div class="space-y-3">
                  <label for="logo" class="block text-xs font-medium"
                    >Logo Entreprise</label
                  >
                  <div class="flex items-center space-x-2">
                    <button
                      type="button"
                      @click="$refs.fileInput.click()"
                      class="px-2 py-1 text-red-600 border border-red-600 rounded-md hover:bg-red-50 flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="h-4 w-4 mr-1"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
                        />
                      </svg>
                      Upload File
                    </button>
                    <span class="text-xs text-gray-500 ml-4" v-if="!formData.logo">
                      Aucun fichier n'a été choisi
                    </span>
                    <span class="text-xs font-medium ml-4" v-else>
                      {{ formData.logo.name }}
                    </span>
                    <input
                      ref="fileInput"
                      type="file"
                      class="hidden"
                      @change="handleFileUpload"
                      accept="image/*"
                    />
                  </div>
                  <span v-if="errors.logo" class="text-[10px] text-red-500">
                    {{ errors.logo }}
                  </span>
                  <span v-if="successMessage" class="text-green-700">{{
                    successMessage
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Bouton de soumission -->
          <div class="mt-20 absolute bottom-7 left-0 right-7 flex justify-end">
            <button
              type="submit"
              class="btn btn-danger w-[15%] py-3 rounded-pill"
              :disabled="!isFormValid"
            >
              Créer
            </button>
          </div>
          <div v-if="serverError" class="server-error -mt-10 ml-12">
            <span v-if="serverError" class="text-red-700">{{ serverError }}</span>
          </div>
        </form>
      </div>

      <div class="text-center text-white text-xs mt-2">
        © 2024 Tunisian Automotive Association
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed, ref } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, email, numeric, url } from "@vuelidate/validators";
import organizationService from "../Services/services/organisation";
import filesService from "../Services/services/files";
import { helpers } from "@vuelidate/validators";
import { useRouter } from "vue-router";

const router = useRouter();

const phoneFormat = helpers.withMessage(
  "Le numéro de téléphone est invalide.",
  (value) => {
    // This is a basic example: it accepts optional "+" followed by 10-15 digits.
    return /^(\+?\d{10,15})$/.test(value);
  }
);

const cities = [
  "Tunis",
  "Ariana",
  "Ben Arous",
  "La Manouba",
  "Bizerte",
  "Béja",
  "Jendouba",
  "Kairouan",
  "Kasserine",
  "Kébili",
  "Kéf",
  "Gabes",
  "Gafsa",
  "Mahdia",
  "Medenine",
  "Monastir",
  "Nabeul",
  "Sfax",
  "Sidi Bouzid",
  "Siliana",
  "Sousse",
  "Tataouine",
  "Tozeur",
  "Zaghouan",
];
const serverError = ref("");
const successMessage = ref("");
const formData = reactive({
  companyName: "",
  email: "",
  headquarters: "",
  taxId: "",
  website: "",
  phone: "",
  rdSites: "",
  address: "",
  postalCode: "",
  city: "",
  foundingYear: "",
  description: "",
  logo: null,
});

const errors = reactive({
  companyName: "",
  email: "",
  headquarters: "",
  taxId: "",
  website: "",
  phone: "",
  rdSites: "",
  address: "",
  postalCode: "",
  city: "",
  foundingYear: "",
  description: "",
  logo: "",
});

const rules = {
  formData: {
    companyName: { required },
    email: { required, email },
    headquarters: { required },
    taxId: { required },
    website: { required, url },
    phone: { required, phoneFormat },
    rdSites: { required },
    address: { required },
    postalCode: { required, numeric },
    city: { required },
    foundingYear: { required, numeric },
    description: { required },
    logo: { required },
  },
};

const v$ = useVuelidate(rules, { formData });

const errorMessages = {
  companyName: {
    required: "Le nom de l'entreprise est requis.",
  },
  email: {
    required: "L'email est requis.",
    email: "Format d'email invalide.",
  },
  headquarters: {
    required: "Le siège social est requis.",
  },
  taxId: {
    required: "Le matricule fiscal est requis.",
  },
  website: {
    required: "Le site web est requis.",
    url: "Format d'URL invalide.",
  },
  phone: {
    required: "Le numéro de téléphone est requis.",
    phoneFormat: "Format de numéro de téléphone invalide.",
  },
  rdSites: {
    required: "Le site de R&D est requis.",
  },
  address: {
    required: "L'adresse est requise.",
  },
  postalCode: {
    required: "Le code postal est requis.",
    numeric: "Le code postal doit être un nombre.",
  },
  city: {
    required: "Veuillez sélectionner une ville.",
  },
  foundingYear: {
    required: "L'année de création est requise.",
    numeric: "L'année de création doit être un nombre.",
  },
  description: {
    required: "La description est requise.",
  },
  logo: {
    required: "Le logo de l'entreprise est requis.",
  },
};

function updateError(fieldName) {
  v$.value.formData[fieldName].$touch();
  const fieldState = v$.value.formData[fieldName];
  if (fieldState.$invalid) {
    if (fieldState.$errors.length > 0) {
      const validator = fieldState.$errors[0].$validator;
      errors[fieldName] = errorMessages[fieldName][validator] || "Erreur inconnue.";
    }
  } else {
    errors[fieldName] = "";
  }
}

function handleFileUpload(event) {
  formData.logo = event.target.files[0];
  updateError("logo");
}

const isFormValid = computed(() => {
  const allFilled = Object.keys(formData).every((key) => {
    if (key === "logo") {
      return formData.logo !== null;
    }

    return formData[key].toString().trim() !== "";
  });
  return allFilled && Object.values(errors).every((msg) => msg === "");
});

async function onSubmit() {
  v$.value.$touch();
  Object.keys(formData).forEach((field) => updateError(field));
  if (!isFormValid.value) {
    return;
  }
  const logoUrl = await filesService.uploadFile(formData.logo);

  const payload = {
    name: formData.companyName,
    email: formData.email,
    headOffice: formData.headquarters,
    taxNumber: formData.taxId,
    websiteUrl: formData.website,
    phone: formData.phone,
    address: formData.address,
    postalCode: formData.postalCode,
    city: formData.city,
    foundingYear: Number(formData.foundingYear),
    description: formData.description,
    logoUrl: logoUrl["data"],
  };
  try {
    const response = await organizationService.createOrganization(payload);
    console.log("Organization created successfully:", response.data);
    successMessage.value = "Organization created successfully!";
    setTimeout(() => {
      router.push("/signin");
    }, 2000);
  } catch (error) {
    serverError.value = error.response?.data?.errorMessage || "Une erreur est survenue.";
    console.error("Error creating organization:", error);
  }
}
</script>

<style scoped>
server-error {
  height: 2rem;
  align-items: center;
}
</style>
