<template>
  <div class="mb-8 bg-white rounded-lg p-6 shadow-sm">
    <!-- Section Title -->
    <h3
      class="!text-[#C62027] border-b border-gray-400 pb-2 !font-bold !text-2xl mb-6"
    >
      {{ title }}
    </h3>

    <!-- Certifications Grid -->
    <div
      v-if="certifications && certifications.length > 0"
      class="flex flex-wrap gap-5 mt-4"
    >
      <div
        v-for="(certification, index) in certifications"
        :key="index"
        class="inline-flex w-[20%] items-center px-4 py-2 bg-black rounded-full text-sm font-medium text-white"
      >
        <!-- Certification Name -->
        <span class="mx-auto">{{ certification.name }}</span>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-gray-500 text-center py-8">
      <p>No certifications available</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  certifications: {
    type: Array,
    default: () => [],
    validator: (value) => {
      // Each certification should have name property
      return value.every((certification) => "name" in certification);
    },
  },
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
