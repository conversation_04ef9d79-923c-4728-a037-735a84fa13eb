<template>
  <section class="relative bg-white shadow rounded-lg p-6">
    <!-- Header -->
    <div class="px-4 border-b border-gray-400 mb-4">
      <div class="flex items-center justify-between">
        <h2 class="!text-lg text-gray-800 !font-bold">Type De Déchets</h2>
        <button
          @click="openEditModal"
          class="text-gray-500 hover:text-gray-700"
        >
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
              stroke="black"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Waste Distribution Table -->
    <div class="w-full px-4 mt-10">
      <!-- Headers row -->
      <div
        class="grid grid-cols-7 gap-0 py-3 !text-sm border-b border-gray-400"
      >
        <div class="font-medium">Plastique</div>
        <div class="font-medium">Métallique</div>
        <div class="font-medium">Textiles et Cuirs</div>
        <div class="font-medium">Huiles</div>
        <div class="font-medium">Papiers et Cartons</div>
        <div class="font-medium">Dangereux</div>
        <div class="font-medium">Autres</div>
      </div>

      <!-- Values row -->
      <div
        class="grid grid-cols-7 gap-0 py-3 !text-lg font-semibold border-b border-gray-400"
      >
        <div>{{ formatPercentage(wasteData.plastic) }}</div>
        <div>{{ formatPercentage(wasteData.metallic) }}</div>
        <div>{{ formatPercentage(wasteData.textilesAndLeather) }}</div>
        <div>{{ formatPercentage(wasteData.oils) }}</div>
        <div>{{ formatPercentage(wasteData.papersAndCardboard) }}</div>
        <div>{{ formatPercentage(wasteData.hazardous) }}</div>
        <div>{{ formatPercentage(wasteData.others) }}</div>
      </div>
    </div>

    <EditPopup
      v-if="isEditing"
      :items="editItems"
      :title="title"
      @save="handleSave"
      @cancel="isEditing = false"
      class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
    />
  </section>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from "vue";
import EditPopup from "./EditPopup.vue";

const props = defineProps({
  title: {
    type: String,
    default: "Type De Déchets",
  },
  wasteData: {
    type: Object,
    default: () => ({
      plastic: 0,
      metallic: 0,
      textilesAndLeather: 0,
      oils: 0,
      papersAndCardboard: 0,
      hazardous: 0,
      others: 0,
    }),
  },
});

const emit = defineEmits(["update"]);
const isEditing = ref(false);

// Format percentage values
const formatPercentage = (value) => {
  if (value === null || value === undefined) return "0%";
  return `${value}%`;
};

// Prepare items for the edit popup
const editItems = computed(() => [
  {
    label: "Plastique (%)",
    value: props.wasteData.plastic || 0,
  },
  {
    label: "Métallique (%)",
    value: props.wasteData.metallic || 0,
  },
  {
    label: "Textiles et Cuirs (%)",
    value: props.wasteData.textilesAndLeather || 0,
  },
  {
    label: "Huiles (%)",
    value: props.wasteData.oils || 0,
  },
  {
    label: "Papiers et Cartons (%)",
    value: props.wasteData.papersAndCardboard || 0,
  },
  {
    label: "Dangereux (%)",
    value: props.wasteData.hazardous || 0,
  },
  {
    label: "Autres (%)",
    value: props.wasteData.others || 0,
  },
]);

// Open the edit modal
function openEditModal() {
  isEditing.value = true;
}

// Handle save from the edit popup
function handleSave(updatedItems) {
  try {
    // Create a variable to track if we should close the popup
    let shouldClosePopup = true;

    // Add an event listener for unhandled promise rejections
    const errorHandler = () => {
      // If there's an error, don't close the popup
      shouldClosePopup = false;
      // Remove the event listener
      window.removeEventListener("unhandledrejection", errorHandler);
    };

    // Add the event listener
    window.addEventListener("unhandledrejection", errorHandler);

    // Map the updated items back to the waste distribution format
    const updatedWasteData = {
      plastic: parseFloat(updatedItems[0].value) || 0,
      metallic: parseFloat(updatedItems[1].value) || 0,
      textilesAndLeather: parseFloat(updatedItems[2].value) || 0,
      oils: parseFloat(updatedItems[3].value) || 0,
      papersAndCardboard: parseFloat(updatedItems[4].value) || 0,
      hazardous: parseFloat(updatedItems[5].value) || 0,
      others: parseFloat(updatedItems[6].value) || 0,
    };

    // Emit the update event to the parent component
    emit("update", updatedWasteData);

    // Close the modal after a short delay, but only if there was no error
    setTimeout(() => {
      // Remove the event listener
      window.removeEventListener("unhandledrejection", errorHandler);

      // Only close if no error occurred
      if (shouldClosePopup) {
        isEditing.value = false;
      }
    }, 1000);
  } catch (error) {
    // If there was an error, the modal stays open
    console.error("Error in WasteDistributionCard:", error);
    // The parent component will handle the error display with Toast
    throw error;
  }
}
</script>
