<template>
  <BRow class="g-0 min-vh-100">
    <!-- Left Half - Centered Logo -->
    <BCol
      md="6"
      class="d-none d-md-flex align-items-center justify-content-center position-relative"
    >
      <div class="w-100 h-100 d-flex align-items-center justify-content-center">
        <img
          src="/TAA_logo.png"
          alt="TAA Logo"
          class="img-fluid"
          style="max-height: 95%; max-width: 95%"
        />
      </div>
    </BCol>

    <!-- Right Half - Form Container -->
    <BCol
      md="6"
      class="d-flex align-items-center justify-content-center bg-[#1E1E1E] py-5"
    >
      <div class="w-100" style="max-width: 480px">
        <BCard no-body class="shadow-lg !rounded-2xl py-12">
          <div class="text-center mb-5">
            <p class="text-muted mb-0 text-sm">
              Réinitialisez votre mot de passe pour continuer vers TAA
            </p>
          </div>
          <BCardBody class="p-5 m-0">
            <form @submit.prevent="tryToResetPassword">
              <div class="mb-2">
                <p class="font-bold mb-2 text-base">
                  Entrez votre nouveau mot de passe
                </p>
                <!-- New Password Field -->
                <div class="mb-2">
                  <label for="password" class="form-label text-xs ml-4">
                    Nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="password"
                    class="form-control !bg-[#F2F2F2] border-0 placeholder:text-xs"
                    placeholder="Entrez le nouveau mot de passe"
                    v-model="formData.password"
                    @input="updateError('password')"
                  />
                  <span v-if="errors.password" class="text-xs text-danger ml-4">
                    {{ errors.password }}
                  </span>
                </div>

                <!-- Password Confirmation Field -->
                <div class="mb-2">
                  <label
                    for="passwordConfirmation"
                    class="form-label text-xs ml-4"
                  >
                    Confirmer le mot de passe
                  </label>
                  <input
                    type="password"
                    id="passwordConfirmation"
                    class="form-control !bg-[#F2F2F2] border-0 placeholder:text-xs"
                    placeholder="Confirmez le nouveau mot de passe"
                    v-model="formData.passwordConfirmation"
                    @input="updateError('passwordConfirmation')"
                  />
                  <span
                    v-if="errors.passwordConfirmation"
                    class="text-xs text-danger ml-4"
                  >
                    {{ errors.passwordConfirmation }}
                  </span>
                </div>

                <!-- Server or Validation Error Message -->
                <div class="mb-2 ml-4">
                  <span v-if="authError" class="text-danger text-xs">
                    {{ authError }}
                  </span>
                  <span v-if="successMessage" class="text-success text-xs">
                    {{ successMessage }}
                  </span>
                </div>

                <BButton
                  variant="success"
                  class="w-100 !bg-[#C62027] !font-bold !rounded-[40px] py-2 !border-none !mt-8"
                  type="submit"
                  :disabled="!isFormValid || processing"
                >
                  Réinitialiser
                </BButton>
              </div>
            </form>
          </BCardBody>

          <footer class="mt-4 pt-4 text-center">
            <p class="text-muted small mb-0">
              &copy; {{ new Date().getFullYear() }} Tunisian Automotive
              Association
            </p>
          </footer>
        </BCard>
      </div>
    </BCol>
  </BRow>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required } from "@vuelidate/validators";
import authService from "../Services/services/auth";

const router = useRouter();
const route = useRoute();

// Automatically retrieve the token from the query parameters
const emailToken = route.params.token;
console.log("Email Token:", emailToken);

const formData = reactive({
  emailToken, // prefilled automatically
  password: "",
  passwordConfirmation: "",
});

// Define validation rules without checking that the passwords match
const rules = computed(() => ({
  password: { required },
  passwordConfirmation: { required },
}));

const v$ = useVuelidate(rules, formData);

const errors = reactive({
  password: "",
  passwordConfirmation: "",
});

const errorMessages = {
  password: {
    required: "Le mot de passe est requis.",
  },
  passwordConfirmation: {
    required: "La confirmation du mot de passe est requise.",
  },
};

function updateError(fieldName) {
  v$.value[fieldName].$touch();
  const fieldState = v$.value[fieldName];
  if (fieldState.$invalid) {
    const validator = fieldState.$errors[0].$validator;
    errors[fieldName] =
      errorMessages[fieldName][validator] || "Erreur inconnue.";
  } else {
    errors[fieldName] = "";
  }
}

const isFormValid = computed(() => {
  const allFilled = Object.keys(formData).every((key) => {
    if (key === "emailToken") return formData.emailToken !== "";
    return formData[key].toString().trim() !== "";
  });
  return allFilled && Object.values(errors).every((msg) => msg === "");
});

const processing = ref(false);
const authError = ref("");
const successMessage = ref("");

async function tryToResetPassword() {
  v$.value.$touch();
  Object.keys(rules.value).forEach(updateError);
  if (!isFormValid.value) {
    return;
  }
  processing.value = true;
  authError.value = "";
  successMessage.value = "";

  const payload = {
    emailToken: formData.emailToken,
    password: formData.password,
    passwordConfirmation: formData.passwordConfirmation,
  };

  try {
    await authService.changePassword(payload);
    successMessage.value = "Mot de passe réinitialisé avec succès !";
    // Redirect to the sign-in page after a 2-second delay
    setTimeout(() => {
      router.push("/signin");
    }, 2000);
  } catch (error) {
    authError.value =
      error.response?.data?.errorMessage || "Une erreur est survenue.";
    console.error("Error resetting password:", error);
  } finally {
    processing.value = false;
  }
}
</script>

<style scoped>
/* Add any additional styles if needed */
</style>
