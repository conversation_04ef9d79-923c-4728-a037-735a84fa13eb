<template>
  <div class="p-4 space-y-6">
    <!-- Primary Activity Section -->
    <details :open="openPrimary" @toggle="openPrimary = $event.target.open">
      <summary
        class="cursor-pointer p-4 text-lg !font-bold justify-between list-none"
      >
        <div class="flex items-center gap-x-10 whitespace-nowrap">
          Activité principale à la déclaration
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            :class="openPrimary ? 'rotate-180' : ''"
            class="ml-2 transition-transform"
          >
            <path
              d="M4.6709 9L12.6709 17L20.6709 9"
              stroke="#1A191F"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </summary>
      <div class="px-4 pb-4 space-y-3">
        <div
          v-for="activity in availableActivities"
          :key="activity.id"
          class="flex items-center gap-2"
        >
          <input
            class="accent-red-600"
            type="checkbox"
            :id="'primary-' + activity.id"
            :value="activity.id"
            :checked="isPrimarySelected(activity.id)"
            @change="togglePrimaryActivity(activity.id)"
          />
          <label :for="'primary-' + activity.id" class="cursor-pointer">
            {{ activity.name }}
          </label>
        </div>
      </div>
    </details>

    <hr class="my-4 border-t border-gray-300" />

    <!-- Secondary Activity Section -->
    <details :open="openSecondary" @toggle="openSecondary = $event.target.open">
      <summary
        class="cursor-pointer p-4 text-lg !font-bold justify-between list-none"
      >
        <div class="flex items-baseline gap-x-10 whitespace-nowrap">
          Activité secondaire à la déclaration
          <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            :class="openSecondary ? 'rotate-180' : ''"
            class="ml-2 transition-transform"
          >
            <path
              d="M4.6709 9L12.6709 17L20.6709 9"
              stroke="#1A191F"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </summary>
      <div class="px-4 pb-4 space-y-3">
        <div
          v-for="activity in availableActivities"
          :key="activity.id"
          class="flex items-center gap-2"
        >
          <input
            class="accent-red-600"
            type="checkbox"
            :id="'secondary-' + activity.id"
            :value="activity.id"
            :checked="isSecondarySelected(activity.id)"
            @change="toggleSecondaryActivity(activity.id)"
          />
          <label :for="'secondary-' + activity.id" class="cursor-pointer">
            {{ activity.name }}
          </label>
        </div>
      </div>
    </details>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import activitiesService from "../Services/services/activities";

const props = defineProps({
  primaryActivities: {
    type: Array,
    default: () => [],
  },
  secondaryActivities: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["updateActivities"]);

const availableActivities = ref([]);
const openPrimary = ref(true);
const openSecondary = ref(true);
const selectedPrimary = ref(new Set());
const selectedSecondary = ref(new Set());

// Watch for changes in props and update local state independently
watch(
  () => props.primaryActivities,
  (newActivities) => {
    if (newActivities) {
      selectedPrimary.value = new Set(newActivities.map((a) => a.id));
    }
  },
  { immediate: true }
);

watch(
  () => props.secondaryActivities,
  (newActivities) => {
    if (newActivities) {
      selectedSecondary.value = new Set(newActivities.map((a) => a.id));
    }
  },
  { immediate: true }
);

onMounted(async () => {
  try {
    const response = await activitiesService.getActivities();
    availableActivities.value = response.data;
  } catch (error) {
    console.error("Error fetching activities:", error);
  }
});

const isPrimarySelected = (activityId) => selectedPrimary.value.has(activityId);
const isSecondarySelected = (activityId) =>
  selectedSecondary.value.has(activityId);

const togglePrimaryActivity = (activityId) => {
  const isPrimary = selectedPrimary.value.has(activityId);

  if (isPrimary) {
    selectedPrimary.value.delete(activityId);
  } else {
    selectedPrimary.value.add(activityId);
  }

  emitUpdate();
};

const toggleSecondaryActivity = (activityId) => {
  const isSecondary = selectedSecondary.value.has(activityId);

  if (isSecondary) {
    selectedSecondary.value.delete(activityId);
  } else {
    selectedSecondary.value.add(activityId);
  }

  emitUpdate();
};

const emitUpdate = () => {
  emit("updateActivities", {
    primaryActivities: Array.from(selectedPrimary.value),
    secondaryActivities: Array.from(selectedSecondary.value),
  });
};
</script>

<style scoped>
summary::-webkit-details-marker {
  display: none;
}
</style>
