<script setup>
import { ref, defineProps, defineEmits, onMounted } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Opportunités et besoins futurs",
  },
  opportunities: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["save", "cancel"]);

// Make a local copy of opportunities so we don't mutate props directly
const localOpportunities = ref([]);

// Available categories and priorities for dropdowns
const categories = ref([
  "Prochaine ressource recherchée",
  "Niveau 2 (Tier 2)",
  "Niveau 1 (Tier 1)",
  "Constructeurs (OEM)",
  "Assemblage de Production",
]);

const priorities = ref(["LOW", "MEDIUM", "HIGH"]);

onMounted(() => {
  // Initialize opportunities with data from props
  if (props.opportunities.length > 0) {
    localOpportunities.value = JSON.parse(JSON.stringify(props.opportunities));
  }
  // No longer adding a default empty opportunity if none exist
});

// Remove an opportunity from the list
function removeOpportunity(index) {
  localOpportunities.value.splice(index, 1);
}

// Add a new empty opportunity
function addOpportunity() {
  localOpportunities.value.push({
    category: "",
    description: "",
    priority: "LOW",
  });
}

// Validate opportunities before saving
function validateOpportunities() {
  const errors = [];

  // Check if any opportunity has empty required fields
  for (let i = 0; i < localOpportunities.value.length; i++) {
    const opportunity = localOpportunities.value[i];

    if (!opportunity.category) {
      errors.push(`L'opportunité #${i + 1} doit avoir une catégorie`);
    }

    if (!opportunity.priority) {
      errors.push(`L'opportunité #${i + 1} doit avoir une priorité`);
    }

    if (opportunity.description && opportunity.description.length > 500) {
      errors.push(
        `La description de l'opportunité #${
          i + 1
        } ne doit pas dépasser 500 caractères`
      );
    }
  }

  return errors;
}

// Save changes and emit to parent
async function saveChanges() {
  try {
    // Validate before saving
    const validationErrors = validateOpportunities();
    if (validationErrors.length > 0) {
      throw new Error(validationErrors.join("\n"));
    }

    await emit("save", localOpportunities.value);
    // The parent component will close the modal on success
  } catch (error) {
    // If an error is thrown from the parent component, keep the modal open
    console.error("Error in save:", error);

    // Re-throw the error to be handled by the parent component
    throw error;
  }
}

// Format priority for display
function formatPriority(priority) {
  return priority.charAt(0) + priority.slice(1).toLowerCase();
}
</script>

<template>
  <!-- Modal Overlay -->
  <div
    class="fixed inset-0 flex items-center justify-center p-12 z-50 bg-black bg-opacity-10"
  >
    <!-- Modal Card -->
    <div class="bg-white shadow-lg rounded-xl max-w-full relative p-6 w-3/4">
      <!-- Close Button (Top Right) -->
      <button
        @click="$emit('cancel')"
        class="absolute top-3 right-3 text-gray-600 hover:text-gray-800"
      >
        <!-- X icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414 1.414L8.586 10l-6.293 6.293a1 1 0 001.414 1.414L10 11.414l6.293 6.293a1 1 0 001.414-1.414L11.414 10l6.293-6.293a1 1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Title -->
      <h2 class="!text-lg font-semibold mb-6">{{ title }}</h2>

      <!-- Opportunities List -->
      <div class="space-y-6 max-h-[550px] overflow-y-auto">
        <div
          v-for="(opportunity, index) in localOpportunities"
          :key="index"
          class="border-b border-gray-200 pb-6 mb-6"
        >
          <!-- Opportunity Info Section -->
          <div class="flex flex-col gap-4">
            <!-- First row: Category and Priority -->
            <div class="flex items-start">
              <!-- Catégorie -->
              <div class="flex-1 mr-4">
                <label class="block text-sm font-semibold text-gray-700 mb-1"
                  >Catégorie</label
                >
                <div class="relative">
                  <select
                    v-model="opportunity.category"
                    class="border border-gray-300 rounded w-full px-3 py-2 text-sm appearance-none pr-8"
                  >
                    <option
                      v-for="category in categories"
                      :key="category"
                      :value="category"
                    >
                      {{ category }}
                    </option>
                  </select>
                  <div
                    class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                  >
                    <svg
                      class="w-4 h-4 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Priorité -->
              <div class="flex-1 mr-4">
                <label class="block text-sm font-semibold text-gray-700 mb-1"
                  >Priorité</label
                >
                <div class="relative">
                  <select
                    v-model="opportunity.priority"
                    class="border border-gray-300 rounded w-full px-3 py-2 text-sm appearance-none pr-8"
                  >
                    <option
                      v-for="priority in priorities"
                      :key="priority"
                      :value="priority"
                    >
                      {{ formatPriority(priority) }}
                    </option>
                  </select>
                  <div
                    class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
                  >
                    <svg
                      class="w-4 h-4 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Remove Button -->
              <div class="pt-8">
                <button
                  class="border !border-[#C62027] bg-red-50 py-3 px-3"
                  @click="removeOpportunity(index)"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-5 h-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Description field -->
            <div>
              <div class="flex justify-between">
                <label class="block text-sm font-semibold text-gray-700 mb-1"
                  >Description</label
                >
                <span
                  class="text-xs text-gray-500"
                  :class="{
                    'text-red-500':
                      opportunity.description &&
                      opportunity.description.length > 500,
                  }"
                >
                  {{
                    opportunity.description
                      ? opportunity.description.length
                      : 0
                  }}/500
                </span>
              </div>
              <textarea
                v-model="opportunity.description"
                rows="4"
                class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                :class="{
                  'border-red-500':
                    opportunity.description &&
                    opportunity.description.length > 500,
                }"
                placeholder="Entrez une description détaillée..."
                maxlength="500"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- Add New Opportunity Button -->
      <div class="flex justify-center mt-4">
        <button
          @click="addOpportunity"
          class="border !border-[#C62027] text-[#C62027] !bg-red-50 rounded-full px-4 py-3 w-full hover:!bg-red-50 transition text-sm font-medium flex items-center justify-center space-x-2"
        >
          <svg
            width="30"
            height="30"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="inline-block"
          >
            <path
              d="M14 5.83301V22.1663"
              stroke="#C62027"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M5.83301 14H22.1663"
              stroke="#C62027"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Save Button -->
      <div class="flex justify-end mt-6">
        <button
          @click="saveChanges"
          class="bg-[#C62027] !font-bold text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
        >
          Sauvegarder
        </button>
      </div>
    </div>
  </div>
</template>
