<script setup>
import { ref, defineProps, defineEmits } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Pourcentage de participation par pays",
  },
  countries: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["save", "cancel", "error"]);

const localCountries = ref(JSON.parse(JSON.stringify(props.countries)));

// Function to remove a country from the list
function removeCountry(index) {
  localCountries.value.splice(index, 1);
}

// Function to add a new country to the list
function addCountry() {
  localCountries.value.push({ country: "", count: "" });
}

// Function to save changes and emit to parent
function saveChanges() {
  // Validate that all countries have names
  const emptyCountries = localCountries.value.filter(
    (country) => !country.country.trim()
  );
  if (emptyCountries.length > 0) {
    emit("error", "Veuillez saisir un nom pour chaque pays");
    return;
  }

  // Validate that all percentages are numbers
  const invalidPercentages = localCountries.value.filter(
    (country) =>
      isNaN(parseInt(country.count)) ||
      parseInt(country.count) < 0 ||
      parseInt(country.count) > 100
  );
  if (invalidPercentages.length > 0) {
    emit(
      "error",
      "Tous les pourcentages doivent être des nombres entre 0 et 100"
    );
    return;
  }

  // Validate that percentages add up to 100%
  const totalPercentage = localCountries.value.reduce((sum, country) => {
    return sum + (parseInt(country.count) || 0);
  }, 0);

  if (totalPercentage !== 100) {
    emit(
      "error",
      `La somme des pourcentages doit être égale à 100%. Actuellement: ${totalPercentage}%`
    );
    return;
  }

  // All validations passed, emit save event
  emit("save", localCountries.value);
}
</script>

<template>
  <!-- Modal Overlay -->
  <div
    class="fixed inset-0 flex items-center justify-center p-12 z-50 bg-black bg-opacity-10"
  >
    <!-- Modal Card -->
    <div class="bg-white shadow-lg rounded-xl max-w-full relative p-6 w-1/3">
      <!-- Close Button (Top Right) -->
      <button
        @click="$emit('cancel')"
        class="absolute top-3 right-3 text-gray-600 hover:text-gray-800"
      >
        <!-- X icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414
            1.414L8.586 10l-6.293 6.293a1 1 0
            001.414 1.414L10 11.414l6.293 6.293a1
            1 0 001.414-1.414L11.414 10l6.293-6.293a1
            1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Title -->
      <h2 class="!text-xs !font-bold text-gray-700 mb-4">
        {{ title }}
      </h2>

      <!-- Countries List -->
      <div class="space-y-4 max-h-[400px] overflow-y-auto">
        <!-- One row per country -->
        <div
          v-for="(country, index) in localCountries"
          :key="index"
          class="grid grid-cols-[1.5fr_1fr_auto] gap-x-2 items-start"
        >
          <!-- Country Input -->
          <div>
            <label class="block !text-sm !font-semibold text-gray-700 mb-1"
              >Pays</label
            >
            <input
              v-model="country.country"
              type="text"
              class="border border-gray-300 rounded w-full !px-6 py-4 text-sm"
              style="padding-left: 2.5rem; padding-right: 2.5rem"
            />
          </div>

          <!-- Percentage Input -->
          <div>
            <label class="block text-sm font-semibold text-gray-700 mb-1"
              >Pourcentage (%)</label
            >
            <input
              v-model="country.count"
              type="number"
              min="0"
              max="100"
              class="border border-gray-300 rounded w-full px-2 py-4 text-sm"
            />
          </div>

          <!-- Remove Button -->
          <div class="!border !border-[#C62027] mt-10 p-2 ml-2 bg-red-50">
            <button
              class="text-[#C62027] hover:text-red-700 mt-6"
              @click="removeCountry(index)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                class="w-5 h-5"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Add New Row Button -->
      <div class="flex justify-center mt-4 !border !border-[#C62027]">
        <button
          @click="addCountry"
          class="bg-red-50 text-red-500 rounded-full px-4 py-2 w-full hover:bg-red-50 transition text-sm font-medium"
        >
          <svg
            width="15"
            height="15"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="mx-auto"
          >
            <path
              d="M14 5.83301V22.1663M5.83334 13.9997H22.1667"
              stroke="#C62027"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Save Button -->
      <div class="flex justify-end mt-6">
        <button
          @click="saveChanges"
          class="bg-[#C62027] !font-bold text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
        >
          Sauvegarder
        </button>
      </div>
    </div>
  </div>
</template>
