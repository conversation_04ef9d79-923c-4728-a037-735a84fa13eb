<template>
  <div class="fixed inset-0 flex items-center justify-center p-12 z-50">
    <div class="bg-white p-14 shadow-lg rounded-xl w-1/4 relative">
      <!-- X Icon in the Top Right Corner -->
      <button
        @click="handleCancel"
        class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414 1.414L8.586 10l-6.293 6.293a1 1 0 001.414 1.414L10 11.414l6.293 6.293a1 1 0 001.414-1.414L11.414 10l6.293-6.293a1 1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Popup Title -->
      <h2 class="!text-xs !font-bold mb-4 !-mt-11 -ml-10">{{ title }}</h2>

      <!-- Form Fields -->
      <div class="mb-12">
        <!-- 1) Type de Réclamation -->
        <div class="mb-4 -ml-10">
          <label class="font-bold block text-xs mb-1"
            >Type de Réclamation</label
          >
          <select
            v-model="typeReclamation"
            class="border p-1 rounded w-full !text-sm"
          >
            <option value="modification des informations">
              modification des informations
            </option>
            <option value="erreur de facturation">erreur de facturation</option>
            <option value="autre">autre</option>
          </select>
        </div>

        <!-- 2) Date d'échéance -->
        <div class="mb-4 -ml-10">
          <label class="font-bold block text-xs mb-1">Date d'échéance</label>
          <input
            type="date"
            v-model="dueDate"
            class="border p-1 rounded w-full !text-sm"
          />
        </div>

        <!-- 3) Description -->
        <div class="mb-4 -ml-10">
          <label class="font-bold block text-xs mb-1">Description</label>
          <textarea
            v-model="description"
            rows="3"
            class="border p-1 rounded w-full !text-sm"
          ></textarea>
        </div>

        <div class="mb-4 -ml-10">
          <label class="font-bold block text-xs mb-1">
            Télécharger des fichiers
          </label>
          <div>
            <div class="flex items-center justify-between mb-3">
              <div class="border-1 border-gray-100 rounded p-3">
                <!-- Header row with 'Files Attachment' + 'Upload File' button -->

                <div>
                  <h3
                    class="font-semibold !text-sm p-2 border-1 border-gray-100 rounded -mt-4"
                  >
                    Files Attachment
                  </h3>

                  <div
                    v-for="(file, index) in files"
                    :key="index"
                    class="flex items-center justify-between bg-white p-2 mb-2 rounded text-xs text-gray-600 border-1 border-gray-200 border-dashed"
                  >
                    <!-- File info -->
                    <div class="flex items-center space-x-2">
                      <!-- Example file icon (optional) -->
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                      <div>
                        <span class="block font-medium text-gray-800">{{
                          file.name
                        }}</span>
                      </div>
                    </div>

                    <!-- Remove button -->
                    <button
                      class="text-red-500 hover:underline"
                      @click="removeFile(index)"
                    >
                      <svg
                        width="17"
                        height="26"
                        viewBox="0 0 17 26"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M11.839 8.63008H15.0787V9.92675H13.7828V18.3551C13.7828 18.5366 13.7202 18.6901 13.5949 18.8154C13.4697 18.9407 13.3163 19.0034 13.1349 19.0034H4.0639C3.88248 19.0034 3.72914 18.9407 3.60387 18.8154C3.47861 18.6901 3.41597 18.5366 3.41597 18.3551V9.92675H2.12012V8.63008H5.35976V6.68509C5.35976 6.50355 5.42239 6.35011 5.54766 6.22477C5.67292 6.09942 5.82627 6.03675 6.00769 6.03675H11.1911C11.3725 6.03675 11.5259 6.09942 11.6511 6.22477C11.7764 6.35011 11.839 6.50355 11.839 6.68509V8.63008ZM12.487 9.92675H4.71183V17.7068H12.487V9.92675ZM6.65562 11.8718H7.95147V15.7618H6.65562V11.8718ZM9.24733 11.8718H10.5432V15.7618H9.24733V11.8718ZM6.65562 7.33342V8.63008H10.5432V7.33342H6.65562Z"
                          fill="#878A99"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <label
                class="bg-white text-red-500 border border-red-300 px-3 py-1 rounded cursor-pointer hover:bg-red-100 text-xs ml-4"
              >
                <input type="file" class="hidden" @change="handleFileUpload" />
                <div class="flex items center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="h-4 w-4 mr-1"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
                    />
                  </svg>
                  <p class="whitespace-nowrap font-semibold">Upload File</p>
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="absolute bottom-4 right-4 flex space-x-2">
        <button
          @click="handleCancel"
          class="bg-white text-[#c62027] border-1 border-[#c62027] !px-8 py-1 !rounded-full !text-xs !mr-3 !font-bold"
        >
          Annuler
        </button>
        <button
          @click="handleSave"
          class="bg-[#C62027] text-white px-8 py-1 !rounded-full !font-bold hover:bg-red-700 transition !text-xs"
        >
          Valider
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Réclamation",
  },
});

const emit = defineEmits(["save", "cancel"]);

const typeReclamation = ref("modification des informations");
const dueDate = ref("2025-09-24");
const description = ref("");
const files = ref([]);

function handleCancel() {
  emit("cancel");
}

function handleSave() {
  const payload = {
    typeReclamation: typeReclamation.value,
    dueDate: dueDate.value,
    description: description.value,
    files: files.value,
  };
  emit("save", payload);
}

function handleFileUpload(e) {
  const selectedFiles = e.target.files;
  if (!selectedFiles.length) return;
  for (let i = 0; i < selectedFiles.length; i++) {
    files.value.push(selectedFiles[i]);
  }

  e.target.value = "";
}

function removeFile(index) {
  files.value.splice(index, 1);
}
</script>
