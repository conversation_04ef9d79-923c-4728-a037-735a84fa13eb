<script setup>
import { defineProps } from "vue";
import { ref, defineEmits } from "vue";
import EditSitesModal from "./EditSitesModal.vue";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  sites: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["updateSites"]);

const showEditModal = ref(false);

const localSites = ref([...props.sites]);

function openEditModal() {
  localSites.value = JSON.parse(JSON.stringify(props.sites));
  showEditModal.value = true;
}

async function handleSave(updatedSites) {
  try {
    await emit("updateSites", updatedSites);
    localSites.value = updatedSites;
    showEditModal.value = false;
  } catch (error) {
    // If an error is thrown from the parent component, keep the modal open
    console.error("Error in updateSites:", error);
  }
}

function handleCancel() {
  showEditModal.value = false;
}
</script>

<template>
  <div class="card mb-3 border-0 bg-white shadow !rounded-2xl px-4 py-3">
    <div class="card-body">
      <button class="absolute top-4 right-4" @click="openEditModal">
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.4257 4H6.55762C5.45305 4 4.55762 4.89543 4.55762 6V18C4.55762 19.1046 5.45305 20 6.55762 20H18.2938C19.3983 20 20.2938 19.1046 20.2938 18V12M9.47517 15V12.5L18.0604 3.77078C18.7473 3.07239 19.8732 3.07239 20.5601 3.77078V3.77078C21.231 4.45294 21.231 5.54706 20.5601 6.22922L15.868 11L11.9339 15H9.47517Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <!-- Card Header -->
      <div class="d-flex justify-content-between align-items-center mb-6">
        <h5 class="card-title mb-0 mx-auto !text-sm !text-[#3B4758] !font-bold">
          {{ title }}
        </h5>
        <i class="ri-edit-box-line" style="cursor: pointer"></i>
      </div>

      <!-- Table for Name + Capacity -->
      <table
        class="table table-borderless align-middle mb-0 !border-spacing-y-10"
      >
        <thead>
          <tr>
            <th scope="col" class="!text-[#7D8FA9] !text-xs">Name</th>
            <th
              scope="col"
              class="!text-[#7D8FA9] !text-xs"
              style="width: 100px"
            >
              Capacity
            </th>
          </tr>
        </thead>
        <tbody class="!w-1/3 !space-y-6">
          <tr v-for="(site, index) in sites" :key="index" class="!py-10">
            <td>
              <div class="flex items-baseline">
                <!-- Show the flag/logo if provided -->
                <template v-if="site.flagUrl">
                  <img
                    :src="site.flagUrl"
                    alt="Flag"
                    class="me-2"
                    style="width: 24px; height: auto"
                  />
                </template>
                <span class="!text-[#3B4758] !font-bold !text-sm">{{
                  site.name
                }}</span>
              </div>
            </td>
            <td>
              <span class="!text-[#586A84] !font-semibold !text-sm mb-3">{{
                site.capacity
              }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <EditSitesModal
    v-if="showEditModal"
    :title="title"
    :sites="localSites"
    @save="handleSave"
    @cancel="handleCancel"
  />
</template>
