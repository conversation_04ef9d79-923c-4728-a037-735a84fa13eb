<template>
  <div class="bg-white rounded-2xl shadow-sm p-6">
    <!-- header -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-900">
        Recherche & Développement
      </h3>
      <button
        @click="$emit('edit')"
        class="p-2 rounded hover:bg-gray-100 focus:outline-none"
        aria-label="Edit research data"
      >
        <!-- pencil icon -->
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.5 4H6.5C5.39543 4 4.5 4.89543 4.5 6V18C4.5 19.1046 5.39543 20 6.5 20H18.5C19.6046 20 20.5 19.1046 20.5 18V12M9.5 15V12.5L18.25 3.75C18.9404 3.05964 20.0596 3.05964 20.75 3.75V3.75C21.4404 4.44036 21.4404 5.55964 20.75 6.25L16 11L12 15H9.5Z"
            stroke="black"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- content -->
    <div class="space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p class="text-sm font-medium text-gray-700">
            Budget alloué à la R&D en 2024 :
          </p>
          <p class="text-sm text-gray-900">
            {{ formatValue(props.researchData.budget2024, "M") }}
          </p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-700">
            Pourcentage du CA dédié à la R&D :
          </p>
          <p class="text-sm text-gray-900">
            {{ formatValue(props.researchData.revenuePercentage, "%") }}
          </p>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p class="text-sm font-medium text-gray-700">
            Nombre de projets R&D en cours :
          </p>
          <p class="text-sm text-gray-900">
            {{ formatValue(props.researchData.projectsInProgress) }}
          </p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-700">
            Nombre de brevets déposés en 2023 – 2024 :
          </p>
          <p class="text-sm text-gray-900">
            {{ formatValue(props.researchData.patentsCount) }}
          </p>
        </div>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-700">
          Partenariats avec des universités ou laboratoires :
        </p>
        <p class="text-sm text-gray-900">
          {{ props.researchData.universityPartnerships ? "Oui" : "Non" }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  researchData: {
    type: Object,
    default: () => ({
      budget2024: null,
      patentsCount: null,
      revenuePercentage: null,
      universityPartnerships: false,
      projectsInProgress: null,
    }),
  },
});

const emit = defineEmits(["edit"]);

// Helper function to format values with units or handle null/undefined
function formatValue(value, unit = "") {
  if (value === null || value === undefined) return "N/A";
  return `${value}${unit}`;
}
</script>
