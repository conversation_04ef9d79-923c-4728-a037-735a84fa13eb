<template>
  <div
    class="flex items-center justify-between bg-white mb-8 shadow p-4 rounded-md"
  >
    <div>
      <BProgress
        class="mt-2 w-xl mb-4"
        :value="progressValue"
        variant="danger"
        style="height: 6px"
      />
      <span class="!text-sm"
        >This page is
        <span class="text-red-500 font-bold !text-xs"
          >{{ progressValue }}%</span
        >
        complete – add more details to boost visibility!</span
      >
    </div>
    <div>
      <BButton
        @click="showReclamation = true"
        class="!text-xs !font-bold !bg-[#C62027] !rounded-full ! text-white px-4 py-2"
        >Demande de Modification</BButton
      >
      <ReclamationPopup
        v-if="showReclamation"
        @save="onReclamationSave"
        @cancel="showReclamation = false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import ReclamationPopup from "./ReclamationPopup.vue";

defineProps({
  progressValue: Number,
});
const showReclamation = ref(false);

function onReclamationSave(data) {
  console.log("Reclamation form data:", data);
  // handle or store data
  showReclamation.value = false;
}
</script>
