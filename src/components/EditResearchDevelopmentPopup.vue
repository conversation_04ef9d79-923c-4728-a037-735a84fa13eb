<template>
  <EditPopup
    :items="formattedItems"
    :title="title"
    @save="handleSave"
    @cancel="$emit('cancel')"
  />
</template>

<script setup>
import { computed, defineProps, defineEmits } from "vue";
import EditPopup from "./EditPopup.vue";

const props = defineProps({
  title: {
    type: String,
    default: "Recherche & Développement",
  },
  researchData: {
    type: Object,
    default: () => ({
      budget2024: null,
      patentsCount: null,
      revenuePercentage: null,
      universityPartnerships: false,
      projectsInProgress: null,
    }),
  },
});

const emit = defineEmits(["save", "cancel"]);

// Format the research data for the EditPopup component
const formattedItems = computed(() => {
  console.log(
    "EditResearchDevelopmentPopup - Formatting research data:",
    props.researchData
  );

  const items = [
    {
      label: "Budget alloué à la R&D en 2024 (en millions)",
      value:
        props.researchData.budget2024 !== null
          ? props.researchData.budget2024.toString()
          : "",
      type: "number",
      placeholder: "Entrez le budget en millions",
    },
    {
      label: "Pourcentage du CA dédié à la R&D",
      value:
        props.researchData.revenuePercentage !== null
          ? props.researchData.revenuePercentage.toString()
          : "",
      type: "number",
      placeholder: "Entrez le pourcentage",
    },
    {
      label: "Nombre de projets R&D en cours",
      value:
        props.researchData.projectsInProgress !== null
          ? props.researchData.projectsInProgress.toString()
          : "",
      type: "number",
      placeholder: "Entrez le nombre de projets",
    },
    {
      label: "Nombre de brevets déposés en 2023 – 2024",
      value:
        props.researchData.patentsCount !== null
          ? props.researchData.patentsCount.toString()
          : "",
      type: "number",
      placeholder: "Entrez le nombre de brevets",
    },
    {
      label: "Partenariats avec des universités ou laboratoires",
      value: props.researchData.universityPartnerships ? "Oui" : "Non",
      type: "select",
      options: ["Oui", "Non"],
    },
  ];

  console.log("EditResearchDevelopmentPopup - Formatted items:", items);
  return items;
});

// Handle the save event from the EditPopup
function handleSave(updatedItems) {
  console.log(
    "EditResearchDevelopmentPopup - handleSave called with:",
    updatedItems
  );

  // Convert the updated items back to the format expected by the backend
  const updatedResearchData = {
    budget2024: parseNumberInput(updatedItems[0].value),
    revenuePercentage: parseNumberInput(updatedItems[1].value),
    projectsInProgress: parseNumberInput(updatedItems[2].value),
    patentsCount: parseNumberInput(updatedItems[3].value),
    universityPartnerships: updatedItems[4].value === "Oui",
  };

  console.log(
    "EditResearchDevelopmentPopup - Converted data:",
    updatedResearchData
  );

  // Emit the save event with the updated research data
  emit("save", updatedResearchData);
}

// Helper function to parse a number input
function parseNumberInput(value) {
  // Handle null, undefined, or empty values
  if (value === null || value === undefined) return null;

  // If value is already a number, return it
  if (typeof value === "number") return value;

  // Convert to string and check if it's empty
  const valueStr = String(value);
  if (valueStr.trim() === "") return null;

  // Parse the string to a number
  const parsed = parseFloat(valueStr);
  return isNaN(parsed) ? null : parsed;
}
</script>
