<template>
  <!-- Preview Overlay -->
  <Transition name="preview">
    <div v-if="isVisible" class="fixed inset-0 z-50 flex">
      <!-- Background blur overlay -->
      <div
        class="absolute inset-0 !bg-opacity-30 backdrop-blur-sm"
        @click="closePreview"
      ></div>
      <!-- Preview Panel -->
      <div
        class="ml-auto w-[65%] bg-[#C62027] h-full relative !rounded-t-4xl flex flex-col"
      >
        <!-- Red Header -->
        <div class="bg-[#C62027] text-white p-6 py-10 flex-shrink-0">
          <h1 class="text-xl font-semibold ml-20">
            {{ dynamicTitle }}
          </h1>
          <!-- Close Button -->
          <button
            @click="closePreview"
            class="absolute top-6 right-6 text-white hover:text-gray-200 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <!-- Content Area with proper scrolling -->
        <div
          class="bg-gray-200 flex-1 overflow-hidden !rounded-t-4xl flex flex-col"
        >
          <!-- Navigation Tabs -->
          <div class="bg-gray-200 px-6 pt-4 flex-shrink-0">
            <nav class="flex !space-x-12 ml-20">
              <button
                v-for="tab in tabs"
                :key="tab.key"
                @click="activeTab = tab.key"
                class="relative pb-3 text-sm font-medium transition-colors"
                :class="[
                  activeTab === tab.key
                    ? 'border-b-2 border-red-500 text-red-500'
                    : 'text-gray-600 hover:text-gray-800',
                ]"
              >
                {{ tab.label }}
              </button>
            </nav>
          </div>

          <!-- Scrollable Tab Content -->
          <div class="flex-1 overflow-y-auto">
            <div class="px-6 py-6 ml-20">
              <div v-if="activeTab === 'Local Plant'">
                <PreviewExternalViewsSection
                  title="Vue Extérieure"
                  :data="externalViewsData"
                />
                <PreviewExternalViewsSection
                  title="Vue Intérieure"
                  :data="internalViewsData"
                />
              </div>
              <div v-if="activeTab === 'Production'">
                <PreviewSection title="Emplacement" :data="locationData" />
                <PreviewProductionSection
                  title="Production mondiale"
                  :data="productionData"
                />
                <PreviewProductsSection
                  title="Principaux produits"
                  :products="productsData"
                />
              </div>
              <div v-if="activeTab === 'Employee'">
                <PreviewClientsSection
                  title="Principaux clients"
                  :clients="clientsData"
                />
              </div>
              <div v-if="activeTab === 'Autres'">
                <PreviewSection
                  title="Investissement en R&D"
                  :data="investmentsData"
                />
                <PreviewCertificationsSection
                  title="Certifications"
                  :certifications="certificationsData"
                />
                <PreviewSection title="Ventes" :data="salesData" />
              </div>
              <!-- Other tab content will go here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>
<script setup>
import { ref, defineProps, defineEmits, onMounted, computed, watch } from "vue";
import PreviewSection from "./PreviewSection.vue";
import PreviewProductionSection from "./PreviewProductionSection.vue";
import PreviewProductsSection from "./PreviewProductsSection.vue";
import PreviewClientsSection from "./PreviewClientsSection.vue";
import PreviewCertificationsSection from "./PreviewCertificationsSection.vue";
import PreviewExternalViewsSection from "./PreviewExternalViewsSection.vue";
import organisationService from "../Services/services/organisation";
import productsService from "../Services/services/products";
import revenueService from "../Services/services/revenue";
import extrasService from "../Services/services/extras";

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  organizationId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["close"]);

// Tab management
const activeTab = ref("Local Plant");
const tabs = [
  { label: "Local Plant", key: "Local Plant" },
  { label: "Production", key: "Production" },
  { label: "Employee", key: "Employee" },
  { label: "Autres", key: "Autres" },
];

// Organization data
const organization = ref(null);
const products = ref(null);
const revenues = ref(null);
const extras = ref(null);
const loading = ref(false);
const error = ref(null);

// Fetch organization data
const fetchOrganizationData = async () => {
  if (!props.organizationId) return;

  loading.value = true;
  error.value = null;

  try {
    const response = await organisationService.getOrganization(
      props.organizationId
    );
    organization.value = response.data;
  } catch (err) {
    console.error("Error fetching organization data:", err);
    error.value = err.message || "Failed to fetch organization data";
  } finally {
    loading.value = false;
  }
};

// Fetch products data
const fetchProductsData = async () => {
  if (!props.organizationId) return;

  try {
    const response = await productsService.getProducts(props.organizationId);
    products.value = response.data;
  } catch (err) {
    console.error("Error fetching products data:", err);
  }
};

// Fetch revenue data
const fetchRevenueData = async () => {
  if (!props.organizationId) return;

  try {
    const response = await revenueService.getRevenues(props.organizationId);
    revenues.value = response.data;
  } catch (err) {
    console.error("Error fetching revenue data:", err);
  }
};

// Fetch extras data
const fetchExtrasData = async () => {
  if (!props.organizationId) return;

  try {
    const response = await extrasService.getExtras(props.organizationId);
    extras.value = response.data;
  } catch (err) {
    console.error("Error fetching extras data:", err);
  }
};

console.log(organization.value);

// Computed property for dynamic title
const dynamicTitle = computed(() => {
  if (!organization.value || !organization.value.name) {
    return "Company Overview";
  }
  return `${organization.value.name} - Company Overview`;
});

// Computed property for location data
const locationData = computed(() => {
  if (!organization.value) {
    // Return default data for preview if no organization data is available
    return [
      { label: "HQ", value: "Tunis, Ariana" },
      { label: "R&D", value: "Ariana, Tunisia" },
    ];
  }

  return [
    {
      label: "HQ",
      value:
        organization.value.rAndDSites &&
        organization.value.rAndDSites.length > 0
          ? organization.value.rAndDSites[0].name
          : "No R&D site available",
    },
    {
      label: "R&D",
      value:
        organization.value.otherLocations &&
        organization.value.otherLocations.length > 0
          ? organization.value.otherLocations[0].name
          : "No other location available",
    },
  ];
});

// Computed property for production data
const productionData = computed(() => {
  if (!products.value || !products.value.foreignImplantationSites) {
    // Return default data for preview if no products data is available
    return [
      { name: "France", capacity: "1000" },
      { name: "Germany", capacity: "800" },
      { name: "China", capacity: "1200" },
    ];
  }

  // Return the foreignImplantationSites data directly
  return products.value.foreignImplantationSites.map((site) => ({
    name: site.name || "Unknown Country",
    capacity: site.capacity || "-",
  }));
});

// Computed property for products data
const productsData = computed(() => {
  if (!products.value || !products.value.products) {
    // Return default data for preview if no products data is available
    return [
      {
        name: "Product_name",
        description: "Product Description can be put here",
        image: null, // Will show placeholder image
      },
      {
        name: "Product_name",
        description: "Product Description can be put here",
        image: null, // Will show placeholder image
      },
      {
        name: "Product_name",
        description: "Product Description can be put here",
        image: null, // Will show placeholder image
      },
    ];
  }

  // Return the products array with name, description, and image
  return products.value.products.map((product) => ({
    name: product.name || "Product_name",
    description: product.description || "Product Description can be put here",
    image: product.image || null, // Add image support
  }));
});

// Computed property for clients data
const clientsData = computed(() => {
  if (!revenues.value || !revenues.value.clientsTypes) {
    // Return default data for preview if no revenue data is available
    return [
      { name: "Nicola Brown", description: "graphic designer" },
      { name: "William Acadel", description: "acrobat" },
      { name: "Nicola Brown", description: "graphic designer" },
      { name: "Nicolae O'Baneon", description: "nature science enthusiast" },
      { name: "Irina Potkapova", description: "net developer" },
      { name: "Nicolae O'Baneon", description: "nature science enthusiast" },
    ];
  }

  // Return the clients array using the example field from clientsTypes
  return revenues.value.clientsTypes.map((client) => ({
    name: client.example || "Client Name",
    description: client.type || "Client Type",
  }));
});

// Computed property for investments data
const investmentsData = computed(() => {
  if (!extras.value || !extras.value.investments) {
    // Return default data for preview if no extras data is available
    return [
      { label: "Projet Innovation Digitale", value: "150 000 TND" },
      { label: "Développement Produit A", value: "200 000 TND" },
      { label: "Recherche & Développement", value: "300 000 TND" },
    ];
  }

  // Return each investment project as a separate row
  return extras.value.investments.map((investment) => ({
    label: investment.name || "Projet",
    value: investment.value
      ? `${parseFloat(investment.value).toLocaleString("fr-FR")} TND`
      : "N/A",
  }));
});

// Computed property for certifications data
const certificationsData = computed(() => {
  if (!extras.value || !extras.value.certifications) {
    // Return default data for preview if no extras data is available
    return [
      { name: "ISO 9001" },
      { name: "ISO 14001" },
      { name: "OHSAS 18001" },
      { name: "ISO 27001" },
      { name: "CE Marking" },
    ];
  }

  // Return the certifications array using name from certifications
  return extras.value.certifications.map((certification) => ({
    name: certification.name || "Certification",
  }));
});

// Computed property for sales data
const salesData = computed(() => {
  if (!revenues.value || !revenues.value.turnover) {
    // Return default data for preview if no revenue data is available
    return [
      { label: "Chiffre d'affaires en Euros (2024)", value: "2 500 000 €" },
      { label: "Evolution par rapport à 2023", value: "Oui" },
    ];
  }

  const turnover = revenues.value.turnover;

  return [
    {
      label: "Chiffre d'affaires en Euros (2024)",
      value: turnover.revenue2024
        ? `${turnover.revenue2024.toLocaleString("fr-FR")} €`
        : "N/A",
    },
    {
      label: "Evolution par rapport à 2023",
      value:
        turnover.hasGrowthComparedTo2023 !== undefined
          ? turnover.hasGrowthComparedTo2023
            ? "Oui"
            : "Non"
          : "N/A",
    },
  ];
});

// Computed property for external views data
const externalViewsData = computed(() => {
  if (!organization.value) {
    // Return default data for preview if no organization data is available
    return {
      images: [],
      info: [
        { label: "Nom Complet De L'entreprise", value: "Entreprise_full_name" },
        { label: "Année Préparatoire", value: "2017" },
      ],
    };
  }

  return {
    images: organization.value.externalViews || [],
    info: [
      {
        label: "Nom Complet De L'entreprise",
        value: organization.value.name || "Entreprise_full_name",
      },
      {
        label: "Année Préparatoire",
        value: organization.value.foundingYear || "2017",
      },
    ],
  };
});

// Computed property for internal views data
const internalViewsData = computed(() => {
  if (!organization.value) {
    // Return default data for preview if no organization data is available
    return {
      images: [],
      info: [{ label: "Adresse", value: "Adresse de l'entreprise" }],
    };
  }

  return {
    images: organization.value.internalViews || [],
    info: [
      {
        label: "Adresse",
        value: organization.value.address || "Adresse de l'entreprise",
      },
    ],
  };
});

// Watch for visibility changes to fetch data when the preview is opened
onMounted(() => {
  if (props.isVisible && props.organizationId) {
    fetchOrganizationData();
    fetchProductsData();
    fetchRevenueData();
    fetchExtrasData();
  }
});

// Watch for changes in visibility
watch(
  () => props.isVisible,
  (newValue) => {
    if (newValue && props.organizationId) {
      fetchOrganizationData();
      fetchProductsData();
      fetchRevenueData();
      fetchExtrasData();
    }
  }
);

// Close preview function
const closePreview = () => {
  emit("close");
};
</script>
<style scoped>
/* Preview slide-in animation */
.preview-enter-active,
.preview-leave-active {
  transition: all 0.3s ease;
}
.preview-enter-from {
  transform: translateX(100%);
  opacity: 0;
}
.preview-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
/* Ensure the panel slides in from the right */
.preview-enter-active .ml-auto,
.preview-leave-active .ml-auto {
  transition: transform 0.3s ease;
}
.preview-enter-from .ml-auto {
  transform: translateX(100%);
}
.preview-leave-to .ml-auto {
  transform: translateX(100%);
}
</style>
