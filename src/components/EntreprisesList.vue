<template>
  <BCol xl="12" class="!flex">
    <aside class="w-[20%] !bg-white !p-4 !mr-4 !rounded !shadow">
      <!-- Search Input -->
      <div class="!mb-6">
        <div class="relative">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Recherche"
            class="!w-full !pl-10 !pr-4 !py-2 !border !border-gray-300 !rounded-lg !text-sm focus:!outline-none focus:!ring-2 focus:!ring-red-500 focus:!border-red-500"
            @input="handleSearch"
          />
          <svg
            class="!absolute !left-3 !top-2.5 !w-4 !h-4 !text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <!-- Activity Filter -->
      <div class="!mb-6">
        <h3 class="!text-sm !font-medium !text-gray-400 !mb-3">
          ACTIVITÉ PRINCIPALE
        </h3>
        <div class="!space-y-2">
          <label
            v-for="activity in activities"
            :key="activity.id"
            class="!flex !items-center !text-sm"
          >
            <input
              type="checkbox"
              v-model="selectedActivities"
              :value="activity.id"
              class="!mr-2 !text-red-500 focus:!ring-red-500"
              @change="handleActivityFilter"
            />
            <span class="!text-gray-700">{{ activity.name }}</span>
          </label>
        </div>
      </div>

      <!-- City Filter -->
      <div class="!mb-6">
        <h3 class="!text-sm !font-medium !text-gray-400 !mb-3">VILLE</h3>
        <select
          v-model="selectedCity"
          class="!w-full !p-2 !border !border-gray-300 !rounded-lg !text-sm focus:!outline-none focus:!ring-2 focus:!ring-red-500 focus:!border-red-500"
          @change="handleCityFilter"
        >
          <option value="">Sélectionner une ville</option>
          <option v-for="city in cities" :key="city" :value="city">
            {{ city }}
          </option>
        </select>
      </div>

      <!-- Capital Range Filter -->
      <div class="!mb-6">
        <h3 class="!text-sm !font-medium !text-gray-400 !mb-3">CAPITAL</h3>
        <div class="!space-y-3">
          <div class="!relative !h-6">
            <div class="dual-range-slider">
              <input
                type="range"
                min="0"
                max="1000000000"
                v-model="capitalRange.min"
                class="range-input range-input-min"
                @input="handleCapitalFilter"
              />
              <input
                type="range"
                min="0"
                max="1000000000"
                v-model="capitalRange.max"
                class="range-input range-input-max"
                @input="handleCapitalFilter"
              />
              <div class="range-track"></div>
              <div
                class="range-fill"
                :style="getRangeFillStyle('capital')"
              ></div>
            </div>
          </div>
          <div class="!flex !justify-between !text-xs !text-gray-600">
            <input
              type="text"
              v-model="capitalRange.min"
              placeholder="TND"
              class="!w-16 !p-1 !border !border-gray-300 !rounded !text-xs"
              @input="handleCapitalFilter"
            />
            <span class="!self-center">to</span>
            <input
              type="text"
              v-model="capitalRange.max"
              placeholder="TND"
              class="!w-20 !p-1 !border !border-gray-300 !rounded !text-xs"
              @input="handleCapitalFilter"
            />
          </div>
        </div>
      </div>

      <!-- Founding Year Filter -->
      <div class="!mb-6">
        <h3 class="!text-sm !font-medium !text-gray-400 !mb-3">
          ANNÉE DE CRÉATION
        </h3>
        <input
          type="number"
          v-model="foundingYear"
          placeholder="1998"
          min="1900"
          max="2024"
          class="!w-full !p-2 !border !border-gray-300 !rounded-lg !text-sm focus:!outline-none focus:!ring-2 focus:!ring-red-500 focus:!border-red-500"
          @input="handleFoundingYearFilter"
        />
      </div>

      <!-- Employees Range Filter -->
      <div class="!mb-6">
        <h3 class="!text-sm !font-medium !text-gray-400 !mb-3">
          NOMBRE D'EMPLOYÉS
        </h3>
        <div class="!space-y-3">
          <div class="!relative !h-6">
            <div class="dual-range-slider">
              <input
                type="range"
                min="0"
                max="10000"
                v-model="employeesRange.min"
                class="range-input range-input-min"
                @input="handleEmployeesFilter"
              />
              <input
                type="range"
                min="0"
                max="10000"
                v-model="employeesRange.max"
                class="range-input range-input-max"
                @input="handleEmployeesFilter"
              />
              <div class="range-track"></div>
              <div
                class="range-fill"
                :style="getRangeFillStyle('employees')"
              ></div>
            </div>
          </div>
          <div class="!flex !justify-between !text-xs !text-gray-600">
            <input
              type="text"
              v-model="employeesRange.min"
              placeholder="0"
              class="!w-12 !p-1 !border !border-gray-300 !rounded !text-xs"
              @input="handleEmployeesFilter"
            />
            <input
              type="text"
              v-model="employeesRange.max"
              placeholder="10 000"
              class="!w-16 !p-1 !border !border-gray-300 !rounded !text-xs"
              @input="handleEmployeesFilter"
            />
          </div>
        </div>
      </div>

      <!-- Partners Filter -->
      <div class="!mb-6">
        <h3 class="!text-sm !font-medium !text-gray-400 !mb-3">
          PARTENAIRES DANS LE MONDE
        </h3>
        <div class="!space-y-2">
          <label
            v-for="partner in partners"
            :key="partner.value"
            class="!flex !items-center !text-sm"
          >
            <input
              type="checkbox"
              v-model="selectedPartners"
              :value="partner.value"
              class="!mr-2 !text-red-500 focus:!ring-red-500"
              @change="handlePartnersFilter"
            />
            <span class="!text-gray-700">{{ partner.label }}</span>
          </label>
        </div>
      </div>
    </aside>
    <div class="p-4 w-[80%]">
      <div v-if="loading" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>

      <div v-else-if="error" class="alert alert-danger" role="alert">
        {{ error }}
      </div>

      <div v-else class="custom-table">
        <!-- Header Row -->
        <div
          class="table-row header-row bg-white rounded mb-2 p-3 shadow-sm !gap-1"
        >
          <div class="col-company !mr-1 !text-sm !font-bold">
            Nom de l'entreprise
          </div>
          <div class="col-year !mr-1 !text-sm !font-bold">
            Année de création
          </div>
          <div class="col-activity !mr-1 !text-sm !font-bold">
            Activité principale
          </div>
          <div class="col-actions !text-sm !font-bold"></div>
        </div>

        <!-- Data Rows -->
        <div
          v-for="organization in organizations"
          :key="organization.id"
          class="table-row data-row bg-white rounded mb-2 p-3 shadow-sm !gap-1"
        >
          <div class="col-company !mr-1">
            <div class="d-flex align-items-center">
              <div class="me-3">
                <div v-if="organization.logoUrl" class="avatar-sm">
                  <img
                    :src="organization.logoUrl"
                    :alt="organization.name"
                    class="w-100 h-100 object-fit-cover rounded"
                    @error="handleImageError"
                  />
                </div>
                <div
                  v-else
                  class="avatar-sm bg-light d-flex align-items-center justify-content-center rounded"
                >
                  <span class="text-muted fw-medium small">{{
                    getInitials(organization.name)
                  }}</span>
                </div>
              </div>
              <div>
                <h6 class="mb-0 fw-medium text-dark">
                  {{ organization.name }}
                </h6>
              </div>
            </div>
          </div>
          <div class="col-year !mr-1">
            <span class="text-muted !ml-12">{{
              organization.foundingYear || "N/A"
            }}</span>
          </div>
          <div class="col-activity !ml-12">
            <span class="text-muted">{{
              getFirstActivity(organization.primaryActivities)
            }}</span>
          </div>
          <div class="col-actions">
            <div class="d-flex gap-2 align-items-center">
              <button
                @click="openPreview(organization.id)"
                class="btn btn-sm btn-outline-secondary d-flex align-items-center justify-content-center !bg-red-100"
                title="Preview"
                style="width: 32px; height: 32px"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="red"
                  />
                  <circle
                    cx="12"
                    cy="12"
                    r="4"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="red"
                  />
                </svg>
              </button>
              <button
                @click="openPreview(organization.id)"
                class="bg-red-100 !text-[#C62027] !text-xs py-2 px-3 border !border-[#C62027] !rounded"
                title="Source Book"
              >
                Source Book
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div
        class="d-flex justify-content-end mt-4"
        v-if="!loading && !error && organizations.length > 0"
      >
        <nav>
          <ul class="pagination mb-0">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <button
                class="btn btn-outline-secondary me-2"
                @click="changePage(currentPage - 1)"
                :disabled="currentPage === 1"
              >
                Previous
              </button>
            </li>
            <li v-for="page in visiblePages" :key="page" class="page-item mx-1">
              <button
                class="btn"
                :class="
                  page === currentPage ? 'btn-danger' : 'btn-outline-secondary'
                "
                @click="changePage(page)"
              >
                {{ page }}
              </button>
            </li>
            <li
              class="page-item"
              :class="{ disabled: currentPage === totalPages }"
            >
              <button
                class="btn btn-outline-secondary ms-2"
                @click="changePage(currentPage + 1)"
                :disabled="currentPage === totalPages"
              >
                Next
              </button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </BCol>

  <!-- Preview Component -->
  <Preview
    :isVisible="showPreview"
    :organizationId="selectedOrganizationId"
    @close="closePreview"
  />
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { BCol } from "bootstrap-vue-next";
import organisationService from "../Services/services/organisation";
import activitiesService from "../Services/services/activities";
import Preview from "./Preview.vue";

// Reactive data
const organizations = ref([]);
const loading = ref(false);
const error = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(0);
const showPreview = ref(false);
const selectedOrganizationId = ref("");

// Filter data
const searchQuery = ref("");
const activities = ref([]);
const selectedActivities = ref([]);
const selectedCity = ref("");
const capitalRange = ref({ min: 0, max: 1000000000 });
const foundingYear = ref("");
const employeesRange = ref({ min: 0, max: 10000 });
const selectedPartners = ref([]);

// Static data for cities
const cities = ref([
  "Tunis",
  "Ariana",
  "Ben Arous",
  "La Manouba",
  "Bizerte",
  "Béja",
  "Jendouba",
  "Kairouan",
  "Kasserine",
  "Kébili",
  "Kéf",
  "Gabes",
  "Gafsa",
  "Mahdia",
  "Medenine",
  "Monastir",
  "Nabeul",
  "Sfax",
  "Sidi Bouzid",
  "Siliana",
  "Sousse",
  "Tataouine",
  "Tozeur",
  "Zaghouan",
]);

// Static data for partners
const partners = ref([
  { value: "afrique", label: "Afrique" },
  { value: "amerique_nord", label: "Amérique du Nord" },
  { value: "amerique_sud", label: "Amérique du Sud" },
  { value: "europe", label: "Europe" },
  { value: "asie", label: "Asie" },
  { value: "australie", label: "Australie" },
]);

// Computed properties
const totalPages = computed(() =>
  Math.ceil(totalItems.value / itemsPerPage.value)
);
const startIndex = computed(
  () => (currentPage.value - 1) * itemsPerPage.value + 1
);
const endIndex = computed(() =>
  Math.min(currentPage.value * itemsPerPage.value, totalItems.value)
);

const visiblePages = computed(() => {
  const pages = [];
  const start = Math.max(1, currentPage.value - 2);
  const end = Math.min(totalPages.value, currentPage.value + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// Methods
const fetchOrganizations = async (page = 1) => {
  loading.value = true;
  error.value = null;

  try {
    const filters = buildFilters();
    const response = await organisationService.getOrganizations({
      page: page - 1, // Backend might use 0-based pagination
      limit: itemsPerPage.value,
      ...filters,
    });

    organizations.value = response.data.data || response.data;
    totalItems.value = response.data.total || response.data.length;
    currentPage.value = page;
  } catch (err) {
    console.error("Error fetching organizations:", err);
    error.value = "Failed to load organizations. Please try again.";
  } finally {
    loading.value = false;
  }
};

const buildFilters = () => {
  const filters = {};

  if (searchQuery.value) {
    filters.name = searchQuery.value;
  }

  if (selectedActivities.value.length > 0) {
    filters["organizationActivities.activityId"] = selectedActivities.value;
  }

  if (selectedCity.value) {
    filters.city = selectedCity.value;
  }

  if (foundingYear.value) {
    filters.foundingYear = parseInt(foundingYear.value);
  }

  return filters;
};

const fetchActivities = async () => {
  try {
    const response = await activitiesService.getActivities();
    activities.value = response.data || [];
  } catch (err) {
    console.error("Error fetching activities:", err);
  }
};

// Filter handlers
const handleSearch = () => {
  fetchOrganizations(1);
};

const handleActivityFilter = () => {
  fetchOrganizations(1);
};

const handleCityFilter = () => {
  fetchOrganizations(1);
};

const handleCapitalFilter = () => {
  // TODO: Implement capital filter when backend supports it
  console.log("Capital filter:", capitalRange.value);
};

const handleFoundingYearFilter = () => {
  fetchOrganizations(1);
};

const handleEmployeesFilter = () => {
  // TODO: Implement employees filter when backend supports it
  console.log("Employees filter:", employeesRange.value);
};

const handlePartnersFilter = () => {
  // TODO: Implement partners filter when backend supports it
  console.log("Partners filter:", selectedPartners.value);
};

const getRangeFillStyle = (type) => {
  let min, max, rangeMin, rangeMax;

  if (type === "capital") {
    min = parseInt(capitalRange.value.min);
    max = parseInt(capitalRange.value.max);
    rangeMin = 0;
    rangeMax = 1000000000;
  } else if (type === "employees") {
    min = parseInt(employeesRange.value.min);
    max = parseInt(employeesRange.value.max);
    rangeMin = 0;
    rangeMax = 10000;
  }

  const leftPercent = ((min - rangeMin) / (rangeMax - rangeMin)) * 100;
  const rightPercent = ((max - rangeMin) / (rangeMax - rangeMin)) * 100;

  return {
    left: `${leftPercent}%`,
    width: `${rightPercent - leftPercent}%`,
  };
};

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
    fetchOrganizations(page);
  }
};

const getFirstActivity = (activities) => {
  console.log("hi");
  if (!activities || !Array.isArray(activities) || activities.length === 0) {
    return "N/A";
  }

  const firstActivity = activities[0];
  console.log(firstActivity.name);
  return firstActivity.name || "N/A";
};

const getInitials = (name) => {
  if (!name) return "??";
  return name
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join("");
};

const handleImageError = (event) => {
  // Hide the image and show initials instead
  event.target.style.display = "none";
};

const openPreview = (organizationId) => {
  selectedOrganizationId.value = organizationId;
  showPreview.value = true;
};

const closePreview = () => {
  showPreview.value = false;
  selectedOrganizationId.value = "";
};

// Lifecycle
onMounted(() => {
  fetchOrganizations();
  fetchActivities();
});
</script>

<style scoped>
.custom-table {
  width: 100%;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 0.5fr 2fr auto;
  gap: 0.5rem;
  align-items: center;
  border: 1px solid #e9ecef;
}

.header-row {
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa !important;
}

.data-row {
  transition: all 0.2s ease;
}

.data-row:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.col-company {
  display: flex;
  align-items: center;
}

.col-year {
  text-align: left;
}

.col-activity {
  text-align: left;
}

.col-actions {
  text-align: right;
  justify-self: end;
}

.avatar-sm {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-sm img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.pagination .btn {
  min-width: 40px;
  height: 38px;
  border-radius: 6px;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.rounded {
  border-radius: 0.375rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.bg-white {
  background-color: #fff !important;
}

/* Dual Range Slider Styling */
.dual-range-slider {
  position: relative;
  width: 100%;
  height: 24px;
}

.range-input {
  position: absolute;
  width: 100%;
  height: 8px;
  background: transparent;
  appearance: none;
  pointer-events: none;
  top: 50%;
  transform: translateY(-50%);
}

.range-input::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #dc3545;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  pointer-events: all;
  position: relative;
  z-index: 2;
}

.range-input::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #dc3545;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  pointer-events: all;
  position: relative;
  z-index: 2;
}

.range-track {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 4px;
  z-index: 1;
}

.range-fill {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 4px;
  background: #c7;
  border-radius: 4px;
  z-index: 1;
}
</style>
