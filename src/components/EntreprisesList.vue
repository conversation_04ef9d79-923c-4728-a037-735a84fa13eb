<template>
  <BCol xl="12">
    <div class="p-4">
      <div v-if="loading" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>

      <div v-else-if="error" class="alert alert-danger" role="alert">
        {{ error }}
      </div>

      <div v-else class="custom-table">
        <!-- Header Row -->
        <div
          class="table-row header-row bg-white rounded mb-2 p-3 shadow-sm !gap-1"
        >
          <div class="col-company !mr-1 !text-sm !font-bold">
            Nom de l'entreprise
          </div>
          <div class="col-year !mr-1 !text-sm !font-bold">
            Année de création
          </div>
          <div class="col-activity !mr-1 !text-sm !font-bold">
            Activité principale
          </div>
          <div class="col-actions !text-sm !font-bold"></div>
        </div>

        <!-- Data Rows -->
        <div
          v-for="organization in organizations"
          :key="organization.id"
          class="table-row data-row bg-white rounded mb-2 p-3 shadow-sm !gap-1"
        >
          <div class="col-company !mr-1">
            <div class="d-flex align-items-center">
              <div class="me-3">
                <div v-if="organization.logoUrl" class="avatar-sm">
                  <img
                    :src="organization.logoUrl"
                    :alt="organization.name"
                    class="w-100 h-100 object-fit-cover rounded"
                    @error="handleImageError"
                  />
                </div>
                <div
                  v-else
                  class="avatar-sm bg-light d-flex align-items-center justify-content-center rounded"
                >
                  <span class="text-muted fw-medium small">{{
                    getInitials(organization.name)
                  }}</span>
                </div>
              </div>
              <div>
                <h6 class="mb-0 fw-medium text-dark">
                  {{ organization.name }}
                </h6>
              </div>
            </div>
          </div>
          <div class="col-year !mr-1">
            <span class="text-muted !ml-12">{{
              organization.foundingYear || "N/A"
            }}</span>
          </div>
          <div class="col-activity !ml-12">
            <span class="text-muted">{{
              getFirstActivity(organization.primaryActivities)
            }}</span>
          </div>
          <div class="col-actions">
            <div class="d-flex gap-2 align-items-center">
              <button
                @click="openPreview(organization.id)"
                class="btn btn-sm btn-outline-secondary d-flex align-items-center justify-content-center !bg-red-100"
                title="Preview"
                style="width: 32px; height: 32px"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="red"
                  />
                  <circle
                    cx="12"
                    cy="12"
                    r="4"
                    stroke="currentColor"
                    stroke-width="2"
                    fill="red"
                  />
                </svg>
              </button>
              <button
                @click="openPreview(organization.id)"
                class="bg-red-100 !text-[#C62027] !text-xs py-2 px-3 border !border-[#C62027] !rounded"
                title="Source Book"
              >
                Source Book
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div
        class="d-flex justify-content-end mt-4"
        v-if="!loading && !error && organizations.length > 0"
      >
        <nav>
          <ul class="pagination mb-0">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <button
                class="btn btn-outline-secondary me-2"
                @click="changePage(currentPage - 1)"
                :disabled="currentPage === 1"
              >
                Previous
              </button>
            </li>
            <li v-for="page in visiblePages" :key="page" class="page-item mx-1">
              <button
                class="btn"
                :class="
                  page === currentPage ? 'btn-danger' : 'btn-outline-secondary'
                "
                @click="changePage(page)"
              >
                {{ page }}
              </button>
            </li>
            <li
              class="page-item"
              :class="{ disabled: currentPage === totalPages }"
            >
              <button
                class="btn btn-outline-secondary ms-2"
                @click="changePage(currentPage + 1)"
                :disabled="currentPage === totalPages"
              >
                Next
              </button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </BCol>

  <!-- Preview Component -->
  <Preview
    :isVisible="showPreview"
    :organizationId="selectedOrganizationId"
    @close="closePreview"
  />
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { BCol } from "bootstrap-vue-next";
import organisationService from "../Services/services/organisation";
import Preview from "./Preview.vue";

// Reactive data
const organizations = ref([]);
const loading = ref(false);
const error = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(0);
const showPreview = ref(false);
const selectedOrganizationId = ref("");

// Computed properties
const totalPages = computed(() =>
  Math.ceil(totalItems.value / itemsPerPage.value)
);
const startIndex = computed(
  () => (currentPage.value - 1) * itemsPerPage.value + 1
);
const endIndex = computed(() =>
  Math.min(currentPage.value * itemsPerPage.value, totalItems.value)
);

const visiblePages = computed(() => {
  const pages = [];
  const start = Math.max(1, currentPage.value - 2);
  const end = Math.min(totalPages.value, currentPage.value + 2);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// Methods
const fetchOrganizations = async (page = 1) => {
  loading.value = true;
  error.value = null;

  try {
    const response = await organisationService.getOrganizations({
      page: page - 1, // Backend might use 0-based pagination
      limit: itemsPerPage.value,
    });

    organizations.value = response.data.data || response.data;
    totalItems.value = response.data.total || response.data.length;
    currentPage.value = page;
  } catch (err) {
    console.error("Error fetching organizations:", err);
    error.value = "Failed to load organizations. Please try again.";
  } finally {
    loading.value = false;
  }
};

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
    fetchOrganizations(page);
  }
};

const getFirstActivity = (activities) => {
  if (!activities || !Array.isArray(activities) || activities.length === 0) {
    return "N/A";
  }

  const firstActivity = activities[0];
  return firstActivity.activity?.name || firstActivity.name || "N/A";
};

const getInitials = (name) => {
  if (!name) return "??";
  return name
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join("");
};

const handleImageError = (event) => {
  // Hide the image and show initials instead
  event.target.style.display = "none";
};

const openPreview = (organizationId) => {
  selectedOrganizationId.value = organizationId;
  showPreview.value = true;
};

const closePreview = () => {
  showPreview.value = false;
  selectedOrganizationId.value = "";
};

// Lifecycle
onMounted(() => {
  fetchOrganizations();
});
</script>

<style scoped>
.custom-table {
  width: 100%;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 0.5fr 2fr auto;
  gap: 0.5rem;
  align-items: center;
  border: 1px solid #e9ecef;
}

.header-row {
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa !important;
}

.data-row {
  transition: all 0.2s ease;
}

.data-row:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.col-company {
  display: flex;
  align-items: center;
}

.col-year {
  text-align: left;
}

.col-activity {
  text-align: left;
}

.col-actions {
  text-align: right;
  justify-self: end;
}

.avatar-sm {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-sm img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.pagination .btn {
  min-width: 40px;
  height: 38px;
  border-radius: 6px;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.rounded {
  border-radius: 0.375rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.bg-white {
  background-color: #fff !important;
}
</style>
