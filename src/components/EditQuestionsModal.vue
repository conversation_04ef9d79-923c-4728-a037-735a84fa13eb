<script setup>
import { ref, defineProps, defineEmits, onMounted } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "Questions et Réponses",
  },
  questions: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["save", "cancel"]);

// Make a local copy of questions so we don't mutate props directly
const localQuestions = ref([]);

onMounted(() => {
  // Initialize questions with data from props
  if (props.questions.length > 0) {
    localQuestions.value = JSON.parse(JSON.stringify(props.questions));
  }
});

// Remove a question from the list
function removeQuestion(index) {
  localQuestions.value.splice(index, 1);
}

// Add a new empty question
function addQuestion() {
  localQuestions.value.push({
    question: "",
    response: "",
    details: "",
  });
}

// Save changes and emit to parent
async function saveChanges() {
  try {
    await emit("save", localQuestions.value);
    // The parent component will close the modal on success
  } catch (error) {
    // If an error is thrown from the parent component, keep the modal open
    console.error("Error in save:", error);
  }
}
</script>

<template>
  <!-- Modal Overlay -->
  <div
    class="fixed inset-0 flex items-center justify-center p-12 z-50 bg-black bg-opacity-10"
  >
    <!-- Modal Card -->
    <div class="bg-white shadow-lg rounded-xl max-w-full relative p-6 w-3/4">
      <!-- Close Button (Top Right) -->
      <button
        @click="$emit('cancel')"
        class="absolute top-3 right-3 text-gray-600 hover:text-gray-800"
      >
        <!-- X icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 8.586L3.707 2.293a1 1 0 00-1.414
            1.414L8.586 10l-6.293 6.293a1 1 0
            001.414 1.414L10 11.414l6.293 6.293a1
            1 0 001.414-1.414L11.414 10l6.293-6.293a1
            1 0 00-1.414-1.414L10 8.586z"
            clip-rule="evenodd"
          />
        </svg>
      </button>

      <!-- Title -->
      <h2 class="!text-lg font-semibold mb-6">{{ title }}</h2>

      <!-- Questions List -->
      <div class="space-y-6 max-h-[550px] overflow-y-auto">
        <div
          v-for="(question, index) in localQuestions"
          :key="index"
          class="border-b border-gray-200 pb-6 mb-6"
        >
          <!-- Question Info Section -->
          <div class="flex flex-col gap-4">
            <!-- First row: Question -->
            <div class="flex items-start">
              <!-- Question -->
              <div class="flex-1 mr-4">
                <label class="block text-sm font-semibold text-gray-700 mb-1"
                  >Question</label
                >
                <input
                  v-model="question.question"
                  type="text"
                  class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                />
              </div>

              <!-- Remove Button -->
              <div class="pt-8 ml-6">
                <button
                  class="border !border-[#C62027] bg-red-50 py-3 px-3"
                  @click="removeQuestion(index)"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke="currentColor"
                    class="w-5 h-5"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Second row: Response -->
            <div class="flex">
              <!-- Response -->
              <div class="flex-1 mr-4">
                <label class="block text-sm font-semibold text-gray-700 mb-1"
                  >Réponse</label
                >
                <input
                  v-model="question.response"
                  type="text"
                  class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                />
              </div>

              <!-- Empty div to maintain alignment -->
              <div class="pt-6 w-10"></div>
            </div>

            <!-- Third row: Details -->
            <div class="flex">
              <!-- Details -->
              <div class="flex-1 mr-4">
                <label class="block text-sm font-semibold text-gray-700 mb-1"
                  >Détails (si oui)</label
                >
                <input
                  v-model="question.details"
                  type="text"
                  class="border border-gray-300 rounded w-full px-3 py-2 text-sm"
                />
              </div>

              <!-- Empty div to maintain alignment -->
              <div class="pt-6 w-10"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Add New Question Button -->
      <div class="flex justify-center mt-4">
        <button
          @click="addQuestion"
          class="border !border-[#C62027] text-[#C62027] !bg-red-50 rounded-full px-4 py-3 w-full hover:!bg-red-50 transition text-sm font-medium flex items-center justify-center space-x-2"
        >
          <svg
            width="30"
            height="30"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="inline-block"
          >
            <path
              d="M14 5.83301V22.1663"
              stroke="#C62027"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M5.83301 14H22.1663"
              stroke="#C62027"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Save Button -->
      <div class="flex justify-end mt-6">
        <button
          @click="saveChanges"
          class="bg-[#C62027] !font-bold text-white px-6 py-2 !text-xs !rounded-full shadow-md hover:bg-red-700 transition"
        >
          Sauvegarder
        </button>
      </div>
    </div>
  </div>
</template>
