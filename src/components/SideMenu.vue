<template class="h-full">
  <nav
    class="flex flex-col items-center bg-[#0E1B0F] text-white h-full py-6 w-full"
  >
    <!-- Logo Area -->
    <div class="mb-8 bg-white rounded-full py-2 px-6">
      <img
        src="/TAA_logo.png"
        alt="Logo"
        class="w-30 h-auto mx-auto object-cover"
      />
    </div>
    <!-- Menu Items -->
    <ul class="flex flex-col w-full space-y-4 mr-12 flex-grow">
      <li v-for="item in menuItems" :key="item.name" class="w-full">
        <router-link
          :to="item.path"
          class="flex items-center w-full py-2 rounded transition-colors duration-200 hover:bg-[#1C2D1A] !focus:outline-none !no-underline text-white whitespace-nowrap !text-xs"
          :class="{
            'bg-white !text-[#C62027]': isActive(item.path),
          }"
        >
          <!-- Icon (if provided) -->
          <svg
            v-if="item.icon"
            class="w-5 h-5 ml-1"
            fill="none"
            :key="item.name"
            stroke="currentColor"
            stroke-width="0.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            v-html="item.icon"
          ></svg>
          <!-- Label -->
          <span class="ml-2 !text-xs font-medium truncate flex-grow min-w-0">
            {{ item.label }}
          </span>
        </router-link>
      </li>
      <li class="mt-auto">
        <button
          @click="logout"
          class="flex items-center w-full py-2 rounded transition-colors duration-200 hover:bg-[white] hover:!text-[#C62027] !focus:outline-none !no-underline text-white whitespace-nowrap !text-xs"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-log-out-icon lucide-log-out"
          >
            <path d="m16 17 5-5-5-5" />
            <path d="M21 12H9" />
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
          </svg>
          <span class="ml-2 !text-lg font-medium truncate flex-grow min-w-0">
            Log out
          </span>
        </button>
      </li>
    </ul>
  </nav>
</template>
<script setup>
import { defineProps } from "vue";
import { useRoute, useRouter } from "vue-router";

const props = defineProps({
  menuItems: {
    type: Array,
  },
});

const route = useRoute();
const router = useRouter();

// Modified isActive to check if the current route starts with the menu item path
const isActive = (path) => {
  return route.path.startsWith(path);
};

// Logout function to remove token and redirect
const logout = () => {
  // Remove the access token from localStorage
  localStorage.removeItem("token");

  // Redirect to signin page
  router.push("/signin");
};
</script>
<style scoped>
/* Additional custom styles if needed */
svg path {
  stroke: currentColor !important;
  fill: currentColor !important;
}
</style>
