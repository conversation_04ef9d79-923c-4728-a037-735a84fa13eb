// src/router/index.ts
import {
  createRouter,
  createWebHistory,
  type NavigationGuardNext,
  type RouteLocationNormalized,
} from "vue-router";
import RegisterPage from "@/views/RegisterPage.vue";
import SignInPage from "@/views/SignInPage.vue";
import GeneralTestPage from "@/views/GeneralTestPage.vue";
import ForgotPasswordPage from "@/views/ForgotPasswordPage.vue";
import ResetPasswordPage from "@/views/ResetPasswordPage.vue";
import userDetailsService from "../Services/services/userDetails";
import productPage from "@/views/productPage.vue";
import EntrepriseInfoPage from "@/views/EntrepriseInfoPage.vue";
import EntreprisesPage from "@/views/EntreprisesPage.vue";
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/create_entreprise",
      name: "Register",
      component: RegisterPage,
    },
    {
      path: "/signin",
      name: "signin",
      component: SignInPage,
    },
    {
      path: "/entrepriseinfo/:organizationId",
      name: "entrepriseinfo",
      component: EntrepriseInfoPage,
      // Navigation guard for this route to ensure organization id is loaded
      beforeEnter: async (
        to: RouteLocationNormalized,
        from: RouteLocationNormalized,
        next: NavigationGuardNext
      ) => {
        // If the organization id is already provided, no need to fetch it again
        if (to.params.organizationId) {
          next();
        } else {
          try {
            const userDetailsResponse =
              await userDetailsService.getUserDetails();
            const orgId = userDetailsResponse.data.organization?.id;
            if (orgId) {
              next({
                name: "entrepriseinfo",
                params: { organizationId: orgId },
              });
            } else {
              // Handle the case where organization id is not available
              // For example, redirect to a page that prompts registration or an error page
              next({ name: "Register" });
            }
          } catch (error) {
            // Optionally, handle errors (like token expiration) here as well.
            next({ name: "signin" });
          }
        }
      },
      children: [
        {
          path: "",
          name: "general",
          component: () => import("@/views/GeneralTestPage.vue"), // Use lazy loading
          props: true,
        },
        {
          path: "products",
          name: "products",
          component: () => import("@/views/productPage.vue"), // Use lazy loading
          props: true,
        },
        {
          path: "hr",
          name: "hr",
          component: () => import("@/views/HrSectionPage.vue"), // Use lazy loading
          props: true,
        },
        {
          path: "revenue",
          name: "revenue",
          component: () => import("@/views/RevenuePage.vue"), // Use lazy loading
          props: true,
        },
        {
          path: "extras",
          name: "extras",
          component: () => import("@/views/ExtrasPage.vue"), // Use lazy loading
          props: true,
        },
        {
          path: "Autre",
          name: "Autre",
          component: () => import("@/views/AutrePage.vue"), // Use lazy loading
          props: true,
        },
        {
          path: "opportunity",
          name: "opportunity",
          component: () => import("@/views/OpportunityPage.vue"), // Use lazy loading
          props: true,
        },
      ],
    },
    {
      path: "/entreprises",
      name: "entreprises",
      component: EntreprisesPage,
    },
    {
      path: "/forgot_password",
      name: "forgot_password",
      component: ForgotPasswordPage,
    },
    {
      path: "/auth/create-new-password/:token",
      name: "reset_password",
      component: ResetPasswordPage,
    },
    // Catch-all route to handle undefined paths:
    {
      path: "/:pathMatch(.*)*",
      name: "NotFound",
      redirect: () => {
        const token = localStorage.getItem("token");
        if (!token) {
          return { name: "signin" };
        }
        // Redirect to a specific route that can handle missing organization id
        // For example, you might have a dedicated "loading" route or simply force re-check by navigating to the general route.
        return { name: "general" };
      },
    },
  ],
});

export default router;
