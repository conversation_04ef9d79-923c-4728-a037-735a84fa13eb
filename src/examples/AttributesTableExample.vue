<template>
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-6">Organization Attributes</h1>

    <!-- Loading State -->
    <div v-if="isLoading" class="text-center py-8">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error Message -->
    <Toast
      v-if="errorMessage"
      :message="errorMessage"
      type="error"
      :duration="5000"
      @close="errorMessage = ''"
    />

    <!-- Success Message -->
    <Toast
      v-if="successMessage"
      :message="successMessage"
      type="success"
      :duration="3000"
      @close="successMessage = ''"
    />

    <!-- Main Content -->
    <div v-else>
      <!-- ESG Indicators Section -->
      <div class="mb-6">
        <ESGTable :data="esgData" @edit="onEditESG" />
      </div>

      <!-- Partnerships Section -->
      <div class="mb-6">
        <PartnershipsTable
          :data="partnershipsData"
          @edit="onEditPartnerships"
        />
      </div>

      <!-- Technologies Section -->
      <div class="mb-6">
        <TechnologiesTable
          :data="technologiesData"
          @edit="onEditTechnologies"
        />
      </div>

      <!-- Edit Modals -->
      <EditESGModal
        v-if="showESGModal"
        title="Indicateur ESG"
        :items="editESG"
        @save="handleSaveESG"
        @cancel="showESGModal = false"
      />

      <EditPartnershipsModal
        v-if="showPartnershipsModal"
        title="Partenariats et Co-dév stratégiques"
        :items="editPartnerships"
        @save="handleSavePartnerships"
        @cancel="showPartnershipsModal = false"
      />

      <EditTechnologiesModal
        v-if="showTechnologiesModal"
        title="Technologies Clés et Digitalisation"
        :items="editTechnologies"
        @save="handleSaveTechnologies"
        @cancel="showTechnologiesModal = false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import Toast from "@/components/Toast.vue";
import ESGTable from "@/components/ESGTable.vue";
import PartnershipsTable from "@/components/PartnershipsTable.vue";
import TechnologiesTable from "@/components/TechnologiesTable.vue";
import EditESGModal from "@/components/EditESGModal.vue";
import EditPartnershipsModal from "@/components/EditPartnershipsModal.vue";
import EditTechnologiesModal from "@/components/EditTechnologiesModal.vue";
import extrasService from "@/Services/services/extras";

// State
const isLoading = ref(true);
const errorMessage = ref("");
const successMessage = ref("");
const extras = ref(null);

// Modal state for ESG
const showESGModal = ref(false);
const editESG = ref([]);

// Modal state for Partnerships
const showPartnershipsModal = ref(false);
const editPartnerships = ref([]);

// Modal state for Technologies
const showTechnologiesModal = ref(false);
const editTechnologies = ref([]);

// Get organizationId from route params
const route = useRoute();
const organizationId = route.params.organizationId;

// Computed properties for table data
const esgData = computed(() => {
  if (extras.value && Array.isArray(extras.value.esgs)) {
    return extras.value.esgs;
  }
  return [];
});

const partnershipsData = computed(() => {
  if (extras.value && Array.isArray(extras.value.partnerships)) {
    return extras.value.partnerships;
  }
  return [];
});

const technologiesData = computed(() => {
  if (extras.value && Array.isArray(extras.value.technologies)) {
    return extras.value.technologies;
  }
  return [];
});

// Fetch extras from backend
async function loadExtras() {
  if (!organizationId) return;
  isLoading.value = true;
  try {
    const response = await extrasService.getExtras(organizationId);
    extras.value = response.data;
    console.log("Extras data:", extras.value);
  } catch (err) {
    console.error("Failed to load organization extras", err);
    errorMessage.value = "Erreur lors du chargement des données";
  } finally {
    isLoading.value = false;
  }
}

// Load data on component mount
onMounted(() => {
  loadExtras();
});

// Open ESG edit modal
function onEditESG() {
  // Create a copy of the ESG data for editing
  if (extras.value && Array.isArray(extras.value.esgs)) {
    editESG.value = JSON.parse(JSON.stringify(extras.value.esgs));
  } else {
    // If no ESG data exists yet, create an empty array
    editESG.value = [];
  }
  showESGModal.value = true;
}

// Open Partnerships edit modal
function onEditPartnerships() {
  // Create a copy of the partnerships data for editing
  if (extras.value && Array.isArray(extras.value.partnerships)) {
    editPartnerships.value = JSON.parse(
      JSON.stringify(extras.value.partnerships)
    );
  } else {
    // If no partnerships data exists yet, create an empty array
    editPartnerships.value = [];
  }
  showPartnershipsModal.value = true;
}

// Open Technologies edit modal
function onEditTechnologies() {
  // Create a copy of the technologies data for editing
  if (extras.value && Array.isArray(extras.value.technologies)) {
    editTechnologies.value = JSON.parse(
      JSON.stringify(extras.value.technologies)
    );
  } else {
    // If no technologies data exists yet, create an empty array
    editTechnologies.value = [];
  }
  showTechnologiesModal.value = true;
}

// Handle save from ESG modal
async function handleSaveESG(updatedESG) {
  try {
    // Build payload for the backend
    const payload = {
      esgs: updatedESG.map((item) => ({
        name: item.name,
        value: item.value,
        type: "esg", // Must match OrganizationAttributeType.ESG
      })),
    };

    console.log("Sending ESG payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.esgs = payload.esgs;
    }

    // Show success message
    successMessage.value = "Indicateurs ESG mis à jour avec succès";

    // Close modal
    showESGModal.value = false;
  } catch (error) {
    console.error("Error updating ESG data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des indicateurs ESG";
    // Keep modal open on error
  }
}

// Handle save from Partnerships modal
async function handleSavePartnerships(updatedPartnerships) {
  try {
    // Build payload for the backend
    const payload = {
      partnerships: updatedPartnerships.map((item) => ({
        name: item.name,
        value: item.value,
        type: "partnerships", // Must match OrganizationAttributeType.Partnerships
      })),
    };

    console.log("Sending Partnerships payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.partnerships = payload.partnerships;
    }

    // Show success message
    successMessage.value = "Partenariats mis à jour avec succès";

    // Close modal
    showPartnershipsModal.value = false;
  } catch (error) {
    console.error("Error updating Partnerships data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des partenariats";
    // Keep modal open on error
  }
}

// Handle save from Technologies modal
async function handleSaveTechnologies(updatedTechnologies) {
  try {
    // Build payload for the backend
    const payload = {
      technologies: updatedTechnologies.map((item) => ({
        name: item.name,
        value: item.value,
        type: "technologies", // Must match OrganizationAttributeType.Technologies
      })),
    };

    console.log("Sending Technologies payload to backend:", payload);

    // Send update to backend
    await extrasService.updateExtras(organizationId, payload);

    // Update local data
    if (extras.value) {
      extras.value.technologies = payload.technologies;
    }

    // Show success message
    successMessage.value = "Technologies mises à jour avec succès";

    // Close modal
    showTechnologiesModal.value = false;
  } catch (error) {
    console.error("Error updating Technologies data:", error);
    errorMessage.value = "Erreur lors de la mise à jour des technologies";
    // Keep modal open on error
  }
}
</script>
