/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActivityChecklist: typeof import('./src/components/ActivityChecklist.vue')['default']
    BButton: typeof import('bootstrap-vue-next/components/BButton')['BButton']
    BCard: typeof import('bootstrap-vue-next/components/BCard')['BCard']
    BCardBody: typeof import('bootstrap-vue-next/components/BCard')['BCardBody']
    BCol: typeof import('bootstrap-vue-next/components/BContainer')['BCol']
    BProgress: typeof import('bootstrap-vue-next/components/BProgress')['BProgress']
    BRow: typeof import('bootstrap-vue-next/components/BContainer')['BRow']
    ClientTypesTable: typeof import('./src/components/ClientTypesTable.vue')['default']
    CompanyHeader: typeof import('./src/components/CompanyHeader.vue')['default']
    CountryParticipationTable: typeof import('./src/components/CountryParticipationTable.vue')['default']
    EditCertificationsModal: typeof import('./src/components/EditCertificationsModal.vue')['default']
    EditClientTypesModal: typeof import('./src/components/EditClientTypesModal.vue')['default']
    EditContractsModal: typeof import('./src/components/EditContractsModal.vue')['default']
    EditCountryParticipationModal: typeof import('./src/components/EditCountryParticipationModal.vue')['default']
    EditEnvironmentModal: typeof import('./src/components/EditEnvironmentModal.vue')['default']
    EditESGModal: typeof import('./src/components/EditESGModal.vue')['default']
    EditInvestmentsModal: typeof import('./src/components/EditInvestmentsModal.vue')['default']
    EditOpportunityModal: typeof import('./src/components/EditOpportunityModal.vue')['default']
    EditPartnershipsModal: typeof import('./src/components/EditPartnershipsModal.vue')['default']
    EditPopup: typeof import('./src/components/EditPopup.vue')['default']
    EditProductModal: typeof import('./src/components/EditProductModal.vue')['default']
    EditQuestionsModal: typeof import('./src/components/EditQuestionsModal.vue')['default']
    EditRequest: typeof import('./src/components/EditRequest.vue')['default']
    EditResearchDevelopmentPopup: typeof import('./src/components/EditResearchDevelopmentPopup.vue')['default']
    EditSitesModal: typeof import('./src/components/EditSitesModal.vue')['default']
    EditTechnologiesModal: typeof import('./src/components/EditTechnologiesModal.vue')['default']
    EntreprisesList: typeof import('./src/components/EntreprisesList.vue')['default']
    EnvironmentCard: typeof import('./src/components/EnvironmentCard.vue')['default']
    ESGTable: typeof import('./src/components/ESGTable.vue')['default']
    ExampleUsage: typeof import('./src/components/ExampleUsage.vue')['default']
    ExtrasTable: typeof import('./src/components/ExtrasTable.vue')['default']
    ForgotPassword: typeof import('./src/components/ForgotPassword.vue')['default']
    HrTable: typeof import('./src/components/HrTable.vue')['default']
    InvestmentTable: typeof import('./src/components/InvestmentTable.vue')['default']
    OpportunityTable: typeof import('./src/components/OpportunityTable.vue')['default']
    PartnershipsTable: typeof import('./src/components/PartnershipsTable.vue')['default']
    Preview: typeof import('./src/components/Preview.vue')['default']
    PreviewCertificationsSection: typeof import('./src/components/PreviewCertificationsSection.vue')['default']
    PreviewClientsSection: typeof import('./src/components/PreviewClientsSection.vue')['default']
    PreviewExternalViewsSection: typeof import('./src/components/PreviewExternalViewsSection.vue')['default']
    PreviewProductionSection: typeof import('./src/components/PreviewProductionSection.vue')['default']
    PreviewProductsSection: typeof import('./src/components/PreviewProductsSection.vue')['default']
    PreviewSection: typeof import('./src/components/PreviewSection.vue')['default']
    ProductTable: typeof import('./src/components/ProductTable.vue')['default']
    ProjectsInitiativesCard: typeof import('./src/components/ProjectsInitiativesCard.vue')['default']
    QuestionsTable: typeof import('./src/components/QuestionsTable.vue')['default']
    ReclamationPopup: typeof import('./src/components/ReclamationPopup.vue')['default']
    RegisterForm: typeof import('./src/components/RegisterForm.vue')['default']
    ResearchDevelopmentCard: typeof import('./src/components/ResearchDevelopmentCard.vue')['default']
    ResetPassword: typeof import('./src/components/ResetPassword.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SectionCard: typeof import('./src/components/SectionCard.vue')['default']
    SideMenu: typeof import('./src/components/SideMenu.vue')['default']
    SignIn: typeof import('./src/components/SignIn.vue')['default']
    SitesCard: typeof import('./src/components/SitesCard.vue')['default']
    TechnologiesTable: typeof import('./src/components/TechnologiesTable.vue')['default']
    Toast: typeof import('./src/components/Toast.vue')['default']
    WasteDistributionCard: typeof import('./src/components/WasteDistributionCard.vue')['default']
  }
}
