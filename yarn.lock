# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8c0

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: 81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@angular-devkit/core@npm:13.3.5":
  version: 13.3.5
  resolution: "@angular-devkit/core@npm:13.3.5"
  dependencies:
    ajv: 8.9.0
    ajv-formats: 2.1.1
    fast-json-stable-stringify: 2.1.0
    magic-string: 0.25.7
    rxjs: 6.6.7
    source-map: 0.7.3
  peerDependencies:
    chokidar: ^3.5.2
  peerDependenciesMeta:
    chokidar:
      optional: true
  checksum: 3e3bea124150ed4e22070bbc63b0f6e67cef4781f8b3620ab716d3dd44a7a63df892f41c6d5d8e845360d13feb17747b215b57a8a822246b9c9a5ea5691b347c
  languageName: node
  linkType: hard

"@angular-devkit/core@npm:13.3.6":
  version: 13.3.6
  resolution: "@angular-devkit/core@npm:13.3.6"
  dependencies:
    ajv: 8.9.0
    ajv-formats: 2.1.1
    fast-json-stable-stringify: 2.1.0
    magic-string: 0.25.7
    rxjs: 6.6.7
    source-map: 0.7.3
  peerDependencies:
    chokidar: ^3.5.2
  peerDependenciesMeta:
    chokidar:
      optional: true
  checksum: 263e12c255eb6603bade92b402591bdd2af44810495e07d607094db3ee1bb937cf45b4459c477f2edacebb8da8ea132f19b13dc4828b0158cd3bd34c67585de7
  languageName: node
  linkType: hard

"@angular-devkit/core@npm:15.1.4":
  version: 15.1.4
  resolution: "@angular-devkit/core@npm:15.1.4"
  dependencies:
    ajv: 8.12.0
    ajv-formats: 2.1.1
    jsonc-parser: 3.2.0
    rxjs: 6.6.7
    source-map: 0.7.4
  peerDependencies:
    chokidar: ^3.5.2
  peerDependenciesMeta:
    chokidar:
      optional: true
  checksum: 69fb31e81747089480388c284b9c4362d99d1aea68789fa4129fd7bd7573ddc0ba2e7a376bd836ccb502c4cb2f77c80e6868acbce653e3a7a5b41621bc4f2c43
  languageName: node
  linkType: hard

"@angular-devkit/core@npm:16.0.1":
  version: 16.0.1
  resolution: "@angular-devkit/core@npm:16.0.1"
  dependencies:
    ajv: 8.12.0
    ajv-formats: 2.1.1
    jsonc-parser: 3.2.0
    rxjs: 7.8.1
    source-map: 0.7.4
  peerDependencies:
    chokidar: ^3.5.2
  peerDependenciesMeta:
    chokidar:
      optional: true
  checksum: 061e9248f43dbbe11a74ccebe01152a93f1ebd43463f503d92bdb1bab8369a3568147b0b92ef4f285b8782f07532db62b83b639322479301e28fa606859fbccc
  languageName: node
  linkType: hard

"@angular-devkit/schematics-cli@npm:13.3.6":
  version: 13.3.6
  resolution: "@angular-devkit/schematics-cli@npm:13.3.6"
  dependencies:
    "@angular-devkit/core": 13.3.6
    "@angular-devkit/schematics": 13.3.6
    ansi-colors: 4.1.1
    inquirer: 8.2.0
    minimist: 1.2.6
    symbol-observable: 4.0.0
  bin:
    schematics: bin/schematics.js
  checksum: 7dc7a0b49ac680a33f26d257017a2ce9ee3d3844ee4c0d0bb47a45e0a9e22991b39271abcf77f18e00e7d4ca82dc5911996b1945d3c6ec4674fa33ed7f5c5afc
  languageName: node
  linkType: hard

"@angular-devkit/schematics-cli@npm:15.1.4":
  version: 15.1.4
  resolution: "@angular-devkit/schematics-cli@npm:15.1.4"
  dependencies:
    "@angular-devkit/core": 15.1.4
    "@angular-devkit/schematics": 15.1.4
    ansi-colors: 4.1.3
    inquirer: 8.2.4
    symbol-observable: 4.0.0
    yargs-parser: 21.1.1
  bin:
    schematics: bin/schematics.js
  checksum: c86af3680d097e4c9eeef6dc87e538c8049568a2981e83ad57dc1660406eb778fe38eb9b35956e4eb7432e89f20f20e72da7232fac203c32411331e7aafe8ecb
  languageName: node
  linkType: hard

"@angular-devkit/schematics@npm:13.3.5":
  version: 13.3.5
  resolution: "@angular-devkit/schematics@npm:13.3.5"
  dependencies:
    "@angular-devkit/core": 13.3.5
    jsonc-parser: 3.0.0
    magic-string: 0.25.7
    ora: 5.4.1
    rxjs: 6.6.7
  checksum: a9d1d8f8b97f792fff0abe6b14d5a43fe0803bc7913a52d8606d3fbc16c4d79e98d6ea5f7e720c67d63be77038907560c5efb72e2edcad9280743b38e59b3461
  languageName: node
  linkType: hard

"@angular-devkit/schematics@npm:13.3.6":
  version: 13.3.6
  resolution: "@angular-devkit/schematics@npm:13.3.6"
  dependencies:
    "@angular-devkit/core": 13.3.6
    jsonc-parser: 3.0.0
    magic-string: 0.25.7
    ora: 5.4.1
    rxjs: 6.6.7
  checksum: 8d64e1aa4b9369928a57699769a21765c48e1dbca73411bb6a77f4a0b293fc051e0967d8295f4f3fba62c3c18babf8104ae1f776e61c34f4ee1037b172e022a3
  languageName: node
  linkType: hard

"@angular-devkit/schematics@npm:15.1.4":
  version: 15.1.4
  resolution: "@angular-devkit/schematics@npm:15.1.4"
  dependencies:
    "@angular-devkit/core": 15.1.4
    jsonc-parser: 3.2.0
    magic-string: 0.27.0
    ora: 5.4.1
    rxjs: 6.6.7
  checksum: c393b458e4afdd7b5e536a8c7e1f66bbda9e26dcfdeacbf10acfa2bf5e7ace0b86a778bbf697237d8cbf84268110256591dde22275a13eda2a57619b6c5174a0
  languageName: node
  linkType: hard

"@angular-devkit/schematics@npm:16.0.1":
  version: 16.0.1
  resolution: "@angular-devkit/schematics@npm:16.0.1"
  dependencies:
    "@angular-devkit/core": 16.0.1
    jsonc-parser: 3.2.0
    magic-string: 0.30.0
    ora: 5.4.1
    rxjs: 7.8.1
  checksum: 26f6ef07ced08273ad9384867c92d4b295a013115355597306e84da1d3445dc41f90f9046dbe7024b7ed568f20dd4e28c070b59f10f0dea8e44cbe9f31825408
  languageName: node
  linkType: hard

"@aws-crypto/crc32@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: eab9581d3363af5ea498ae0e72de792f54d8890360e14a9d8261b7b5c55ebe080279fb2556e07994d785341cdaa99ab0b1ccf137832b53b5904cd6928f2b094b
  languageName: node
  linkType: hard

"@aws-crypto/crc32c@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32c@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: 223efac396cdebaf5645568fa9a38cd0c322c960ae1f4276bedfe2e1031d0112e49d7d39225d386354680ecefae29f39af469a84b2ddfa77cb6692036188af77
  languageName: node
  linkType: hard

"@aws-crypto/sha1-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha1-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/supports-web-crypto": ^5.2.0
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    "@aws-sdk/util-locate-window": ^3.0.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: 51fed0bf078c10322d910af179871b7d299dde5b5897873ffbeeb036f427e5d11d23db9794439226544b73901920fd19f4d86bbc103ed73cc0cfdea47a83c6ac
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": ^5.2.0
    "@aws-crypto/supports-web-crypto": ^5.2.0
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    "@aws-sdk/util-locate-window": ^3.0.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: 05f6d256794df800fe9aef5f52f2ac7415f7f3117d461f85a6aecaa4e29e91527b6fd503681a17136fa89e9dd3d916e9c7e4cfb5eba222875cb6c077bdc1d00d
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: 6c48701f8336341bb104dfde3d0050c89c288051f6b5e9bdfeb8091cf3ffc86efcd5c9e6ff2a4a134406b019c07aca9db608128f8d9267c952578a3108db9fd1
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 4d2118e29d68ca3f5947f1e37ce1fbb3239a0c569cc938cdc8ab8390d595609b5caf51a07c9e0535105b17bf5c52ea256fed705a07e9681118120ab64ee73af2
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:5.2.0, @aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": ^3.222.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: 0362d4c197b1fd64b423966945130207d1fe23e1bb2878a18e361f7743c8d339dad3f8729895a29aa34fff6a86c65f281cf5167c4bf253f21627ae80b6dd2951
  languageName: node
  linkType: hard

"@aws-sdk/client-s3@npm:^3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/client-s3@npm:3.804.0"
  dependencies:
    "@aws-crypto/sha1-browser": 5.2.0
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/credential-provider-node": 3.804.0
    "@aws-sdk/middleware-bucket-endpoint": 3.804.0
    "@aws-sdk/middleware-expect-continue": 3.804.0
    "@aws-sdk/middleware-flexible-checksums": 3.804.0
    "@aws-sdk/middleware-host-header": 3.804.0
    "@aws-sdk/middleware-location-constraint": 3.804.0
    "@aws-sdk/middleware-logger": 3.804.0
    "@aws-sdk/middleware-recursion-detection": 3.804.0
    "@aws-sdk/middleware-sdk-s3": 3.804.0
    "@aws-sdk/middleware-ssec": 3.804.0
    "@aws-sdk/middleware-user-agent": 3.804.0
    "@aws-sdk/region-config-resolver": 3.804.0
    "@aws-sdk/signature-v4-multi-region": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@aws-sdk/util-endpoints": 3.804.0
    "@aws-sdk/util-user-agent-browser": 3.804.0
    "@aws-sdk/util-user-agent-node": 3.804.0
    "@aws-sdk/xml-builder": 3.804.0
    "@smithy/config-resolver": ^4.1.0
    "@smithy/core": ^3.3.1
    "@smithy/eventstream-serde-browser": ^4.0.2
    "@smithy/eventstream-serde-config-resolver": ^4.1.0
    "@smithy/eventstream-serde-node": ^4.0.2
    "@smithy/fetch-http-handler": ^5.0.2
    "@smithy/hash-blob-browser": ^4.0.2
    "@smithy/hash-node": ^4.0.2
    "@smithy/hash-stream-node": ^4.0.2
    "@smithy/invalid-dependency": ^4.0.2
    "@smithy/md5-js": ^4.0.2
    "@smithy/middleware-content-length": ^4.0.2
    "@smithy/middleware-endpoint": ^4.1.2
    "@smithy/middleware-retry": ^4.1.3
    "@smithy/middleware-serde": ^4.0.3
    "@smithy/middleware-stack": ^4.0.2
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/node-http-handler": ^4.0.4
    "@smithy/protocol-http": ^5.1.0
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/url-parser": ^4.0.2
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.10
    "@smithy/util-defaults-mode-node": ^4.0.10
    "@smithy/util-endpoints": ^3.0.2
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-retry": ^4.0.3
    "@smithy/util-stream": ^4.2.0
    "@smithy/util-utf8": ^4.0.0
    "@smithy/util-waiter": ^4.0.3
    tslib: ^2.6.2
  checksum: a2544aa511a5592b050175768ce7abb17a53dbaafb92b218888f86878c978d28fd35be46a3469ed7bb291ceb6d2af6ede0b6aa65a27596a3e8b8b45e590619d5
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/client-sso@npm:3.804.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/middleware-host-header": 3.804.0
    "@aws-sdk/middleware-logger": 3.804.0
    "@aws-sdk/middleware-recursion-detection": 3.804.0
    "@aws-sdk/middleware-user-agent": 3.804.0
    "@aws-sdk/region-config-resolver": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@aws-sdk/util-endpoints": 3.804.0
    "@aws-sdk/util-user-agent-browser": 3.804.0
    "@aws-sdk/util-user-agent-node": 3.804.0
    "@smithy/config-resolver": ^4.1.0
    "@smithy/core": ^3.3.1
    "@smithy/fetch-http-handler": ^5.0.2
    "@smithy/hash-node": ^4.0.2
    "@smithy/invalid-dependency": ^4.0.2
    "@smithy/middleware-content-length": ^4.0.2
    "@smithy/middleware-endpoint": ^4.1.2
    "@smithy/middleware-retry": ^4.1.3
    "@smithy/middleware-serde": ^4.0.3
    "@smithy/middleware-stack": ^4.0.2
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/node-http-handler": ^4.0.4
    "@smithy/protocol-http": ^5.1.0
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/url-parser": ^4.0.2
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.10
    "@smithy/util-defaults-mode-node": ^4.0.10
    "@smithy/util-endpoints": ^3.0.2
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-retry": ^4.0.3
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: bdca6467b4d8cda794a5e2353c4cefe28630b07b7bf8dd2441275d77195f49765f17cb8154110c562ecb125a890ba6bc129e7a080683fd87205b17e56ad39331
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/core@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/core": ^3.3.1
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/property-provider": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/signature-v4": ^5.1.0
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/util-middleware": ^4.0.2
    fast-xml-parser: 4.4.1
    tslib: ^2.6.2
  checksum: c20d9cd253bbe214e92f33260f7c52766cfdd21ebf4ee198bbba0e6a1d24ee8dc9e1ed024abf3f2772919d9bfd391f236839e4da07b4330690a0c6a874b0f992
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/property-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 5a8f5ed24c29c1cd0d87f552975b16bb413ac9c3f7c608effb169c6b5b75727eb6200e3eaecb9560783bbb8899655d6ecd2bb5290304945c2b5fb97d35ac19e5
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/fetch-http-handler": ^5.0.2
    "@smithy/node-http-handler": ^4.0.4
    "@smithy/property-provider": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/util-stream": ^4.2.0
    tslib: ^2.6.2
  checksum: 7c290ba3fb5245010f193cd5d6f2c6ed41555c107911ac1a6dc9136ba6f21857a0101cd3f7b226a748a1b407b5e6b90503badde3ff0d6ab7e9900bd3b414786c
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/credential-provider-env": 3.804.0
    "@aws-sdk/credential-provider-http": 3.804.0
    "@aws-sdk/credential-provider-process": 3.804.0
    "@aws-sdk/credential-provider-sso": 3.804.0
    "@aws-sdk/credential-provider-web-identity": 3.804.0
    "@aws-sdk/nested-clients": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/credential-provider-imds": ^4.0.2
    "@smithy/property-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: f1e78aef499fcbb4da35385ecc7af52bcd0833446ad1cd041d7912a85096a516e9f96eb3ea984d7559aa6ebb3ae6b1406dc328b5e87d22fbb12ff94d33328695
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.804.0"
  dependencies:
    "@aws-sdk/credential-provider-env": 3.804.0
    "@aws-sdk/credential-provider-http": 3.804.0
    "@aws-sdk/credential-provider-ini": 3.804.0
    "@aws-sdk/credential-provider-process": 3.804.0
    "@aws-sdk/credential-provider-sso": 3.804.0
    "@aws-sdk/credential-provider-web-identity": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/credential-provider-imds": ^4.0.2
    "@smithy/property-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 3146becead8deddc7bd0a147a1b6a98b3fed02bb4b833d664c0b950ac7fb7b41c8f3c99b0969eff38d303c527009217f7309a98e3ff1a24b5e618ac1a4aaa8dc
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/property-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: b1a885940b21bda0c373f3792bb2280b5689032acd81b028dbac66d50c56efe31033c14c074bfcf07f207a8b2cb6f5095972094df8b9618f1e16522e1facf567
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.804.0"
  dependencies:
    "@aws-sdk/client-sso": 3.804.0
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/token-providers": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/property-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 61bb9e97988f3c42ad832788231ea77fb6cf631e2906e4740c20549edf369f2b67e470f430b16c63e99749bafff7220947ec1a1fc0c68f7f3c7adcbbe407d429
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/nested-clients": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/property-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 9b4193cd459836aa386c8ef65bcb125774379660b05a1fd42eb9b953976d29bd5c2857720aa595f1c3de9b17b4a9ace8aab07334bd1f637cc454b3b982eeafa7
  languageName: node
  linkType: hard

"@aws-sdk/middleware-bucket-endpoint@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-bucket-endpoint@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@aws-sdk/util-arn-parser": 3.804.0
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    "@smithy/util-config-provider": ^4.0.0
    tslib: ^2.6.2
  checksum: e585d01797cd67f769cb0ee43a777bfdd50fd2e8d1fa6c7074d14f3d4f4864c2fe533aefed1bf34fd3c11cdfdee3b4877c9835514ab4c272fcdf789fb9cd2070
  languageName: node
  linkType: hard

"@aws-sdk/middleware-expect-continue@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-expect-continue@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: f3ffbf1f8ad43db7ed16398ed991e99ccada76f674fc0cc8579d1b43bb680fac93ddefd2cd59107c4a2b396bd64e43bab6accc3e6f1c970421f855427c317726
  languageName: node
  linkType: hard

"@aws-sdk/middleware-flexible-checksums@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-flexible-checksums@npm:3.804.0"
  dependencies:
    "@aws-crypto/crc32": 5.2.0
    "@aws-crypto/crc32c": 5.2.0
    "@aws-crypto/util": 5.2.0
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/is-array-buffer": ^4.0.0
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-stream": ^4.2.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: b491c7b7536ec0c13f0edf07454faff52edf2d807f7d9006d6ea61c6b6c07a2be83b27392c6412a118af9b8786e450dac9d0f1cff705306a878f73767f32cf33
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 4089d24b70d2d80f4936e4468f69d3ce1dfd80bc84ed0db65c57b25814c7842076e6ab55a80346f2b396b1db967c447370839a2dac836e1db81b01928985ceeb
  languageName: node
  linkType: hard

"@aws-sdk/middleware-location-constraint@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-location-constraint@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 2ec5767721fd153de6c4ec8ed9c127279839df7f8d3f6b577caee717240bb2c951ade643d7842ee2f0daba0a1d8d51485fd4118bee3824527673d194d2c725e4
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-logger@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 4b7deebb336231e529857673fbba898e578b7ca88049923f3e822e67732262a08bbcaecf388e1cd687102744ad9867ab535b71e8b086602f91e1a4bb43b39a5a
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 86e6d2902dd8ec8551d9f66ebc470b21bcf0d3caab457ef3395ee18fcd371d3816e86f17f39cd69a27cc39ee96b6b85d0b179e44fec5a6da11f44a4905d8ac92
  languageName: node
  linkType: hard

"@aws-sdk/middleware-sdk-s3@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-sdk-s3@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@aws-sdk/util-arn-parser": 3.804.0
    "@smithy/core": ^3.3.1
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/signature-v4": ^5.1.0
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-stream": ^4.2.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 185d1335128f637dd5abe685ea90d4af0339d6cf43d9e18fbccdd5416ba25aafaef5cb11a782ed3d8dab850d9b61d218439d2cd3f3073fef7e258a86ff49459c
  languageName: node
  linkType: hard

"@aws-sdk/middleware-ssec@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-ssec@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: f93b366b8dc42a2bcaa5b049ccc5f7a7f77d5c1d1da50edad1ef74bfb582859ff84b0b46800e1801689a81c772017dea0b7f3bb7707ad3b400736bf67e7f4907
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.804.0"
  dependencies:
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@aws-sdk/util-endpoints": 3.804.0
    "@smithy/core": ^3.3.1
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 5e125dbb220a4ccef454cca673226f3af5306d79016740b71b41c83da9b6ea382301b88f741c500fe3cd72b79ecd22cce591eeb9c2d231773ab52895a84cf183
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/nested-clients@npm:3.804.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.804.0
    "@aws-sdk/middleware-host-header": 3.804.0
    "@aws-sdk/middleware-logger": 3.804.0
    "@aws-sdk/middleware-recursion-detection": 3.804.0
    "@aws-sdk/middleware-user-agent": 3.804.0
    "@aws-sdk/region-config-resolver": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@aws-sdk/util-endpoints": 3.804.0
    "@aws-sdk/util-user-agent-browser": 3.804.0
    "@aws-sdk/util-user-agent-node": 3.804.0
    "@smithy/config-resolver": ^4.1.0
    "@smithy/core": ^3.3.1
    "@smithy/fetch-http-handler": ^5.0.2
    "@smithy/hash-node": ^4.0.2
    "@smithy/invalid-dependency": ^4.0.2
    "@smithy/middleware-content-length": ^4.0.2
    "@smithy/middleware-endpoint": ^4.1.2
    "@smithy/middleware-retry": ^4.1.3
    "@smithy/middleware-serde": ^4.0.3
    "@smithy/middleware-stack": ^4.0.2
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/node-http-handler": ^4.0.4
    "@smithy/protocol-http": ^5.1.0
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/url-parser": ^4.0.2
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.10
    "@smithy/util-defaults-mode-node": ^4.0.10
    "@smithy/util-endpoints": ^3.0.2
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-retry": ^4.0.3
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 38517496490d138ad150aedeb8ce0ab4ab660bb6d3ec5d82fb29c08afd495296cba3cd9375e5f5f0f9be4bfecd4f03beef5e0b925f2fb0da7acba3aaa583f6db
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.2
    tslib: ^2.6.2
  checksum: f4c5b9c3cb75e2c928f71b6d060ed292d408bf6401ae86a29342fe34a5af3eda0e2680848134f909d41e1c0cd1c0c63656c06090f29494fd9699bb11cafd240b
  languageName: node
  linkType: hard

"@aws-sdk/signature-v4-multi-region@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/signature-v4-multi-region@npm:3.804.0"
  dependencies:
    "@aws-sdk/middleware-sdk-s3": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/protocol-http": ^5.1.0
    "@smithy/signature-v4": ^5.1.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 94a4c52d1f29767d7ea1928881faf8af6f8f1b9c5d418a367cc4e0bb0ca9e5c2bd9e03d34b7cdd23b073545184d46a8f12451fb4c0e6dc02d9b3fb90bf19ad8e
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/token-providers@npm:3.804.0"
  dependencies:
    "@aws-sdk/nested-clients": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/property-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: ea2c56708b0ea312f14bbaedad1cc0ce8247d9f7a6a881173240479d0a0a22dd566afb9a8fa7dff504813a455f6aab1cd0309b43548a84e6cad9b6fb46507795
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.804.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.804.0
  resolution: "@aws-sdk/types@npm:3.804.0"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: cdda6d77466ed34de1ca0e23b9df5c576e7d67dc87cfda2a2d024a9c5f4180fe77ebaf57194a4cf034ee5edfbcd8efdeca458e9b61b1f364b261284b4a141ae5
  languageName: node
  linkType: hard

"@aws-sdk/util-arn-parser@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-arn-parser@npm:3.804.0"
  dependencies:
    tslib: ^2.6.2
  checksum: b6d4c883ec2949fa40552fe8573c9c32af07c92c1bd94a27d978aa14d37b005be95392069d6b882ba977484f4dd0371792296fb2516f5d7601be5102888ee9ee
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-endpoints@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/types": ^4.2.0
    "@smithy/util-endpoints": ^3.0.2
    tslib: ^2.6.2
  checksum: 09bd3a9a114b0ebc897275ef75df72b3652af3023405484b86484861455b14f6c51809086598c9395b6381f85abe8f3d11a02b98806b55004c450b9937fcc4b2
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-locate-window@npm:3.804.0"
  dependencies:
    tslib: ^2.6.2
  checksum: a0ceaf6531f188751fea7e829b730650689fa2196e0b3f870dde3888bcb840fe0852e10488699d4d9683db0765cd7f7060ca8ac216348991996b6d794f9957ab
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.804.0"
  dependencies:
    "@aws-sdk/types": 3.804.0
    "@smithy/types": ^4.2.0
    bowser: ^2.11.0
    tslib: ^2.6.2
  checksum: d459a94955e7298c72dc97e7e59d276320ff004615b117bd2f63bbacf762159c5476f40a8f24d9b5eb054915bd6ce97ffade3a649d5fad98643f92a9d90a5192
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.804.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": 3.804.0
    "@aws-sdk/types": 3.804.0
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: f039f6420b7fe01674f63d5a48733e471ddcd03871d86cbd14d51af2a38da1473ac38f3cd192f0b82ce2bae66c69dd86074e2cf212cb68a183f17430cdf7dc63
  languageName: node
  linkType: hard

"@aws-sdk/xml-builder@npm:3.804.0":
  version: 3.804.0
  resolution: "@aws-sdk/xml-builder@npm:3.804.0"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: c5985eb24e02eccc4d21f38e3b1912f4808a9406138f9f9ba6715d5004e1910496d2d8dd929bfb3222ac2209f55b3a630fe817abc32e8c4c7e715d1a54712fae
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.16.7, @babel/code-frame@npm:^7.26.2":
  version: 7.26.2
  resolution: "@babel/code-frame@npm:7.26.2"
  dependencies:
    "@babel/helper-validator-identifier": ^7.25.9
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: 7d79621a6849183c415486af99b1a20b84737e8c11cd55b6544f688c51ce1fd710e6d869c3dd21232023da272a79b91efb3e83b5bc2dc65c1187c5fcd1b72ea8
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.26.5":
  version: 7.26.8
  resolution: "@babel/compat-data@npm:7.26.8"
  checksum: 66408a0388c3457fff1c2f6c3a061278dd7b3d2f0455ea29bb7b187fa52c60ae8b4054b3c0a184e21e45f0eaac63cf390737bc7504d1f4a088a6e7f652c068ca
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3":
  version: 7.26.9
  resolution: "@babel/core@npm:7.26.9"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.26.2
    "@babel/generator": ^7.26.9
    "@babel/helper-compilation-targets": ^7.26.5
    "@babel/helper-module-transforms": ^7.26.0
    "@babel/helpers": ^7.26.9
    "@babel/parser": ^7.26.9
    "@babel/template": ^7.26.9
    "@babel/traverse": ^7.26.9
    "@babel/types": ^7.26.9
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: ed7212ff42a9453765787019b7d191b167afcacd4bd8fec10b055344ef53fa0cc648c9a80159ae4ecf870016a6318731e087042dcb68d1a2a9d34eb290dc014b
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.9, @babel/generator@npm:^7.7.2":
  version: 7.26.9
  resolution: "@babel/generator@npm:7.26.9"
  dependencies:
    "@babel/parser": ^7.26.9
    "@babel/types": ^7.26.9
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^3.0.2
  checksum: 6b78872128205224a9a9761b9ea7543a9a7902a04b82fc2f6801ead4de8f59056bab3fd17b1f834ca7b049555fc4c79234b9a6230dd9531a06525306050becad
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.26.5":
  version: 7.26.5
  resolution: "@babel/helper-compilation-targets@npm:7.26.5"
  dependencies:
    "@babel/compat-data": ^7.26.5
    "@babel/helper-validator-option": ^7.25.9
    browserslist: ^4.24.0
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: 9da5c77e5722f1a2fcb3e893049a01d414124522bbf51323bb1a0c9dcd326f15279836450fc36f83c9e8a846f3c40e88be032ed939c5a9840922bed6073edfb4
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-module-imports@npm:7.25.9"
  dependencies:
    "@babel/traverse": ^7.25.9
    "@babel/types": ^7.25.9
  checksum: 078d3c2b45d1f97ffe6bb47f61961be4785d2342a4156d8b42c92ee4e1b7b9e365655dd6cb25329e8fe1a675c91eeac7e3d04f0c518b67e417e29d6e27b6aa70
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.26.0":
  version: 7.26.0
  resolution: "@babel/helper-module-transforms@npm:7.26.0"
  dependencies:
    "@babel/helper-module-imports": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
    "@babel/traverse": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: ee111b68a5933481d76633dad9cdab30c41df4479f0e5e1cc4756dc9447c1afd2c9473b5ba006362e35b17f4ebddd5fca090233bef8dfc84dca9d9127e56ec3a
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.25.9, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.26.5
  resolution: "@babel/helper-plugin-utils@npm:7.26.5"
  checksum: cdaba71d4b891aa6a8dfbe5bac2f94effb13e5fa4c2c487667fdbaa04eae059b78b28d85a885071f45f7205aeb56d16759e1bed9c118b94b16e4720ef1ab0f65
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-string-parser@npm:7.25.9"
  checksum: 7244b45d8e65f6b4338a6a68a8556f2cb161b782343e97281a5f2b9b93e420cad0d9f5773a59d79f61d0c448913d06f6a2358a87f2e203cf112e3c5b53522ee6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-identifier@npm:7.25.9"
  checksum: 4fc6f830177b7b7e887ad3277ddb3b91d81e6c4a24151540d9d1023e8dc6b1c0505f0f0628ae653601eb4388a8db45c1c14b2c07a9173837aef7e4116456259d
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.25.9":
  version: 7.25.9
  resolution: "@babel/helper-validator-option@npm:7.25.9"
  checksum: 27fb195d14c7dcb07f14e58fe77c44eea19a6a40a74472ec05c441478fa0bb49fa1c32b2d64be7a38870ee48ef6601bdebe98d512f0253aea0b39756c4014f3e
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.26.9":
  version: 7.26.9
  resolution: "@babel/helpers@npm:7.26.9"
  dependencies:
    "@babel/template": ^7.26.9
    "@babel/types": ^7.26.9
  checksum: 3d4dbc4a33fe4181ed810cac52318b578294745ceaec07e2f6ecccf6cda55d25e4bfcea8f085f333bf911c9e1fc13320248dd1d5315ab47ad82ce1077410df05
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.26.9":
  version: 7.26.9
  resolution: "@babel/parser@npm:7.26.9"
  dependencies:
    "@babel/types": ^7.26.9
  bin:
    parser: ./bin/babel-parser.js
  checksum: 4b9ef3c9a0d4c328e5e5544f50fe8932c36f8a2c851e7f14a85401487cd3da75cad72c2e1bcec1eac55599a6bbb2fdc091f274c4fcafa6bdd112d4915ff087fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 686891b81af2bc74c39013655da368a480f17dd237bf9fbc32048e5865cb706d5a8f65438030da535b332b1d6b22feba336da8fa931f663b6b34e13147d12dde
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 95168fa186416195280b1264fb18afcdcdcea780b3515537b766cb90de6ce042d42dd6a204a39002f794ae5845b02afb0fd4861a3308a861204a55e68310a120
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4464bf9115f4a2d02ce1454411baf9cfb665af1da53709c5c56953e5e2913745b0fcce82982a00463d6facbdd93445c691024e310b91431a1e2f024b158f6371
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7":
  version: 7.26.0
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.26.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e594c185b12bfe0bbe7ca78dfeebe870e6d569a12128cac86f3164a075fe0ff70e25ddbd97fd0782906b91f65560c9dc6957716b7b4a68aba2516c9b7455e352
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0b08b5e4c3128523d8e346f8cfc86824f0da2697b1be12d71af50a31aff7a56ceb873ed28779121051475010c28d6146a6bfea8518b150b71eeb4e46190172ee
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e98f31b2ec406c57757d115aac81d0336e8434101c224edd9a5c93cefa53faf63eacc69f3138960c8b25401315af03df37f68d316c151c4b933136716ed6906e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2594cfbe29411ad5bc2ad4058de7b2f6a8c5b86eda525a993959438615479e59c012c14aec979e538d60a584a1a799b60d1b8942c3b18468cb9d99b8fd34cd0b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2024fbb1162899094cfc81152449b12bd0cc7053c6d4bda8ac2852545c87d0a851b1b72ed9560673cbf3ef6248257262c3c04aabf73117215c1b9cc7dd2542ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c55a82b3113480942c6aa2fcbe976ff9caa74b7b1109ff4369641dfbc88d1da348aceb3c31b6ed311c84d1e7c479440b961906c735d0ab494f688bf2fd5b9bb9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46edddf2faa6ebf94147b8e8540dfc60a5ab718e2de4d01b2c0bdf250a4d642c2bd47cbcbb739febcb2bf75514dbcefad3c52208787994b8d0f8822490f55e81
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 69822772561706c87f0a65bc92d0772cea74d6bc0911537904a676d5ff496a6d3ac4e05a166d8125fce4a16605bace141afc3611074e170a994e66e5397787f3
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 14bf6e65d5bc1231ffa9def5f0ef30b19b51c218fcecaa78cd1bdf7939dfdf23f90336080b7f5196916368e399934ce5d581492d8292b46a2fb569d8b2da106f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.25.9
  resolution: "@babel/plugin-syntax-typescript@npm:7.25.9"
  dependencies:
    "@babel/helper-plugin-utils": ^7.25.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5192ebe11bd46aea68b7a60fd9555465c59af7e279e71126788e59121b86e00b505816685ab4782abe159232b0f73854e804b54449820b0d950b397ee158caa2
  languageName: node
  linkType: hard

"@babel/template@npm:^7.26.9, @babel/template@npm:^7.3.3":
  version: 7.26.9
  resolution: "@babel/template@npm:7.26.9"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/parser": ^7.26.9
    "@babel/types": ^7.26.9
  checksum: 019b1c4129cc01ad63e17529089c2c559c74709d225f595eee017af227fee11ae8a97a6ab19ae6768b8aa22d8d75dcb60a00b28f52e9fa78140672d928bc1ae9
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.25.9, @babel/traverse@npm:^7.26.9, @babel/traverse@npm:^7.7.2":
  version: 7.26.9
  resolution: "@babel/traverse@npm:7.26.9"
  dependencies:
    "@babel/code-frame": ^7.26.2
    "@babel/generator": ^7.26.9
    "@babel/parser": ^7.26.9
    "@babel/template": ^7.26.9
    "@babel/types": ^7.26.9
    debug: ^4.3.1
    globals: ^11.1.0
  checksum: 51dd57fa39ea34d04816806bfead04c74f37301269d24c192d1406dc6e244fea99713b3b9c5f3e926d9ef6aa9cd5c062ad4f2fc1caa9cf843d5e864484ac955e
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.25.9, @babel/types@npm:^7.26.9, @babel/types@npm:^7.3.3":
  version: 7.26.9
  resolution: "@babel/types@npm:7.26.9"
  dependencies:
    "@babel/helper-string-parser": ^7.25.9
    "@babel/helper-validator-identifier": ^7.25.9
  checksum: 999c56269ba00e5c57aa711fbe7ff071cd6990bafd1b978341ea7572cc78919986e2aa6ee51dacf4b6a7a6fa63ba4eb3f1a03cf55eee31b896a56d068b895964
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 6b80ae4cb3db53f486da2dc63b6e190a74c8c3cca16bb2733f234a0b6a9382b09b146488ae08e2b22cf00f6c83e20f3e040a2f7894f05c045c946d6a090b1d52
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: eb42729851adca56d19a08e48d5a1e95efd2a32c55ae0323de8119052be0510d4b7a1611f2abcbf28c044a6c11e6b7d38f99fccdad7429300c37a8ea5fb95b44
  languageName: node
  linkType: hard

"@commitlint/cli@npm:17.1.2":
  version: 17.1.2
  resolution: "@commitlint/cli@npm:17.1.2"
  dependencies:
    "@commitlint/format": ^17.0.0
    "@commitlint/lint": ^17.1.0
    "@commitlint/load": ^17.1.2
    "@commitlint/read": ^17.1.0
    "@commitlint/types": ^17.0.0
    execa: ^5.0.0
    lodash: ^4.17.19
    resolve-from: 5.0.0
    resolve-global: 1.0.0
    yargs: ^17.0.0
  bin:
    commitlint: cli.js
  checksum: ff5a8848e777e1e099787efd9b0dc53f533b6155261c8921e140de1e57c295d785cbb04f78b55c292ccd3bb4ce3553e9ae88e09d22eb856d29c1de6ee4fbde02
  languageName: node
  linkType: hard

"@commitlint/config-conventional@npm:17.1.0":
  version: 17.1.0
  resolution: "@commitlint/config-conventional@npm:17.1.0"
  dependencies:
    conventional-changelog-conventionalcommits: ^5.0.0
  checksum: 45511bc58f99be7d5380926fcc043f50746d45b8cda428427ae8e8466b910c16fae0acd4344b1354a42652763c575663c19edd8b76617c0530ca3258aa3f9948
  languageName: node
  linkType: hard

"@commitlint/config-validator@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/config-validator@npm:17.8.1"
  dependencies:
    "@commitlint/types": ^17.8.1
    ajv: ^8.11.0
  checksum: f60a000832c878cb2133aae34599f5b4a38d00bdbead9a07147b00b39a06a1aa59021268198795509a2bea69ddbf8c676c20209146b8d7a628405f5e6b6b9ee1
  languageName: node
  linkType: hard

"@commitlint/config-validator@npm:^19.5.0":
  version: 19.5.0
  resolution: "@commitlint/config-validator@npm:19.5.0"
  dependencies:
    "@commitlint/types": ^19.5.0
    ajv: ^8.11.0
  checksum: f04b8c66448c9a4f335d1ac9625393d471d2bcc864adc834eeec52ce19939c25475bf90677504df03ab88869e883b4ebfddff68f99f7652900d6b297ef586643
  languageName: node
  linkType: hard

"@commitlint/ensure@npm:^17.0.0, @commitlint/ensure@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/ensure@npm:17.8.1"
  dependencies:
    "@commitlint/types": ^17.8.1
    lodash.camelcase: ^4.3.0
    lodash.kebabcase: ^4.1.1
    lodash.snakecase: ^4.1.1
    lodash.startcase: ^4.4.0
    lodash.upperfirst: ^4.3.1
  checksum: 35b3b754f290cec71fa5f76e1fde02eabd8b301c24a37f2309a994cd698416c00cc4d5abc591af95846b667db01943ede9817dcb3358d7dcb73e9da1410b5ebe
  languageName: node
  linkType: hard

"@commitlint/execute-rule@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/execute-rule@npm:17.8.1"
  checksum: fa952f10caf48d934668227dcef257e406ea6c9ed0a710c1ec29984ef128c49c985f7d490ad0481dffc694da2d5bc171862e9a17feebab136b163cd92ee14f19
  languageName: node
  linkType: hard

"@commitlint/execute-rule@npm:^19.5.0":
  version: 19.5.0
  resolution: "@commitlint/execute-rule@npm:19.5.0"
  checksum: 966dfc09ae3fe609527fb49c7773ae210ade9d14a802a92a57ab251900a77d2968aed08df6b34f175bf4ae9bf5d675b52b346e7b10b717e8a635499e4cf42267
  languageName: node
  linkType: hard

"@commitlint/format@npm:^17.0.0":
  version: 17.8.1
  resolution: "@commitlint/format@npm:17.8.1"
  dependencies:
    "@commitlint/types": ^17.8.1
    chalk: ^4.1.0
  checksum: 2a42291cbff467b343a2c2c14fa049a04ba0c2913fd9e6cc7550ac31be9581c8c6d1ce4e7cadccf011228be6e1b513a704f793e5cdd82995c97a7629a68e806c
  languageName: node
  linkType: hard

"@commitlint/is-ignored@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/is-ignored@npm:17.8.1"
  dependencies:
    "@commitlint/types": ^17.8.1
    semver: 7.5.4
  checksum: 7a7f90ffb25a16a3a2e08a53e0a8c08d4c5d9646a3e3bd8372fdd8f1f8bc260a97fcf4bf58420140da4d16675c13ecc007b41836d173d4efb71942173b0717e3
  languageName: node
  linkType: hard

"@commitlint/lint@npm:^17.1.0":
  version: 17.8.1
  resolution: "@commitlint/lint@npm:17.8.1"
  dependencies:
    "@commitlint/is-ignored": ^17.8.1
    "@commitlint/parse": ^17.8.1
    "@commitlint/rules": ^17.8.1
    "@commitlint/types": ^17.8.1
  checksum: 7cd6ab67d76d7cbacaf4adf80ca1785f12882c900222cb8e575df1b766bfbeaa022b5c9f59ee3ae6f99deb6ec884787c01e4e80b28867469c7fb08a968b5b495
  languageName: node
  linkType: hard

"@commitlint/load@npm:>6.1.1":
  version: 19.6.1
  resolution: "@commitlint/load@npm:19.6.1"
  dependencies:
    "@commitlint/config-validator": ^19.5.0
    "@commitlint/execute-rule": ^19.5.0
    "@commitlint/resolve-extends": ^19.5.0
    "@commitlint/types": ^19.5.0
    chalk: ^5.3.0
    cosmiconfig: ^9.0.0
    cosmiconfig-typescript-loader: ^6.1.0
    lodash.isplainobject: ^4.0.6
    lodash.merge: ^4.6.2
    lodash.uniq: ^4.5.0
  checksum: 3f92ef6a592491dbb48ae985ef8e3897adccbbb735c09425304cbe574a0ec392b2d724ca14ebb99107e32f60bbec3b873ab64e87fea6d5af7aa579a9052a626e
  languageName: node
  linkType: hard

"@commitlint/load@npm:^17.1.2":
  version: 17.8.1
  resolution: "@commitlint/load@npm:17.8.1"
  dependencies:
    "@commitlint/config-validator": ^17.8.1
    "@commitlint/execute-rule": ^17.8.1
    "@commitlint/resolve-extends": ^17.8.1
    "@commitlint/types": ^17.8.1
    "@types/node": 20.5.1
    chalk: ^4.1.0
    cosmiconfig: ^8.0.0
    cosmiconfig-typescript-loader: ^4.0.0
    lodash.isplainobject: ^4.0.6
    lodash.merge: ^4.6.2
    lodash.uniq: ^4.5.0
    resolve-from: ^5.0.0
    ts-node: ^10.8.1
    typescript: ^4.6.4 || ^5.2.2
  checksum: 2a1345660e6deb3acd649c49487f7311d5678b8f09bd2bf9e8c6d0a1895b439c1811ff5524b0072dd251fbf751cffa199443bbb0a22a086520475227ca878bb6
  languageName: node
  linkType: hard

"@commitlint/message@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/message@npm:17.8.1"
  checksum: e8d7e7874e38e599f17865ffb6a50461de2027b09593aed5cfacc3811f21a77448586c71f8c861357d4f27673a1f5293add09f9101105c73357cdb1e29595de0
  languageName: node
  linkType: hard

"@commitlint/parse@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/parse@npm:17.8.1"
  dependencies:
    "@commitlint/types": ^17.8.1
    conventional-changelog-angular: ^6.0.0
    conventional-commits-parser: ^4.0.0
  checksum: cde1f35dbac72ac30ac9803d4342bc5784116b56e09bf1ff02738ffbaa54a43da3360bf7b10d2a6b98067eea1026ef561acef2bf762b77739e4edef0feaea318
  languageName: node
  linkType: hard

"@commitlint/prompt@npm:17.1.2":
  version: 17.1.2
  resolution: "@commitlint/prompt@npm:17.1.2"
  dependencies:
    "@commitlint/ensure": ^17.0.0
    "@commitlint/load": ^17.1.2
    "@commitlint/types": ^17.0.0
    chalk: ^4.1.0
    inquirer: ^6.5.2
    lodash: ^4.17.19
  checksum: 20587fba153342f10bd472c2f54f123671a2ae717f613e15a93a25491bf89a3298854a7cef185cf9a4f9513e1ff427417a755c8f9b0213b5e2fa2d6df4bf721b
  languageName: node
  linkType: hard

"@commitlint/read@npm:^17.1.0":
  version: 17.8.1
  resolution: "@commitlint/read@npm:17.8.1"
  dependencies:
    "@commitlint/top-level": ^17.8.1
    "@commitlint/types": ^17.8.1
    fs-extra: ^11.0.0
    git-raw-commits: ^2.0.11
    minimist: ^1.2.6
  checksum: 700dcab7f83f27a8262a8ac09e4431f5a42a5e0b180eaed0b1707ae9252d74f4686ee4fef5d8cd928a06c57bf09e876a2196f0c32dd09e285420da492d00dafa
  languageName: node
  linkType: hard

"@commitlint/resolve-extends@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/resolve-extends@npm:17.8.1"
  dependencies:
    "@commitlint/config-validator": ^17.8.1
    "@commitlint/types": ^17.8.1
    import-fresh: ^3.0.0
    lodash.mergewith: ^4.6.2
    resolve-from: ^5.0.0
    resolve-global: ^1.0.0
  checksum: 785fa1ed4675671383dd6ee55dabfba662d0f336a038ae6e84aacc6d8ffd03033df5f43c3d2daf4bc1047060a54efe1c1255517ca8eb6f50ec7f2874c6db182d
  languageName: node
  linkType: hard

"@commitlint/resolve-extends@npm:^19.5.0":
  version: 19.5.0
  resolution: "@commitlint/resolve-extends@npm:19.5.0"
  dependencies:
    "@commitlint/config-validator": ^19.5.0
    "@commitlint/types": ^19.5.0
    global-directory: ^4.0.1
    import-meta-resolve: ^4.0.0
    lodash.mergewith: ^4.6.2
    resolve-from: ^5.0.0
  checksum: 10569a46036b7aa93c77dc5001a67bc9f36b340b97b2fd39b5ee95b0efc5e35335c61f86d4ba0bb5a8e6dd49ccf956990cce9ee29cfea9ba567e02668be01841
  languageName: node
  linkType: hard

"@commitlint/rules@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/rules@npm:17.8.1"
  dependencies:
    "@commitlint/ensure": ^17.8.1
    "@commitlint/message": ^17.8.1
    "@commitlint/to-lines": ^17.8.1
    "@commitlint/types": ^17.8.1
    execa: ^5.0.0
  checksum: f8139c86d998a984cc9d873a8650cb28edf4b0da16351f6a0787d920b47209f8a346ce0c6405257a3cf1ab7e238805d93fd708dea63f82a25506a970a6fa350e
  languageName: node
  linkType: hard

"@commitlint/to-lines@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/to-lines@npm:17.8.1"
  checksum: 14d70d2f4826fd00236a2a36f8ab18ea44892d5fd82f50a99fe996f92a9efdedf50864dddaff7f266da8140eee6f2e255ce3f8b77bac04532c13b37d49761698
  languageName: node
  linkType: hard

"@commitlint/top-level@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/top-level@npm:17.8.1"
  dependencies:
    find-up: ^5.0.0
  checksum: 0b68105cad4762fb75a46643850e43c793b359233f11eafa3591cc944756cd906211ef17fb34ce8365723077c2025b1f5d240f1f02fc423b8aa9b69c7d20bdf2
  languageName: node
  linkType: hard

"@commitlint/types@npm:^17.0.0, @commitlint/types@npm:^17.8.1":
  version: 17.8.1
  resolution: "@commitlint/types@npm:17.8.1"
  dependencies:
    chalk: ^4.1.0
  checksum: 303528008d4c8b2e5b9a4a8177a072ead740cfbc1bad47b5327466a78c4029730bfaf805181dd38e86f38f2981ad20e6d2195fb5fcb0aa91afb8e87c2c848383
  languageName: node
  linkType: hard

"@commitlint/types@npm:^19.5.0":
  version: 19.5.0
  resolution: "@commitlint/types@npm:19.5.0"
  dependencies:
    "@types/conventional-commits-parser": ^5.0.0
    chalk: ^5.3.0
  checksum: f4a93992f43b23cd5af200c69bb73227fdc0f78a6f7ebcda73dad10d558c1ac66ff164aa6dc3c2ddb322c9ed8b1a89b05f458e40d7c440a0358f435d2d71c2df
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": 0.3.9
  checksum: 05c5368c13b662ee4c122c7bfbe5dc0b613416672a829f3e78bc49a357a197e0218d6e74e7c66cfcd04e15a179acab080bd3c69658c9fbefd0e1ccd950a07fc6
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0":
  version: 4.4.1
  resolution: "@eslint-community/eslint-utils@npm:4.4.1"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 2aa0ac2fc50ff3f234408b10900ed4f1a0b19352f21346ad4cc3d83a1271481bdda11097baa45d484dd564c895e0762a27a8240be7a256b3ad47129e96528252
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.4.0, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^9.6.0
    globals: ^13.19.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 32f67052b81768ae876c84569ffd562491ec5a5091b0c1e1ca1e0f3c24fb42f804952fdd0a137873bc64303ba368a71ba079a6f691cee25beee9722d94cc8573
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.1":
  version: 8.57.1
  resolution: "@eslint/js@npm:8.57.1"
  checksum: b489c474a3b5b54381c62e82b3f7f65f4b8a5eaaed126546520bf2fede5532a8ed53212919fed1e9048dcf7f37167c8561d58d0ba4492a4244004e7793805223
  languageName: node
  linkType: hard

"@fast-csv/format@npm:4.3.5":
  version: 4.3.5
  resolution: "@fast-csv/format@npm:4.3.5"
  dependencies:
    "@types/node": ^14.0.1
    lodash.escaperegexp: ^4.1.2
    lodash.isboolean: ^3.0.3
    lodash.isequal: ^4.5.0
    lodash.isfunction: ^3.0.9
    lodash.isnil: ^4.0.0
  checksum: 06c6b3310edaf08033236539b93ebed027ea36f9f8a3cf42069034d4f75dff103a35930c9a9f01e2e344d8836fb2cc55a16affb5c345a8b3682d5a0cb031e0ea
  languageName: node
  linkType: hard

"@fast-csv/parse@npm:4.3.6":
  version: 4.3.6
  resolution: "@fast-csv/parse@npm:4.3.6"
  dependencies:
    "@types/node": ^14.0.1
    lodash.escaperegexp: ^4.1.2
    lodash.groupby: ^4.6.0
    lodash.isfunction: ^3.0.9
    lodash.isnil: ^4.0.0
    lodash.isundefined: ^3.0.1
    lodash.uniq: ^4.5.0
  checksum: dfd1834bfcea2665bd9db05b21793f79fd3502abdf955d6d63f7bf9724082f0b67a6379687b2945ca8d513b4d383a7bdaeed72a03cabd9191032b81448379917
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.13.0":
  version: 0.13.0
  resolution: "@humanwhocodes/config-array@npm:0.13.0"
  dependencies:
    "@humanwhocodes/object-schema": ^2.0.3
    debug: ^4.3.1
    minimatch: ^3.0.5
  checksum: 205c99e756b759f92e1f44a3dc6292b37db199beacba8f26c2165d4051fe73a4ae52fdcfd08ffa93e7e5cb63da7c88648f0e84e197d154bbbbe137b2e0dd332e
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.3":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: 80520eabbfc2d32fe195a93557cef50dfe8c8905de447f022675aaf66abc33ae54098f5ea78548d925aa671cd4ab7c7daa5ad704fe42358c9b5e7db60f80696c
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: ^5.3.1
    find-up: ^4.1.0
    get-package-type: ^0.1.0
    js-yaml: ^3.13.1
    resolve-from: ^5.0.0
  checksum: dd2a8b094887da5a1a2339543a4933d06db2e63cbbc2e288eb6431bd832065df0c099d091b6a67436e71b7d6bf85f01ce7c15f9253b4cbebcc3b9a496165ba42
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jest/console@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/console@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    "@types/node": "*"
    chalk: ^4.0.0
    jest-message-util: ^28.1.3
    jest-util: ^28.1.3
    slash: ^3.0.0
  checksum: c539b814cd9d3eadb53ce04e2ac00716fe0d808511cb64aebf2920bcb1646c65f094188a7f9aa74fca73a501c00ee5835e906717dc3682cbb4ecf7fbb316fc75
  languageName: node
  linkType: hard

"@jest/core@npm:^28.1.2, @jest/core@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/core@npm:28.1.3"
  dependencies:
    "@jest/console": ^28.1.3
    "@jest/reporters": ^28.1.3
    "@jest/test-result": ^28.1.3
    "@jest/transform": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    ci-info: ^3.2.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-changed-files: ^28.1.3
    jest-config: ^28.1.3
    jest-haste-map: ^28.1.3
    jest-message-util: ^28.1.3
    jest-regex-util: ^28.0.2
    jest-resolve: ^28.1.3
    jest-resolve-dependencies: ^28.1.3
    jest-runner: ^28.1.3
    jest-runtime: ^28.1.3
    jest-snapshot: ^28.1.3
    jest-util: ^28.1.3
    jest-validate: ^28.1.3
    jest-watcher: ^28.1.3
    micromatch: ^4.0.4
    pretty-format: ^28.1.3
    rimraf: ^3.0.0
    slash: ^3.0.0
    strip-ansi: ^6.0.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: eac1ac262303344cccace0cef9cee57298a90aa376e649f46110e8e950bb2b36579b9dc273b1f958fa9dca2c0c152b8b3107faf5ecb76a1e8109fdf9cbe4e600
  languageName: node
  linkType: hard

"@jest/environment@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/environment@npm:28.1.3"
  dependencies:
    "@jest/fake-timers": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/node": "*"
    jest-mock: ^28.1.3
  checksum: 910b8863f300e0627c8f7bf6280fe51da25060e72ac1179d959cce74907b048e64042ad192800259a037dc52faa2e361e778a94df223cf1b17a315e5eec5471e
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/expect-utils@npm:28.1.3"
  dependencies:
    jest-get-type: ^28.0.2
  checksum: 6cb424bf24c9a20d7420601fb5599a563f09c1771cc8df3399a291f77f3cb512cfa06e6b0bce23b8b078d333d2713572fae298c6a017ca9bbe26d6b05f7bae46
  languageName: node
  linkType: hard

"@jest/expect@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/expect@npm:28.1.3"
  dependencies:
    expect: ^28.1.3
    jest-snapshot: ^28.1.3
  checksum: 6000cd5322bca35b9e920a822f3e093d01d646508e5eb639f0a2577f203f15143315b93e79e412525e7312a2290e1bac979b26f6417ebaf50799a3a38eb6d011
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/fake-timers@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    "@sinonjs/fake-timers": ^9.1.2
    "@types/node": "*"
    jest-message-util: ^28.1.3
    jest-mock: ^28.1.3
    jest-util: ^28.1.3
  checksum: 70ca341df62bf51a9bed653743dfc17011df58995520b51730ee7f5aef26a0295a5f5b58e838e6dbace998de417aa1c3a77d6de1590b53065475a195601552c6
  languageName: node
  linkType: hard

"@jest/globals@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/globals@npm:28.1.3"
  dependencies:
    "@jest/environment": ^28.1.3
    "@jest/expect": ^28.1.3
    "@jest/types": ^28.1.3
  checksum: de95367a5e7312b643bfa5f6ac760fbfa4ac626abec11444702bc08506c32e9da44fc5ad5bf3049115b0757533cb0f4b90be3eb7fcea5d4ef06c31fe8ed0b579
  languageName: node
  linkType: hard

"@jest/reporters@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/reporters@npm:28.1.3"
  dependencies:
    "@bcoe/v8-coverage": ^0.2.3
    "@jest/console": ^28.1.3
    "@jest/test-result": ^28.1.3
    "@jest/transform": ^28.1.3
    "@jest/types": ^28.1.3
    "@jridgewell/trace-mapping": ^0.3.13
    "@types/node": "*"
    chalk: ^4.0.0
    collect-v8-coverage: ^1.0.0
    exit: ^0.1.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    istanbul-lib-coverage: ^3.0.0
    istanbul-lib-instrument: ^5.1.0
    istanbul-lib-report: ^3.0.0
    istanbul-lib-source-maps: ^4.0.0
    istanbul-reports: ^3.1.3
    jest-message-util: ^28.1.3
    jest-util: ^28.1.3
    jest-worker: ^28.1.3
    slash: ^3.0.0
    string-length: ^4.0.1
    strip-ansi: ^6.0.0
    terminal-link: ^2.0.0
    v8-to-istanbul: ^9.0.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: c54f989d8b2bca758a4740826042329399d7c4e1a47a67ccefede05db0a9f414fcb1f30ec3ce7b6c4f58843383fd3d24b0cc9e6d9390f90ba6a3edaf9f9c093c
  languageName: node
  linkType: hard

"@jest/schemas@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/schemas@npm:28.1.3"
  dependencies:
    "@sinclair/typebox": ^0.24.1
  checksum: 8c325918f3e1b83e687987b05c2e5143d171f372b091f891fe17835f06fadd864ddae3c7e221a704bdd7e2ea28c4b337124c02023d8affcbdd51eca2879162ac
  languageName: node
  linkType: hard

"@jest/source-map@npm:^28.1.2":
  version: 28.1.2
  resolution: "@jest/source-map@npm:28.1.2"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.13
    callsites: ^3.0.0
    graceful-fs: ^4.2.9
  checksum: 535036de941aa98bff1c46a77fb2e98ec1f78f5b101a8c8b3c1a7e3e863a1a71ea3aef111afc4ef9d44c39b4e7e7c8384412d0a685138a92c6d522fdb8cd5b3b
  languageName: node
  linkType: hard

"@jest/test-result@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/test-result@npm:28.1.3"
  dependencies:
    "@jest/console": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/istanbul-lib-coverage": ^2.0.0
    collect-v8-coverage: ^1.0.0
  checksum: 2dcc5dda444d4a308c6cb5b62f71a72ee5ff5702541e7faeec0314b4d50139d9004efd503baa15dec692856005c8a5c4afc3a94dabd92825645832eb12f00bea
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/test-sequencer@npm:28.1.3"
  dependencies:
    "@jest/test-result": ^28.1.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^28.1.3
    slash: ^3.0.0
  checksum: 7401537789902edc9c0cf2333a5052e8f8d936aa45ac4074fa1dc0af928c8a53e4b21802019bc4b6c01a66be2aba6d9aaa04ab97c6729a123476d9cf4f69eace
  languageName: node
  linkType: hard

"@jest/transform@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/transform@npm:28.1.3"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/types": ^28.1.3
    "@jridgewell/trace-mapping": ^0.3.13
    babel-plugin-istanbul: ^6.1.1
    chalk: ^4.0.0
    convert-source-map: ^1.4.0
    fast-json-stable-stringify: ^2.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^28.1.3
    jest-regex-util: ^28.0.2
    jest-util: ^28.1.3
    micromatch: ^4.0.4
    pirates: ^4.0.4
    slash: ^3.0.0
    write-file-atomic: ^4.0.1
  checksum: d4211fb30ad17a450a86ab1af488762742b00480c4f76684ba0ad9b2ffc34a0d309a922514775de36a5b74aa8e22ec833e38600565dbbd0596a041fbe9ecf44c
  languageName: node
  linkType: hard

"@jest/types@npm:^28.1.1, @jest/types@npm:^28.1.3":
  version: 28.1.3
  resolution: "@jest/types@npm:28.1.3"
  dependencies:
    "@jest/schemas": ^28.1.3
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^17.0.8
    chalk: ^4.0.0
  checksum: 3cffae7d1133aa7952a6b5c4806f89ed78cb0dfe3ec4e8c5a6e704d7bab3cff86c714abb5f0f637540da22776900a33b3bad79c5ed5fc5b5535fb24e3006e3cb
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3, @jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
  checksum: 6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.13, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": ^3.0.3
    "@jridgewell/sourcemap-codec": ^1.4.10
  checksum: fa425b606d7c7ee5bfa6a31a7b050dd5814b4082f318e0e4190f991902181b4330f43f4805db1dd4f2433fd0ed9cc7a7b9c2683f1deeab1df1b0a98b1e24055b
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.13, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@lukeed/csprng@npm:^1.0.0":
  version: 1.1.0
  resolution: "@lukeed/csprng@npm:1.1.0"
  checksum: 5d6dcf478af732972083ab2889c294b57f1028fa13c2c240d7a4aaa079c2c75df7ef0dcbdda5419147fc6704b4adf96b2de92f1a9a72ac21c6350c4014fffe6c
  languageName: node
  linkType: hard

"@mapbox/node-pre-gyp@npm:^1.0.11":
  version: 1.0.11
  resolution: "@mapbox/node-pre-gyp@npm:1.0.11"
  dependencies:
    detect-libc: ^2.0.0
    https-proxy-agent: ^5.0.0
    make-dir: ^3.1.0
    node-fetch: ^2.6.7
    nopt: ^5.0.0
    npmlog: ^5.0.1
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.11
  bin:
    node-pre-gyp: bin/node-pre-gyp
  checksum: 2b24b93c31beca1c91336fa3b3769fda98e202fb7f9771f0f4062588d36dcc30fcf8118c36aa747fa7f7610d8cf601872bdaaf62ce7822bb08b545d1bbe086cc
  languageName: node
  linkType: hard

"@nestjs/axios@npm:^3.0.0":
  version: 3.1.3
  resolution: "@nestjs/axios@npm:3.1.3"
  peerDependencies:
    "@nestjs/common": ^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0
    axios: ^1.3.1
    rxjs: ^6.0.0 || ^7.0.0
  checksum: 3b3f5ecc9a17317daafbf6ffe0cb792c5bd44d9fe7c6e2bda5d87163b9c2ed05a71a49d4e2d810c455eaa94f25e85e63673da21d674917bd9c16dfb937771109
  languageName: node
  linkType: hard

"@nestjs/cli@npm:9.2.0":
  version: 9.2.0
  resolution: "@nestjs/cli@npm:9.2.0"
  dependencies:
    "@angular-devkit/core": 15.1.4
    "@angular-devkit/schematics": 15.1.4
    "@angular-devkit/schematics-cli": 15.1.4
    "@nestjs/schematics": ^9.0.0
    chalk: 3.0.0
    chokidar: 3.5.3
    cli-table3: 0.6.3
    commander: 4.1.1
    fork-ts-checker-webpack-plugin: 7.3.0
    inquirer: 7.3.3
    node-emoji: 1.11.0
    ora: 5.4.1
    os-name: 4.0.1
    rimraf: 4.1.2
    shelljs: 0.8.5
    source-map-support: 0.5.21
    tree-kill: 1.2.2
    tsconfig-paths: 4.1.2
    tsconfig-paths-webpack-plugin: 4.0.0
    typescript: 4.9.5
    webpack: 5.75.0
    webpack-node-externals: 3.0.0
  bin:
    nest: bin/nest.js
  checksum: a7784b1cfa8a2ea144065be095dfe2662140bf210b683fc6af19787f0c755406c19c895fb1729e738be88311089ddc843f4aacd83f23966e88eca0c45261fc42
  languageName: node
  linkType: hard

"@nestjs/cli@npm:^8.0.0":
  version: 8.2.8
  resolution: "@nestjs/cli@npm:8.2.8"
  dependencies:
    "@angular-devkit/core": 13.3.6
    "@angular-devkit/schematics": 13.3.6
    "@angular-devkit/schematics-cli": 13.3.6
    "@nestjs/schematics": ^8.0.3
    chalk: 3.0.0
    chokidar: 3.5.3
    cli-table3: 0.6.2
    commander: 4.1.1
    fork-ts-checker-webpack-plugin: 7.2.11
    inquirer: 7.3.3
    node-emoji: 1.11.0
    ora: 5.4.1
    os-name: 4.0.1
    rimraf: 3.0.2
    shelljs: 0.8.5
    source-map-support: 0.5.21
    tree-kill: 1.2.2
    tsconfig-paths: 3.14.1
    tsconfig-paths-webpack-plugin: 3.5.2
    typescript: 4.7.4
    webpack: 5.73.0
    webpack-node-externals: 3.0.0
  bin:
    nest: bin/nest.js
  checksum: bea14c372174a926ba67cd1ee439eca6efc6585bfe167d3851d9d019003f1b3f3b73d9fc4b3970a62e337a0755d08c474d1d7e570128ecf559855289dcceb2de
  languageName: node
  linkType: hard

"@nestjs/common@npm:^9.0.0":
  version: 9.4.3
  resolution: "@nestjs/common@npm:9.4.3"
  dependencies:
    iterare: 1.2.1
    tslib: 2.5.3
    uid: 2.0.2
  peerDependencies:
    cache-manager: <=5
    class-transformer: "*"
    class-validator: "*"
    reflect-metadata: ^0.1.12
    rxjs: ^7.1.0
  peerDependenciesMeta:
    cache-manager:
      optional: true
    class-transformer:
      optional: true
    class-validator:
      optional: true
  checksum: 4776643f768ed32eff4b72f1d8ed2103a3e689e04d63b17d307776aaedc4ea022ba3edfd97a8d59812633f53b12bcb4f45d7b09af8c5df532eaead355e5dfec3
  languageName: node
  linkType: hard

"@nestjs/config@npm:^4.0.2":
  version: 4.0.2
  resolution: "@nestjs/config@npm:4.0.2"
  dependencies:
    dotenv: 16.4.7
    dotenv-expand: 12.0.1
    lodash: 4.17.21
  peerDependencies:
    "@nestjs/common": ^10.0.0 || ^11.0.0
    rxjs: ^7.1.0
  checksum: 549bc8d784f68742c8020954be250639cd8d37a0dd233d01ebe511f67268e5fd109ffb1ae546ca9792a8484ef4cbb79374ae7e2a9587f59b13db1e9a16d443d6
  languageName: node
  linkType: hard

"@nestjs/core@npm:^9.0.0":
  version: 9.4.3
  resolution: "@nestjs/core@npm:9.4.3"
  dependencies:
    "@nuxtjs/opencollective": 0.3.2
    fast-safe-stringify: 2.1.1
    iterare: 1.2.1
    path-to-regexp: 3.2.0
    tslib: 2.5.3
    uid: 2.0.2
  peerDependencies:
    "@nestjs/common": ^9.0.0
    "@nestjs/microservices": ^9.0.0
    "@nestjs/platform-express": ^9.0.0
    "@nestjs/websockets": ^9.0.0
    reflect-metadata: ^0.1.12
    rxjs: ^7.1.0
  peerDependenciesMeta:
    "@nestjs/microservices":
      optional: true
    "@nestjs/platform-express":
      optional: true
    "@nestjs/websockets":
      optional: true
  checksum: 595bfd46378afb6f5142d7ab4d4ba7f406ea02d08263f41bc1e36d6da637b483b4c32181622cfbaa1cc39ef00d47235ed944aad0e0186b3b191073c6ebd3647c
  languageName: node
  linkType: hard

"@nestjs/jwt@npm:^11.0.0":
  version: 11.0.0
  resolution: "@nestjs/jwt@npm:11.0.0"
  dependencies:
    "@types/jsonwebtoken": 9.0.7
    jsonwebtoken: 9.0.2
  peerDependencies:
    "@nestjs/common": ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0
  checksum: e4c28d7feb02c022a0febb8c426785ba292f959cf9f7cd66d4d48a6fbcc76d90c6a0b44065c1e21d743fcb914b91113e354bbb3b96995e256aaa6c6469b5c439
  languageName: node
  linkType: hard

"@nestjs/mapped-types@npm:1.2.2":
  version: 1.2.2
  resolution: "@nestjs/mapped-types@npm:1.2.2"
  peerDependencies:
    "@nestjs/common": ^7.0.8 || ^8.0.0 || ^9.0.0
    class-transformer: ^0.2.0 || ^0.3.0 || ^0.4.0 || ^0.5.0
    class-validator: ^0.11.1 || ^0.12.0 || ^0.13.0 || ^0.14.0
    reflect-metadata: ^0.1.12
  peerDependenciesMeta:
    class-transformer:
      optional: true
    class-validator:
      optional: true
  checksum: e80a08787a6ac2a27199f8d475a338e85a7113a413b6347c700b3ec3e2ee45c59220929c4a79b7a61e05bf9d146fa8c7ba8438a93709542148d7281aae7544e8
  languageName: node
  linkType: hard

"@nestjs/passport@npm:^11.0.5":
  version: 11.0.5
  resolution: "@nestjs/passport@npm:11.0.5"
  peerDependencies:
    "@nestjs/common": ^10.0.0 || ^11.0.0
    passport: ^0.5.0 || ^0.6.0 || ^0.7.0
  checksum: 24175f6791abf02b70c3c0705ce56fefd3981fdece0da7e58325f790ce71aa6932b31a87a2ceaa52b6d9d8edab3a7c049f6081e4b4d59bcdfcaa00383b64ae02
  languageName: node
  linkType: hard

"@nestjs/platform-express@npm:^9.0.0":
  version: 9.4.3
  resolution: "@nestjs/platform-express@npm:9.4.3"
  dependencies:
    body-parser: 1.20.2
    cors: 2.8.5
    express: 4.18.2
    multer: 1.4.4-lts.1
    tslib: 2.5.3
  peerDependencies:
    "@nestjs/common": ^9.0.0
    "@nestjs/core": ^9.0.0
  checksum: 7444872a5a6b9c2722561ff9c3722987f9654122098ef3f976d93bd55316be2b865be9c19756e529327346a032ff3ddbba0d9bd11bfc3526c8a5ffbe57173b43
  languageName: node
  linkType: hard

"@nestjs/schematics@npm:^8.0.0, @nestjs/schematics@npm:^8.0.3":
  version: 8.0.11
  resolution: "@nestjs/schematics@npm:8.0.11"
  dependencies:
    "@angular-devkit/core": 13.3.5
    "@angular-devkit/schematics": 13.3.5
    fs-extra: 10.1.0
    jsonc-parser: 3.0.0
    pluralize: 8.0.0
  peerDependencies:
    typescript: ^3.4.5 || ^4.3.5
  checksum: f5e08bb1177d896ff491379fce73d91f07c44013bdd50ac5e5cca17c5af667d2beeec5f1dbdce24bb4f2a47b6211a154d344dcdce2a4a48b92f7012cd806ee99
  languageName: node
  linkType: hard

"@nestjs/schematics@npm:^9.0.0":
  version: 9.2.0
  resolution: "@nestjs/schematics@npm:9.2.0"
  dependencies:
    "@angular-devkit/core": 16.0.1
    "@angular-devkit/schematics": 16.0.1
    jsonc-parser: 3.2.0
    pluralize: 8.0.0
  peerDependencies:
    typescript: ">=4.3.5"
  checksum: 8ce2de63f5d2d8ecd5fd5f761e1d718c0921423b620aeed2ac521d61d362aea83995f0081bd8d9af1796fa1f7d14751c5783142b9783aed36ac66c53b19106e0
  languageName: node
  linkType: hard

"@nestjs/swagger@npm:^6.2.1":
  version: 6.3.0
  resolution: "@nestjs/swagger@npm:6.3.0"
  dependencies:
    "@nestjs/mapped-types": 1.2.2
    js-yaml: 4.1.0
    lodash: 4.17.21
    path-to-regexp: 3.2.0
    swagger-ui-dist: 4.18.2
  peerDependencies:
    "@fastify/static": ^6.0.0
    "@nestjs/common": ^9.0.0
    "@nestjs/core": ^9.0.0
    class-transformer: "*"
    class-validator: "*"
    reflect-metadata: ^0.1.12
  peerDependenciesMeta:
    "@fastify/static":
      optional: true
    class-transformer:
      optional: true
    class-validator:
      optional: true
  checksum: 545a50611249b559ef52cd1219ec4514836c03f9351a8a9e3490b7e56eb2ee4e59cc7bac3fe0a0ebc9c60cc14d0f90942793a277cb1bb44d95d12cede18c7c65
  languageName: node
  linkType: hard

"@nestjs/testing@npm:^9.0.0":
  version: 9.4.3
  resolution: "@nestjs/testing@npm:9.4.3"
  dependencies:
    tslib: 2.5.3
  peerDependencies:
    "@nestjs/common": ^9.0.0
    "@nestjs/core": ^9.0.0
    "@nestjs/microservices": ^9.0.0
    "@nestjs/platform-express": ^9.0.0
  peerDependenciesMeta:
    "@nestjs/microservices":
      optional: true
    "@nestjs/platform-express":
      optional: true
  checksum: 120ddc30a74171f41b4195360c7e6e8409035c762f87f51873469a6f0a52b6738c0ed882d4bfafdcf0b1bd28734cb1b0bd150ed1ace1034c41cda80b516e2f48
  languageName: node
  linkType: hard

"@nestjs/typeorm@npm:^9.0.1":
  version: 9.0.1
  resolution: "@nestjs/typeorm@npm:9.0.1"
  dependencies:
    uuid: 8.3.2
  peerDependencies:
    "@nestjs/common": ^8.0.0 || ^9.0.0
    "@nestjs/core": ^8.0.0 || ^9.0.0
    reflect-metadata: ^0.1.13
    rxjs: ^7.2.0
    typeorm: ^0.3.0
  checksum: fe4d408dcd3ee22b3710db07d841daed15448a2cd43f61e7634cb2a05c76b9641d085775a5c6dbf876e960139a864d15a684c5534ca8ec1c129b07a2d4e6eca0
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: 732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@nuxtjs/opencollective@npm:0.3.2":
  version: 0.3.2
  resolution: "@nuxtjs/opencollective@npm:0.3.2"
  dependencies:
    chalk: ^4.1.0
    consola: ^2.15.0
    node-fetch: ^2.6.1
  bin:
    opencollective: bin/opencollective.js
  checksum: 540268687af3289ff107585484d42201b404cdbb98b3a512487c12a6b180a8f0e1df0d701df47d3d9e0d5c0f6eb3252d80535562aedca9edf52cf7fd17ae4601
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.24.1":
  version: 0.24.51
  resolution: "@sinclair/typebox@npm:0.24.51"
  checksum: 458131e83ca59ad3721f0abeef2aa5220aff2083767e1143d75c67c85d55ef7a212f48f394471ee6bdd2e860ba30f09a489cdd2a28a2824d5b0d1014bdfb2552
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^1.7.0":
  version: 1.8.6
  resolution: "@sinonjs/commons@npm:1.8.6"
  dependencies:
    type-detect: 4.0.8
  checksum: 93b4d4e27e93652b83467869c2fe09cbd8f37cd5582327f0e081fbf9b93899e2d267db7b668c96810c63dc229867614ced825e5512b47db96ca6f87cb3ec0f61
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^9.1.2":
  version: 9.1.2
  resolution: "@sinonjs/fake-timers@npm:9.1.2"
  dependencies:
    "@sinonjs/commons": ^1.7.0
  checksum: d9187f9130f03272562ff9845867299c6f7cf15157bbb3e6aca4a1f06d885b0eef54259d0ad41e2f8043dc530b4db730b6c9415b169033e7ba8fed0ad449ceec
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/abort-controller@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: d5647478fa61d5d1cf3ac8fe5b91955c679ecf48e0d71638c0ce908fbcc87f166e42722d181f33ae3c37761de89e48c5eecf620f6fd0e99cd86edbb8365dd38d
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader-native@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/chunked-blob-reader-native@npm:4.0.0"
  dependencies:
    "@smithy/util-base64": ^4.0.0
    tslib: ^2.6.2
  checksum: 4387f4e8841f20c1c4e689078141de7e6f239e7883be3a02810a023aa30939b15576ee00227b991972d2c5a2f3b6152bcaeca0975c9fa8d3669354c647bd532a
  languageName: node
  linkType: hard

"@smithy/chunked-blob-reader@npm:^5.0.0":
  version: 5.0.0
  resolution: "@smithy/chunked-blob-reader@npm:5.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 55ba0fe366ddaa3f93e1faf8a70df0b67efedbd0008922295efe215df09b68df0ba3043293e65b17e7d1be71448d074c2bfc54e5eb6bd18f59b425822c2b9e9a
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.1.0":
  version: 4.1.0
  resolution: "@smithy/config-resolver@npm:4.1.0"
  dependencies:
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.2
    tslib: ^2.6.2
  checksum: db67064f27981452788ef8cffa3146a1896b50911379febda7315e0657e4fe3bba6c52414670b9778eb986fe06f7e50d10e7373fa644975a0491d27333e909de
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.3.1":
  version: 3.3.1
  resolution: "@smithy/core@npm:3.3.1"
  dependencies:
    "@smithy/middleware-serde": ^4.0.3
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-stream": ^4.2.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: c11f914ac9b798a6c7da2e1e8d164264118f4547b1a4eedc9c0e8ad329050f25ffa68af7658bcbbe3a852cd9c0055b891856a659bc380c87eca755efff0a548f
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/credential-provider-imds@npm:4.0.2"
  dependencies:
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/property-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    "@smithy/url-parser": ^4.0.2
    tslib: ^2.6.2
  checksum: 516482c103bd42d93de584ec75fa75d1184541816a7b430db109f8ec18abcaf8eb7bd072660fbf417f37a3df5c7438a1ba92aabd5a185ed736be1a6e885f0999
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/eventstream-codec@npm:4.0.2"
  dependencies:
    "@aws-crypto/crc32": 5.2.0
    "@smithy/types": ^4.2.0
    "@smithy/util-hex-encoding": ^4.0.0
    tslib: ^2.6.2
  checksum: 865a44e74c81e3177608f8931e22c92c851f586c1344962db3810b2e23d39d4da2d5f7a933d24481c0b6df219404e34db463e76294d3f71445f7d4e5721aa4ea
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-browser@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/eventstream-serde-browser@npm:4.0.2"
  dependencies:
    "@smithy/eventstream-serde-universal": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 6974a13448b733b4d98eb368a202a5c2d86389efe10e1d147be1392fb016e010d490368773d915d719973a4d29c419fab7b0eff2dd0abf1f65d1cd3bf26f4fc2
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-config-resolver@npm:^4.1.0":
  version: 4.1.0
  resolution: "@smithy/eventstream-serde-config-resolver@npm:4.1.0"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 41ae76c9ad429e09908658db36f79f0c172496d9ba2727b3566692915bd8ef6df56d692ec1b30d9fa69cfa287d138bccd70422db404d4eef0792fc358d338787
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-node@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/eventstream-serde-node@npm:4.0.2"
  dependencies:
    "@smithy/eventstream-serde-universal": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 6f22010305ac1514d19d78dbb14866bd9b9739a72ef557355514ceb264be6aeb40532758c2e3f70e811a762e7efacd8a6eb64c4d859bdcb79253bf92ee8f60ad
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-universal@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/eventstream-serde-universal@npm:4.0.2"
  dependencies:
    "@smithy/eventstream-codec": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 1e486919a7d454c4f5a7f8756d74061943dcf5a72b1ba6f735c0e4e34afabe357713e42daed99ea2c4f52995fb37185bd2b02e6778c2aaf02206068e2ebc306c
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.0.2":
  version: 5.0.2
  resolution: "@smithy/fetch-http-handler@npm:5.0.2"
  dependencies:
    "@smithy/protocol-http": ^5.1.0
    "@smithy/querystring-builder": ^4.0.2
    "@smithy/types": ^4.2.0
    "@smithy/util-base64": ^4.0.0
    tslib: ^2.6.2
  checksum: 3bf84a1fe93c07558a5ba520ab0aca62518c13659d5794094764aaef95acfbcf58ba938c51b9269c485304fdbe7353eb3cd37d7e4c57863d7c50478a9e3ff4fc
  languageName: node
  linkType: hard

"@smithy/hash-blob-browser@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/hash-blob-browser@npm:4.0.2"
  dependencies:
    "@smithy/chunked-blob-reader": ^5.0.0
    "@smithy/chunked-blob-reader-native": ^4.0.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 08b6f1893803c51e7a40256c9c819a0f3a6ff26acf235abe1b2667393094bab982232d0a395d9533e2d4b7af9ab8bedb2ee71ed6bc502669ee5d2901bdabc54a
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/hash-node@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: aaec3fb2146d4347e97067de4dd91759de9d0254d03e234dcced1cbd52cf8b3a77067d571bd5767cb6295da7aa7261b87a789bd597cbc45a380cd90bb47f3490
  languageName: node
  linkType: hard

"@smithy/hash-stream-node@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/hash-stream-node@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 4c1477c4064e47e026c0ba051eb643a3ed2f850a4e87a8ee5ff91547e3765ebb64e797ce99553aa341448df0bfa1d9e9d7132216ac66c6d9e45aae82ecb1c4d6
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/invalid-dependency@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: f0b884ba25c371d3d3f507aebc24e598e23edeadf0a74dfd7092fc49c496cd427ab517454ebde454b2a05916719e01aa98f34603a5396455cc2dc009ad8799e8
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 2f2523cd8cc4538131e408eb31664983fecb0c8724956788b015aaf3ab85a0c976b50f4f09b176f1ed7bbe79f3edf80743be7a80a11f22cd9ce1285d77161aaf
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: ae393fbd5944d710443cd5dd225d1178ef7fb5d6259c14f3e1316ec75e401bda6cf86f7eb98bfd38e5ed76e664b810426a5756b916702cbd418f0933e15e7a3b
  languageName: node
  linkType: hard

"@smithy/md5-js@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/md5-js@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: b50962dc5155d44bbc0bc317eaab1144ade8d691c3f0c0e844b45fc1e16423742e9a1628b2358657b405e05ee681cebd3abc72e94a9c362b82bd4efbee5b8076
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/middleware-content-length@npm:4.0.2"
  dependencies:
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 4ab343b68a15cf461f3b5996460a0730463975d9da739cf40cfb5993794023269a8bd857366f855844290fabb2b340abb6ff473cec4bfd3d6653a64f17e00c4a
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.1.2":
  version: 4.1.2
  resolution: "@smithy/middleware-endpoint@npm:4.1.2"
  dependencies:
    "@smithy/core": ^3.3.1
    "@smithy/middleware-serde": ^4.0.3
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    "@smithy/url-parser": ^4.0.2
    "@smithy/util-middleware": ^4.0.2
    tslib: ^2.6.2
  checksum: d37ccbe77a319e7456f0adc3af0b07ddb73b73e84349cb6f25a89fb1104caf045b44e6f4327a3e9187175431c3336785d83967c9591bc75bee1af6d7fceb04b9
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.1.3":
  version: 4.1.3
  resolution: "@smithy/middleware-retry@npm:4.1.3"
  dependencies:
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/service-error-classification": ^4.0.3
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-retry": ^4.0.3
    tslib: ^2.6.2
    uuid: ^9.0.1
  checksum: fc6680454ea5b2bb5bb9e866a59f8c316ba36eb19d2ab400170f798d68c4af002463a175e2d3437d3bba820d2ba9f00ea504fd7f9b529e45cfdbd4cf06f5476d
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/middleware-serde@npm:4.0.3"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 0a3b037c8f1cade46abf9c782fe11da3f1a92d59f3c61d5806fe26a0f3c8b20d5e7e9ab919549ba33762e63fb02c60b5e436b9459647af39300b37d975acde2e
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/middleware-stack@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: ef94882966431729f7a7bddf8206b6495d67736b1f26fd88d6d6c283a96f9fffd12632ed7352e5f060f17d3ee1845a9a9da1247c26e4c46ff7011aac20b4aacc
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/node-config-provider@npm:4.0.2"
  dependencies:
    "@smithy/property-provider": ^4.0.2
    "@smithy/shared-ini-file-loader": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 1a3b26835577e6c698a2ce59cd1dd9a3653c75e24847d35a45cd80124d72e0118b84daff47ee1ae0cdb89c134efdf7c7754d0ccf1e1c4b57467865b269b5cd0b
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/node-http-handler@npm:4.0.4"
  dependencies:
    "@smithy/abort-controller": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/querystring-builder": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: fb621c6ebcf012a99fc442d82965ca18d752f66be6f937a400e3b4e3feef1c259c028c27df9e78fc9ac7c40679b25276cbaa8d7ab82fd111bda64003ef831358
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/property-provider@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 6effc5ef7895eb4802c6b4c704d5616f50cd0c376da1644176d3aef71396cb65f9df20f4dd85c8301a9fa24f8ac53601e0634463f4364f0d867928efa5eb5e3d
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.1.0":
  version: 5.1.0
  resolution: "@smithy/protocol-http@npm:5.1.0"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: bb2f600853c0282630f5f32286a07a37294a57dbbec25ea0c6fbb6be32341b1be83e37933c2e3540e513c90dcb08f492bcb05980cde0b92b083e67ade6d56eb0
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/querystring-builder@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    "@smithy/util-uri-escape": ^4.0.0
    tslib: ^2.6.2
  checksum: 2ae27840e21982926182df809872e07d6b10b2fd93b58e02fa3f9588de23d333ddf02f0f3517de8a02a949489733bdcecb8c847980f8fb12ce1f8c3b6d127e86
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/querystring-parser@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: e6115fce0a07b1509f407cd3eca371cce1d9c09c7e3bd9156e35506b8ab1100f9864fb8779d4dbe0169501af23f062ebc2176afc012e9132e917781cd11a2f82
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/service-error-classification@npm:4.0.3"
  dependencies:
    "@smithy/types": ^4.2.0
  checksum: bc8a1239f2176fc0e980624e189871b309b0d61c5652022df236f34cd96e97f15719fd44c9d74cf2e1b632f5620a0fcccc6db77dbf9452bcec4e427456d96563
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 1e3d4921b6efbd1aa448a775dcb9a490d0221dd0a4fee434c5d83376de478013b3ad06d58a3d52db781124d4a53bd289fffcdb52eabffe9de152b0010332cee2
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.1.0":
  version: 5.1.0
  resolution: "@smithy/signature-v4@npm:5.1.0"
  dependencies:
    "@smithy/is-array-buffer": ^4.0.0
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    "@smithy/util-hex-encoding": ^4.0.0
    "@smithy/util-middleware": ^4.0.2
    "@smithy/util-uri-escape": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 7f3aed4999b47f04485846a90a08d0863c8bf4201a38616faf4bcb3166892a5b2946e7d0f1d5dc068b667913713873e21ab8374d60c1ff02828972d8c9201282
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.2.2":
  version: 4.2.2
  resolution: "@smithy/smithy-client@npm:4.2.2"
  dependencies:
    "@smithy/core": ^3.3.1
    "@smithy/middleware-endpoint": ^4.1.2
    "@smithy/middleware-stack": ^4.0.2
    "@smithy/protocol-http": ^5.1.0
    "@smithy/types": ^4.2.0
    "@smithy/util-stream": ^4.2.0
    tslib: ^2.6.2
  checksum: 78c3f90f042b8dfe82318dba554a1032ac01f289842d614ad4e462cb033fb85c2360fe26398aaa6c5b1f093717753a7039c745942f5f5333dbbc2400b8c76a42
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.2.0":
  version: 4.2.0
  resolution: "@smithy/types@npm:4.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: a8bd92c7e548bcbe7be211152de041ec164cfcc857d7574a87b1667c38827e5616563c13bd38a1d44b88bbfa3ee8f591dc597d4e2d50f3bc74e320ea82d7c49e
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/url-parser@npm:4.0.2"
  dependencies:
    "@smithy/querystring-parser": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 3da40fc18871c145bcbbb036a3d767ae113b954e94c745770f268dc877378cbafa6fc06759ea5a5e5c159a88e7331739b35b69f4d110ba0bd04b2d0923443f32
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: ad18ec66cc357c189eef358d96876b114faf7086b13e47e009b265d0ff80cec046052500489c183957b3a036768409acdd1a373e01074cc002ca6983f780cffc
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 574a10934024a86556e9dcde1a9776170284326c3dfcc034afa128cc5a33c1c8179fca9cfb622ef8be5f2004316cc3f427badccceb943e829105536ec26306d9
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: e91fd3816767606c5f786166ada26440457fceb60f96653b3d624dcf762a8c650e513c275ff3f647cb081c63c283cc178853a7ed9aa224abc8ece4eeeef7a1dd
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": ^2.2.0
    tslib: ^2.6.2
  checksum: 223d6a508b52ff236eea01cddc062b7652d859dd01d457a4e50365af3de1e24a05f756e19433f6ccf1538544076b4215469e21a4ea83dc1d58d829725b0dbc5a
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": ^4.0.0
    tslib: ^2.6.2
  checksum: be7cd33b6cb91503982b297716251e67cdca02819a15797632091cadab2dc0b4a147fff0709a0aa9bbc0b82a2644a7ed7c8afdd2194d5093cee2e9605b3a9f6f
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: cd9498d5f77a73aadd575084bcb22d2bb5945bac4605d605d36f2efe3f165f2b60f4dc88b7a62c2ed082ffa4b2c2f19621d0859f18399edbc2b5988d92e4649f
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.10":
  version: 4.0.10
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.10"
  dependencies:
    "@smithy/property-provider": ^4.0.2
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    bowser: ^2.11.0
    tslib: ^2.6.2
  checksum: 134d7a22cfeabf715217054c22f4f36ed42a7e2935d9e9767088fc684a1b9bce3c1ca511ba0f683761c106b4beb6e2da9cc35687974316116b31e959ca46ee12
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.10":
  version: 4.0.10
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.10"
  dependencies:
    "@smithy/config-resolver": ^4.1.0
    "@smithy/credential-provider-imds": ^4.0.2
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/property-provider": ^4.0.2
    "@smithy/smithy-client": ^4.2.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 9e099ed858696cdba80e44014359b94e4190f8d4e05939180094c26cdcdbe593eca998a161966a66e30c7873c449a33ba9be9dbe2a2282659da28bc490e3668c
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.2":
  version: 3.0.2
  resolution: "@smithy/util-endpoints@npm:3.0.2"
  dependencies:
    "@smithy/node-config-provider": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 5d2fe3956dc528842c071329bc69bd6567462858c1fbb1cc7ca19622227a803b54d95f44f3ac703852bce6349f455bfec599aea51df56d02e3c8b12e6481c27a
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 70dbb3aa1a79aff3329d07a66411ff26398df338bdd8a6d077b438231afe3dc86d9a7022204baddecd8bc633f059d5c841fa916d81dd7447ea79b64148f386d2
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.2":
  version: 4.0.2
  resolution: "@smithy/util-middleware@npm:4.0.2"
  dependencies:
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 18c3882c94f1b1bbb3825c30d1e41ae77a8da3dcd93ebbf1c486f34d5db9e06431789aef54d1b1fbb0424b115fc1e1ae17d27efe4af4277173d901a76147fef8
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/util-retry@npm:4.0.3"
  dependencies:
    "@smithy/service-error-classification": ^4.0.3
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: a00c47182efed65f3a86afc28fef69ae1e083965cc69f18bc82fb86e23179c3d083639c4819c97a625924b207dc1efaf7b68cf52e1c030f7c9a9625bbea215e0
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.2.0":
  version: 4.2.0
  resolution: "@smithy/util-stream@npm:4.2.0"
  dependencies:
    "@smithy/fetch-http-handler": ^5.0.2
    "@smithy/node-http-handler": ^4.0.4
    "@smithy/types": ^4.2.0
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-hex-encoding": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 52449a6ec68a483fdeef816128c923c744e278f6cf4d5b6fbe50e29fa8b6e5813df26221389f22bce143deb91f047ac56f87db85306908c5d0b87460e162bf63
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 23984624060756adba8aa4ab1693fe6b387ee5064d8ec4dfd39bb5908c4ee8b9c3f2dc755da9b07505d8e3ce1338c1867abfa74158931e4728bf3cfcf2c05c3d
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": ^2.2.0
    tslib: ^2.6.2
  checksum: e18840c58cc507ca57fdd624302aefd13337ee982754c9aa688463ffcae598c08461e8620e9852a424d662ffa948fc64919e852508028d09e89ced459bd506ab
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": ^4.0.0
    tslib: ^2.6.2
  checksum: 28a5a5372cbf0b3d2e32dd16f79b04c2aec6f704cf13789db922e9686fde38dde0171491cfa4c2c201595d54752a319faaeeed3c325329610887694431e28c98
  languageName: node
  linkType: hard

"@smithy/util-waiter@npm:^4.0.3":
  version: 4.0.3
  resolution: "@smithy/util-waiter@npm:4.0.3"
  dependencies:
    "@smithy/abort-controller": ^4.0.2
    "@smithy/types": ^4.2.0
    tslib: ^2.6.2
  checksum: 0ca992cd85719b367655943df08e8f7f0dd0f4ffe335809de7ed4c133ee2db5b58a2661cfc43040cf91512ef21783c8302fc2352f95910ecf3f0a50b0e32c2ff
  languageName: node
  linkType: hard

"@sqltools/formatter@npm:^1.2.5":
  version: 1.2.5
  resolution: "@sqltools/formatter@npm:1.2.5"
  checksum: 4b4fa62b8cd4880784b71cc5edd4a13da04fda0a915c14282765a8ec1a900a495e69b322704413e2052d221b5646d9fb0e20e87911f9a8f438f33180eecb11a4
  languageName: node
  linkType: hard

"@tokenizer/token@npm:^0.3.0":
  version: 0.3.0
  resolution: "@tokenizer/token@npm:0.3.0"
  checksum: 7ab9a822d4b5ff3f5bca7f7d14d46bdd8432528e028db4a52be7fbf90c7f495cc1af1324691dda2813c6af8dc4b8eb29de3107d4508165f9aa5b53e7d501f155
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 28a0710e5d039e0de484bdf85fee883bfd3f6a8980601f4d44066b0a6bcd821d31c4e231d1117731c4e24268bd4cf2a788a6787c12fc7f8d11014c07d582783c
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: dddca2b553e2bee1308a056705103fc8304e42bb2d2cbd797b84403a223b25c78f2c683ec3e24a095e82cd435387c877239bffcb15a590ba817cd3f6b9a99fd9
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 67c1316d065fdaa32525bc9449ff82c197c4c19092b9663b23213c8cbbf8d88b6ed6a17898e0cbc2711950fbfaf40388938c1c748a2ee89f7234fc9e7fe2bf44
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 05f8f2734e266fb1839eb1d57290df1664fe2aa3b0fdd685a9035806daa635f7519bf6d5d9b33f6e69dd545b8c46bd6e2b5c79acb2b1f146e885f7f11a42a5bb
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.8
  resolution: "@types/babel__generator@npm:7.6.8"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: f0ba105e7d2296bf367d6e055bb22996886c114261e2cb70bf9359556d0076c7a57239d019dee42bb063f565bade5ccb46009bce2044b2952d964bf9a454d6d2
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.6
  resolution: "@types/babel__traverse@npm:7.20.6"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: 7ba7db61a53e28cac955aa99af280d2600f15a8c056619c05b6fc911cbe02c61aa4f2823299221b23ce0cce00b294c0e5f618ec772aa3f247523c2e48cf7b888
  languageName: node
  linkType: hard

"@types/bcrypt@npm:^5.0.2":
  version: 5.0.2
  resolution: "@types/bcrypt@npm:5.0.2"
  dependencies:
    "@types/node": "*"
  checksum: dd7f05e183b9b1fc08ec499069febf197ab8e9c720766b5bbb5628395082e248f9a444c60882fe7788361fcadc302e21e055ab9c26a300f100e08791c353e6aa
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.5
  resolution: "@types/body-parser@npm:1.19.5"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: aebeb200f25e8818d8cf39cd0209026750d77c9b85381cdd8deeb50913e4d18a1ebe4b74ca9b0b4d21952511eeaba5e9fbbf739b52731a2061e206ec60d568df
  languageName: node
  linkType: hard

"@types/cls-hooked@npm:^4.3.3":
  version: 4.3.9
  resolution: "@types/cls-hooked@npm:4.3.9"
  dependencies:
    "@types/node": "*"
  checksum: fd531903b2cd7fa76d36ec81142ac55f0ea8702f6d76238a1ca5c773b8e35889cf528198a8a83b5e551f928f47bdfad947e29d9ebbe0df712cbf14510f7ae713
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "*"
  checksum: 2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/conventional-commits-parser@npm:^5.0.0":
  version: 5.0.1
  resolution: "@types/conventional-commits-parser@npm:5.0.1"
  dependencies:
    "@types/node": "*"
  checksum: 4b7b561f195f779d07f973801a9f15d77cd58ceb67e817459688b11cc735288d30de050f445c91f4cd2c007fa86824e59a6e3cde602d150b828c4474f6e67be5
  languageName: node
  linkType: hard

"@types/cookie-parser@npm:1.4.3":
  version: 1.4.3
  resolution: "@types/cookie-parser@npm:1.4.3"
  dependencies:
    "@types/express": "*"
  checksum: 7e4d92d48e05c8310e5fa0d3a386be626b68ef75b62dbaed2dedaa140b10dabbea2efe4c8e1f6b902e337f5f0fd3e17a788b0f357eccefac44eaaf2dcfbd46af
  languageName: node
  linkType: hard

"@types/cookiejar@npm:^2.1.5":
  version: 2.1.5
  resolution: "@types/cookiejar@npm:2.1.5"
  checksum: af38c3d84aebb3ccc6e46fb6afeeaac80fb26e63a487dd4db5a8b87e6ad3d4b845ba1116b2ae90d6f886290a36200fa433d8b1f6fe19c47da6b81872ce9a2764
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.3":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: a0ecbdf2f03912679440550817ff77ef39a30fa8bfdacaf6372b88b1f931828aec392f52283240f0d648cf3055c5ddc564544a626bcf245f3d09fcb099ebe3cc
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 69ba24fee600d1e4c5abe0df086c1a4d798abf13792d8cfab912d76817fe1a894359a1518557d21237fbaf6eda93c5ab9309143dee4c59ef54336d1b3570420e
  languageName: node
  linkType: hard

"@types/estree@npm:*":
  version: 1.0.6
  resolution: "@types/estree@npm:1.0.6"
  checksum: cdfd751f6f9065442cd40957c07fd80361c962869aa853c1c2fd03e101af8b9389d8ff4955a43a6fcfa223dd387a089937f95be0f3eec21ca527039fd2d9859a
  languageName: node
  linkType: hard

"@types/estree@npm:^0.0.51":
  version: 0.0.51
  resolution: "@types/estree@npm:0.0.51"
  checksum: a70c60d5e634e752fcd45b58c9c046ef22ad59ede4bc93ad5193c7e3b736ebd6bcd788ade59d9c3b7da6eeb0939235f011d4c59bb4fc04d8c346b76035099dd1
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
    "@types/send": "*"
  checksum: 4281f4ead71723f376b3ddf64868ae26244d434d9906c101cf8d436d4b5c779d01bd046e4ea0ed1a394d3e402216fabfa22b1fa4dba501061cd7c81c54045983
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^5.0.0":
  version: 5.0.6
  resolution: "@types/express-serve-static-core@npm:5.0.6"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
    "@types/send": "*"
  checksum: aced8cc88c1718adbbd1fc488756b0f22d763368d9eff2ae21b350698fab4a77d8d13c3699056dc662a887e43a8b67a3e8f6289ff76102ecc6bad4a7710d31a6
  languageName: node
  linkType: hard

"@types/express@npm:*":
  version: 5.0.0
  resolution: "@types/express@npm:5.0.0"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^5.0.0
    "@types/qs": "*"
    "@types/serve-static": "*"
  checksum: 0d74b53aefa69c3b3817ee9b5145fd50d7dbac52a8986afc2d7500085c446656d0b6dc13158c04e2d9f18f4324d4d93b0452337c5ff73dd086dca3e4ff11f47b
  languageName: node
  linkType: hard

"@types/express@npm:^4.17.13":
  version: 4.17.21
  resolution: "@types/express@npm:4.17.21"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^4.17.33
    "@types/qs": "*"
    "@types/serve-static": "*"
  checksum: 12e562c4571da50c7d239e117e688dc434db1bac8be55613294762f84fd77fbd0658ccd553c7d3ab02408f385bc93980992369dd30e2ecd2c68c358e6af8fabf
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "*"
  checksum: 235d2fc69741448e853333b7c3d1180a966dd2b8972c8cbcd6b2a0c6cd7f8d582ab2b8e58219dbc62cce8f1b40aa317ff78ea2201cdd8249da5025adebed6f0b
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.4
  resolution: "@types/http-errors@npm:2.0.4"
  checksum: 494670a57ad4062fee6c575047ad5782506dd35a6b9ed3894cea65830a94367bd84ba302eb3dde331871f6d70ca287bfedb1b2cf658e6132cd2cbd427ab56836
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 3948088654f3eeb45363f1db158354fb013b362dba2a5c2c18c559484d5eb9f6fd85b23d66c0a7c2fcfab7308d0a585b14dadaca6cc8bf89ebfdc7f8f5102fb7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
  checksum: 247e477bbc1a77248f3c6de5dadaae85ff86ac2d76c5fc6ab1776f54512a745ff2a5f791d22b942e3990ddbd40f3ef5289317c4fca5741bedfaa4f01df89051c
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "*"
  checksum: 1647fd402aced5b6edac87274af14ebd6b3a85447ef9ad11853a70fd92a98d35f81a5d3ea9fcb5dbb5834e800c6e35b64475e33fcae6bfa9acc70d61497c54ee
  languageName: node
  linkType: hard

"@types/jest@npm:28.1.4":
  version: 28.1.4
  resolution: "@types/jest@npm:28.1.4"
  dependencies:
    jest-matcher-utils: ^28.0.0
    pretty-format: ^28.0.0
  checksum: 0afa0bf0b2701a9122ba091bf2108cd23adf33ff620a18bb255385e028627fe18400c23d520c9c544fd066ad21caa580ac78dced950fc39e27e8db582a3fed21
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 6bf5337bc447b706bb5b4431d37686aa2ea6d07cfd6f79cc31de80170d6ff9b1c7384a9c0ccbc45b3f512bae9e9f75c2e12109806a15331dc94e8a8db6dbb4ac
  languageName: node
  linkType: hard

"@types/jsonwebtoken@npm:9.0.7":
  version: 9.0.7
  resolution: "@types/jsonwebtoken@npm:9.0.7"
  dependencies:
    "@types/node": "*"
  checksum: e1cd0e48fcae21b1d4378887a23453bd7212b480a131b11bcda2cdeb0687d03c9646ee5ba592e04cfaf76f7cc80f179950e627cdb3ebc90a5923bce49a35631a
  languageName: node
  linkType: hard

"@types/jsonwebtoken@npm:^9.0.9":
  version: 9.0.9
  resolution: "@types/jsonwebtoken@npm:9.0.9"
  dependencies:
    "@types/ms": "*"
    "@types/node": "*"
  checksum: d754a7b65fc021b298fc94e8d7a7d71f35dedf24296ac89286f80290abc5dbb0c7830a21440ee9ecbb340efc1b0a21f5609ea298a35b874cae5ad29a65440741
  languageName: node
  linkType: hard

"@types/lodash@npm:^4.14.191":
  version: 4.17.15
  resolution: "@types/lodash@npm:4.17.15"
  checksum: 2eb2dc6d231f5fb4603d176c08c8d7af688f574d09af47466a179cd7812d9f64144ba74bb32ca014570ffdc544eedc51b7a5657212bad083b6eecbd72223f9bb
  languageName: node
  linkType: hard

"@types/methods@npm:^1.1.4":
  version: 1.1.4
  resolution: "@types/methods@npm:1.1.4"
  checksum: a78534d79c300718298bfff92facd07bf38429c66191f640c1db4c9cff1e36f819304298a96f7536b6512bfc398e5c3e6b831405e138cd774b88ad7be78d682a
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: c2ee31cd9b993804df33a694d5aa3fa536511a49f2e06eeab0b484fef59b4483777dbb9e42a4198a0809ffbf698081fdbca1e5c2218b82b91603dfab10a10fbc
  languageName: node
  linkType: hard

"@types/minimist@npm:^1.2.0":
  version: 1.2.5
  resolution: "@types/minimist@npm:1.2.5"
  checksum: 3f791258d8e99a1d7d0ca2bda1ca6ea5a94e5e7b8fc6cde84dd79b0552da6fb68ade750f0e17718f6587783c24254bbca0357648dd59dc3812c150305cabdc46
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 5ce692ffe1549e1b827d99ef8ff71187457e0eb44adbae38fdf7b9a74bae8d20642ee963c14516db1d35fa2652e65f47680fdf679dcbde52bbfadd021f497225
  languageName: node
  linkType: hard

"@types/multer@npm:^1.4.12":
  version: 1.4.12
  resolution: "@types/multer@npm:1.4.12"
  dependencies:
    "@types/express": "*"
  checksum: 1250a32a66b7c5929cc5b005dae4ad0768ded9b6b52ba6f90931ff39b4ed775b09a0fd9d880f0836bcac16a9a7febed6a479c6bf108a52a244c4c53badb82a7b
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.13.4
  resolution: "@types/node@npm:22.13.4"
  dependencies:
    undici-types: ~6.20.0
  checksum: 3a234fa7766a3efc382cf81f66f474c26cdab2f54f43f757634c81c0444eb2160c2dabbde9741e4983078a318a88515b65416b5f1ab5478548579d7b3ead1d95
  languageName: node
  linkType: hard

"@types/node@npm:20.5.1":
  version: 20.5.1
  resolution: "@types/node@npm:20.5.1"
  checksum: b5aeaeb489842081190f8c2c09e923ff7b1b4ee3ecfceba12ba1030ce7750909a1b3c0f5372bd60cbe955e48a9889f416522e8a96697ad7209317752f395e3e5
  languageName: node
  linkType: hard

"@types/node@npm:^14.0.1":
  version: 14.18.63
  resolution: "@types/node@npm:14.18.63"
  checksum: 626a371419a6a0e11ca460b22bb4894abe5d75c303739588bc96267e380aa8b90ba5a87bc552400584f0ac2a84b5c458dadcbcf0dfd2396ebeb765f7a7f95893
  languageName: node
  linkType: hard

"@types/node@npm:^16.0.0":
  version: 16.18.126
  resolution: "@types/node@npm:16.18.126"
  checksum: 5c137eb141d33de32b16ff26047ff6d449432b58d0d25f7cced2040c97727d81fe1099a7581eb336d14a6840f5b09e363bdd43d7a6995e8e81eb47aa51e413fc
  languageName: node
  linkType: hard

"@types/nodemailer@npm:^6.4.17":
  version: 6.4.17
  resolution: "@types/nodemailer@npm:6.4.17"
  dependencies:
    "@types/node": "*"
  checksum: 689abb3005cf36cf89c2abe56f0aa4469a37e0814633a73fbeb35732e856f4b0d7ab32b6d91585038b6941f5b70db58ec2bd147ebe9f73e528eb6c99604f4e82
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: aef7bb9b015883d6f4119c423dd28c4bdc17b0e8a0ccf112c78b4fe0e91fbc4af7c6204b04bba0e199a57d2f3fbbd5b4a14bf8739bf9d2a39b2a0aad545e0f86
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: b1b863ac34a2c2172fbe0807a1ec4d5cb684e48d422d15ec95980b81475fac4fdb3768a8b13eef39130203a7c04340fc167bae057c7ebcafd7dec9fe6c36aeb1
  languageName: node
  linkType: hard

"@types/prettier@npm:^2.1.5":
  version: 2.7.3
  resolution: "@types/prettier@npm:2.7.3"
  checksum: 0960b5c1115bb25e979009d0b44c42cf3d792accf24085e4bfce15aef5794ea042e04e70c2139a2c3387f781f18c89b5706f000ddb089e9a4a2ccb7536a2c5f0
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.18
  resolution: "@types/qs@npm:6.9.18"
  checksum: 790b9091348e06dde2c8e4118b5771ab386a8c22a952139a2eb0675360a2070d0b155663bf6f75b23f258fd0a1f7ffc0ba0f059d99a719332c03c40d9e9cd63b
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 361bb3e964ec5133fa40644a0b942279ed5df1949f21321d77de79f48b728d39253e5ce0408c9c17e4e0fd95ca7899da36841686393b9f7a1e209916e9381a3c
  languageName: node
  linkType: hard

"@types/semver@npm:^7.3.12":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: 8663ff927234d1c5fcc04b33062cb2b9fcfbe0f5f351ed26c4d1e1581657deebd506b41ff7fdf89e787e3d33ce05854bc01686379b89e9c49b564c4cfa988efa
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.4
  resolution: "@types/send@npm:0.17.4"
  dependencies:
    "@types/mime": ^1
    "@types/node": "*"
  checksum: 7f17fa696cb83be0a104b04b424fdedc7eaba1c9a34b06027239aba513b398a0e2b7279778af521f516a397ced417c96960e5f50fcfce40c4bc4509fb1a5883c
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.7
  resolution: "@types/serve-static@npm:1.15.7"
  dependencies:
    "@types/http-errors": "*"
    "@types/node": "*"
    "@types/send": "*"
  checksum: 26ec864d3a626ea627f8b09c122b623499d2221bbf2f470127f4c9ebfe92bd8a6bb5157001372d4c4bd0dd37a1691620217d9dc4df5aa8f779f3fd996b1c60ae
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 1f4658385ae936330581bcb8aa3a066df03867d90281cdf89cc356d404bd6579be0f11902304e1f775d92df22c6dd761d4451c804b0a4fba973e06211e9bd77c
  languageName: node
  linkType: hard

"@types/superagent@npm:*":
  version: 8.1.9
  resolution: "@types/superagent@npm:8.1.9"
  dependencies:
    "@types/cookiejar": ^2.1.5
    "@types/methods": ^1.1.4
    "@types/node": "*"
    form-data: ^4.0.0
  checksum: 12631f1d8b3a62e1f435bc885f6d64d1a2d1ae82b80f0c6d63d4d6372c40b6f1fee6b3da59ac18bb86250b1eb73583bf2d4b1f7882048c32468791c560c69b7c
  languageName: node
  linkType: hard

"@types/supertest@npm:^2.0.11":
  version: 2.0.16
  resolution: "@types/supertest@npm:2.0.16"
  dependencies:
    "@types/superagent": "*"
  checksum: e1b4a4d788c19cd92a3f2e6d0979fb0f679c49aefae2011895a4d9c35aa960d43463aca8783a0b3382bbf0b4eb7ceaf8752d7dc80b8f5a9644fa14e1b1bdbc90
  languageName: node
  linkType: hard

"@types/validator@npm:^13.11.8":
  version: 13.12.2
  resolution: "@types/validator@npm:13.12.2"
  checksum: 64f1326c768947d756ab5bcd73f3f11a6f07dc76292aea83890d0390a9b9acb374f8df6b24af2c783271f276d3d613b78fc79491fe87edee62108d54be2e3c31
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: e71c3bd9d0b73ca82e10bee2064c384ab70f61034bbfb78e74f5206283fc16a6d85267b606b5c22cb2a3338373586786fed595b2009825d6a9115afba36560a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: d16937d7ac30dff697801c3d6f235be2166df42e4a88bf730fa6dc09201de3727c0a9500c59a672122313341de5f24e45ee0ff579c08ce91928e519090b7906b
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.0.0":
  version: 5.62.0
  resolution: "@typescript-eslint/eslint-plugin@npm:5.62.0"
  dependencies:
    "@eslint-community/regexpp": ^4.4.0
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/type-utils": 5.62.0
    "@typescript-eslint/utils": 5.62.0
    debug: ^4.3.4
    graphemer: ^1.4.0
    ignore: ^5.2.0
    natural-compare-lite: ^1.4.0
    semver: ^7.3.7
    tsutils: ^3.21.0
  peerDependencies:
    "@typescript-eslint/parser": ^5.0.0
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3f40cb6bab5a2833c3544e4621b9fdacd8ea53420cadc1c63fac3b89cdf5c62be1e6b7bcf56976dede5db4c43830de298ced3db60b5494a3b961ca1b4bff9f2a
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.0.0":
  version: 5.62.0
  resolution: "@typescript-eslint/parser@npm:5.62.0"
  dependencies:
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/typescript-estree": 5.62.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 315194b3bf39beb9bd16c190956c46beec64b8371e18d6bb72002108b250983eb1e186a01d34b77eb4045f4941acbb243b16155fbb46881105f65e37dc9e24d4
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/scope-manager@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
  checksum: 861253235576c1c5c1772d23cdce1418c2da2618a479a7de4f6114a12a7ca853011a1e530525d0931c355a8fd237b9cd828fac560f85f9623e24054fd024726f
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/type-utils@npm:5.62.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 5.62.0
    "@typescript-eslint/utils": 5.62.0
    debug: ^4.3.4
    tsutils: ^3.21.0
  peerDependencies:
    eslint: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 93112e34026069a48f0484b98caca1c89d9707842afe14e08e7390af51cdde87378df29d213d3bbd10a7cfe6f91b228031b56218515ce077bdb62ddea9d9f474
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/types@npm:5.62.0"
  checksum: 7febd3a7f0701c0b927e094f02e82d8ee2cada2b186fcb938bc2b94ff6fbad88237afc304cbaf33e82797078bbbb1baf91475f6400912f8b64c89be79bfa4ddf
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/typescript-estree@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    semver: ^7.3.7
    tsutils: ^3.21.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: d7984a3e9d56897b2481940ec803cb8e7ead03df8d9cfd9797350be82ff765dfcf3cfec04e7355e1779e948da8f02bc5e11719d07a596eb1cb995c48a95e38cf
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/utils@npm:5.62.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@types/json-schema": ^7.0.9
    "@types/semver": ^7.3.12
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/typescript-estree": 5.62.0
    eslint-scope: ^5.1.1
    semver: ^7.3.7
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: f09b7d9952e4a205eb1ced31d7684dd55cee40bf8c2d78e923aa8a255318d97279825733902742c09d8690f37a50243f4c4d383ab16bd7aefaf9c4b438f785e1
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/visitor-keys@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    eslint-visitor-keys: ^3.3.0
  checksum: 7c3b8e4148e9b94d9b7162a596a1260d7a3efc4e65199693b8025c71c4652b8042501c0bc9f57654c1e2943c26da98c0f77884a746c6ae81389fcb0b513d995d
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/ast@npm:1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
  checksum: 6f75b09f17a29e704d2343967c53128cda7c84af2d192a3146de1b53cafaedfe568eca0804bd6c1acc72e1269477ae22d772de1dcf605cdb0adf9768f31d88d7
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.11.1"
  checksum: 9644d9f7163d25aa301cf3be246e35cca9c472b70feda0593b1a43f30525c68d70bfb4b7f24624cd8e259579f1dee32ef28670adaeb3ab1314ffb52a25b831d5
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-api-error@npm:1.11.1"
  checksum: 23e6f24100eb21779cd4dcc7c4231fd511622545a7638b195098bcfee79decb54a7e2b3295a12056c3042af7a5d8d62d4023a9194c9cba0311acb304ea20a292
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.11.1"
  checksum: ab662fc94a017538c538836387492567ed9f23fe4485a86de1834d61834e4327c24659830e1ecd2eea7690ce031a148b59c4724873dc5d3c0bdb71605c7d01af
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-numbers@npm:1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.11.1
    "@webassemblyjs/helper-api-error": 1.11.1
    "@xtuc/long": 4.2.2
  checksum: 8cc7ced66dad8f968a68fbad551ba50562993cefa1add67b31ca6462bb986f7b21b5d7c6444c05dd39312126e10ac48def025dec6277ce0734665191e05acde7
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.11.1"
  checksum: f14e2bd836fed1420fe7507919767de16346a013bbac97b6b6794993594f37b5f0591d824866a7b32f47524cef8a4a300e5f914952ff2b0ff28659714400c793
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
  checksum: e2da4192a843e96c8bf5156cea23193c9dbe12a1440c9c109d3393828f46753faab75fac78ecfe965aa7988723ad9b0b12f3ca0b9e4de75294980e67515460af
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/ieee754@npm:1.11.1"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 13d6a6ca2e9f35265f10b549cb8354f31a307a7480bbf76c0f4bc8b02e13d5556fb29456cef3815db490effc602c59f98cb0505090ca9e29d7dc61539762a065
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/leb128@npm:1.11.1"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: e505edb5de61f13c6c66c57380ae16e95db9d7c43a41ac132e298426bcead9c90622e3d3035fb63df09d0eeabafd471be35ba583fca72ac2e776ab537dda6883
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/utf8@npm:1.11.1"
  checksum: a7c13c7c82d525fe774f51a4fc1da058b0e2c73345eed9e2d6fbeb96ba50c1942daf97e0ff394e7a4d0f26b705f9587cb14681870086d51f02abc78ff6ce3703
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/helper-wasm-section": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
    "@webassemblyjs/wasm-opt": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    "@webassemblyjs/wast-printer": 1.11.1
  checksum: 10bef22579f96f8c0934aa9fbf6f0d9110563f9c1a510100a84fdfa3dbd9126fdc10bfc12e7ce3ace0ba081e6789eac533c81698faab75859b3a41e97b5ab3bc
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/ieee754": 1.11.1
    "@webassemblyjs/leb128": 1.11.1
    "@webassemblyjs/utf8": 1.11.1
  checksum: 4e49a19e302e19a2a2438e87ae85805acf39a7d93f9ac0ab65620ae395894937ceb762fa328acbe259d2e60d252cbb87a40ec2b4c088f3149be23fa69ddbf855
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
  checksum: af7fd6bcb942baafda3b8cc1e574062d01c582aaa12d4f0ea62ff8e83ce1317f06a79c16313a3bc98625e1226d0fc49ba90edac18c21a64c75e9cd114306f07a
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-api-error": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/ieee754": 1.11.1
    "@webassemblyjs/leb128": 1.11.1
    "@webassemblyjs/utf8": 1.11.1
  checksum: 5a7e8ad36176347f3bc9aee15860a7002b608c181012128ea3e5a1199649d6722e05e029fdf2a73485f2ab3e2f7386b3e0dce46ff9cfd1918417a4ee1151f21e
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wast-printer@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@xtuc/long": 4.2.2
  checksum: cede13c53a176198f949e7f0edf921047c524472b2e4c99edfe829d20e168b4037395479325635b4a3662ea7b4b59be4555ea3bb6050c61b823c68abdb435c74
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: a8565d29d135039bd99ae4b2220d3e167d22cf53f867e491ed479b3f84f895742d0097f935b19aab90265a23d5d46711e4204f14c479ae3637fbf06c4666882f
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8582cbc69c79ad2d31568c412129bf23d2b1210a1dfb60c82d5a1df93334da4ee51f3057051658569e2c196d8dc33bc05ae6b974a711d0d16e801e1d0647ccd1
  languageName: node
  linkType: hard

"JSONStream@npm:^1.3.5":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: ^1.2.0
    through: ">=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 0f54694da32224d57b715385d4a6b668d2117379d1f3223dc758459246cca58fdc4c628b83e8a8883334e454a0a30aa198ede77c788b55537c1844f686a751f2
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: 3f762677702acb24f65e813070e306c61fafe25d4b2583f9dfc935131f774863f3addd5741572ed576bd69cabe473c5af18e1e108b829cb7b6b4747884f726e6
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 049704186396f571650eb7b22ed3627b77a5aedf98bb83caf2eac81ca2a3e25e795394b0464cfb2d6076df3db6a5312139eac5b6a126ca296ac53c5008069c28
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-import-assertions@npm:^1.7.6":
  version: 1.9.0
  resolution: "acorn-import-assertions@npm:1.9.0"
  peerDependencies:
    acorn: ^8
  checksum: 3b4a194e128efdc9b86c2b1544f623aba4c1aa70d638f8ab7dc3971a5b4aa4c57bd62f99af6e5325bb5973c55863b4112e708a6f408bad7a138647ca72283afe
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: ^8.11.0
  checksum: 76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.4.1, acorn@npm:^8.7.1, acorn@npm:^8.8.2, acorn@npm:^8.9.0":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 6d4ee461a7734b2f48836ee0fbb752903606e576cc100eb49340295129ca0b452f3ba91ddd4424a1d4406a98adfb2ebb6bd0ff4c49d7a0930c10e462719bbfd7
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: a42f67faa79e3e6687a4923050e7c9807db3848a037076f791d10e092677d65c1d2d863b7848560699f40fc0502c19f40963fb1cd1fb3d338a7423df8e45e039
  languageName: node
  linkType: hard

"ajv-formats@npm:2.1.1, ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: e43ba22e91b6a48d96224b83d260d3a3a561b42d391f8d3c6d2c1559f9aa5b253bfb306bc94bbeca1d967c014e15a6efe9a207309e95b3eaae07fcbcdc2af662
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 0c57a47cbd656e8cdfd99d7c2264de5868918ffa207c8d7a72a7f63379d4333254b2ba03d69e3c035e996a3fd3eb6d5725d7a1597cca10694296e32510546360
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: 18bec51f0171b83123ba1d8883c126e60c6f420cef885250898bf77a8d3e65e3bfb9e8564f497e30bdbe762a83e0d144a36931328616a973ee669dc74d4a9590
  languageName: node
  linkType: hard

"ajv@npm:8.12.0":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: ^3.1.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
    uri-js: ^4.2.2
  checksum: ac4f72adf727ee425e049bc9d8b31d4a57e1c90da8d28bcd23d60781b12fcd6fc3d68db5df16994c57b78b94eed7988f5a6b482fd376dc5b084125e20a0a622e
  languageName: node
  linkType: hard

"ajv@npm:8.9.0":
  version: 8.9.0
  resolution: "ajv@npm:8.9.0"
  dependencies:
    fast-deep-equal: ^3.1.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
    uri-js: ^4.2.2
  checksum: 791cf7c386abcf90fd988f7cfa1d1455a74ed178f9c53ad1621375e6752d88de6a5b447efd93107bc68b2578d4ac4fee29d8fefc1724b6324d8ac3987ca64a3f
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.11.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: ec3ba10a573c6b60f94639ffc53526275917a2df6810e4ab5a6b959d87459f9ef3f00d5e7865b82677cb7d21590355b34da14d1d0b9c32d75f95a187e76fff35
  languageName: node
  linkType: hard

"ansi-colors@npm:4.1.1":
  version: 4.1.1
  resolution: "ansi-colors@npm:4.1.1"
  checksum: 6086ade4336b4250b6b25e144b83e5623bcaf654d3df0c3546ce09c9c5ff999cb6a6f00c87e802d05cf98aef79d92dc76ade2670a2493b8dcb80220bec457838
  languageName: node
  linkType: hard

"ansi-colors@npm:4.1.3":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: ec87a2f59902f74e61eada7f6e6fe20094a628dab765cfdbd03c3477599368768cffccdb5d3bb19a1b6c99126783a143b1fee31aab729b31ffe5836c7e5e28b9
  languageName: node
  linkType: hard

"ansi-escapes@npm:^3.2.0":
  version: 3.2.0
  resolution: "ansi-escapes@npm:3.2.0"
  checksum: 084e1ce38139ad2406f18a8e7efe2b850ddd06ce3c00f633392d1ce67756dab44fe290e573d09ef3c9a0cb13c12881e0e35a8f77a017d39a0a4ab85ae2fae04f
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1, ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-regex@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-regex@npm:3.0.1"
  checksum: d108a7498b8568caf4a46eea4f1784ab4e0dfb2e3f3938c697dee21443d622d765c958f2b7e2b9f6b9e55e2e2af0584eaa9915d51782b89a841c28e744e7a167
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: d36d34234d077e8770169d980fed7b2f3724bfa2a01da150ccd75ef9707c80e883d27cdf7a0eac2f145ac1d10a785a8a855cffd05b85f778629a0db62e7033da
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: ece5a8ef069fcc5298f67e3f4771a663129abd174ea2dfa87923a2be2abf6cd367ef72ac87942da00ce85bd1d651d4cd8595aebdb1b385889b89b205860e977b
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.0.0, ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 60f0298ed34c74fef50daab88e8dab786036ed5a7fad02e012ab57e376e0a0b4b29e83b95ea9b5e7d89df762f5f25119b83e00706ecaccb22cfbacee98d74889
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"app-root-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "app-root-path@npm:3.1.0"
  checksum: 4a0fd976de1bffcdb18a5e1f8050091f15d0780e0582bca99aaa9d52de71f0e08e5185355fcffc781180bfb898499e787a2f5ed79b9c448b942b31dc947acaa9
  languageName: node
  linkType: hard

"append-field@npm:^1.0.0":
  version: 1.0.0
  resolution: "append-field@npm:1.0.0"
  checksum: 1b5abcc227e5179936a9e4f7e2af4769fa1f00eda85bbaed907f7964b0fd1f7d61f0f332b35337f391389ff13dd5310c2546ba670f8e5a743b23ec85185c73ef
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: d06e26384a8f6245d8c8896e138c0388824e259a329e0c9f196b4fa533c82502a6fd449586e3604950a0c42921832a458bb3aa0aa9f0ba449cfd4f50fd0d09b5
  languageName: node
  linkType: hard

"archiver-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "archiver-utils@npm:2.1.0"
  dependencies:
    glob: ^7.1.4
    graceful-fs: ^4.2.0
    lazystream: ^1.0.0
    lodash.defaults: ^4.2.0
    lodash.difference: ^4.5.0
    lodash.flatten: ^4.4.0
    lodash.isplainobject: ^4.0.6
    lodash.union: ^4.6.0
    normalize-path: ^3.0.0
    readable-stream: ^2.0.0
  checksum: 6ea5b02e440f3099aff58b18dd384f84ecfe18632e81d26c1011fe7dfdb80ade43d7a06cbf048ef0e9ee0f2c87a80cb24c0f0ac5e3a2c4d67641d6f0d6e36ece
  languageName: node
  linkType: hard

"archiver-utils@npm:^3.0.4":
  version: 3.0.4
  resolution: "archiver-utils@npm:3.0.4"
  dependencies:
    glob: ^7.2.3
    graceful-fs: ^4.2.0
    lazystream: ^1.0.0
    lodash.defaults: ^4.2.0
    lodash.difference: ^4.5.0
    lodash.flatten: ^4.4.0
    lodash.isplainobject: ^4.0.6
    lodash.union: ^4.6.0
    normalize-path: ^3.0.0
    readable-stream: ^3.6.0
  checksum: 9bb7e271e95ff33bdbdcd6f69f8860e0aeed3fcba352a74f51a626d1c32b404f20e3185d5214f171b24a692471d01702f43874d1a4f0d2e5f57bd0834bc54c14
  languageName: node
  linkType: hard

"archiver@npm:^5.0.0":
  version: 5.3.2
  resolution: "archiver@npm:5.3.2"
  dependencies:
    archiver-utils: ^2.1.0
    async: ^3.2.4
    buffer-crc32: ^0.2.1
    readable-stream: ^3.6.0
    readdir-glob: ^1.1.2
    tar-stream: ^2.2.0
    zip-stream: ^4.1.0
  checksum: 973384d749b3fa96f44ceda1603a65aaa3f24a267230d69a4df9d7b607d38d3ebc6c18c358af76eb06345b6b331ccb9eca07bd079430226b5afce95de22dfade
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^2.0.0":
  version: 2.0.0
  resolution: "are-we-there-yet@npm:2.0.0"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 375f753c10329153c8d66dc95e8f8b6c7cc2aa66e05cb0960bd69092b10dae22900cacc7d653ad11d26b3ecbdbfe1e8bfb6ccf0265ba8077a7d979970f16b99c
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 070ff801a9d236a6caa647507bdcc7034530604844d64408149a26b9e87c2f97650055c0f049abd1efc024b334635c01f29e0b632b371ac3f26130f4cf65997a
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"arr-diff@npm:^4.0.0":
  version: 4.0.0
  resolution: "arr-diff@npm:4.0.0"
  checksum: 67b80067137f70c89953b95f5c6279ad379c3ee39f7143578e13bd51580a40066ee2a55da066e22d498dce10f68c2d70056d7823f972fab99dfbf4c78d0bc0f7
  languageName: node
  linkType: hard

"arr-flatten@npm:^1.1.0":
  version: 1.1.0
  resolution: "arr-flatten@npm:1.1.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"arr-union@npm:^3.1.0":
  version: 3.1.0
  resolution: "arr-union@npm:3.1.0"
  checksum: 7d5aa05894e54aa93c77c5726c1dd5d8e8d3afe4f77983c0aa8a14a8a5cbe8b18f0cf4ecaa4ac8c908ef5f744d2cbbdaa83fd6e96724d15fea56cfa7f5efdd51
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-ify@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-ify@npm:1.0.0"
  checksum: 75c9c072faac47bd61779c0c595e912fe660d338504ac70d10e39e1b8a4a0c9c87658703d619b9d1b70d324177ae29dc8d07dda0d0a15d005597bc4c5a59c70c
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"array-unique@npm:^0.3.2":
  version: 0.3.2
  resolution: "array-unique@npm:0.3.2"
  checksum: dbf4462cdba8a4b85577be07705210b3d35be4b765822a3f52962d907186617638ce15e0603a4fefdcf82f4cbbc9d433f8cbbd6855148a68872fa041b6474121
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: c35c8d1a81bcd5474c0c57fe3f4bad1a4d46a5fa353cedcff7a54da315df60db71829e69104b859dff96c5d68af46bd2be259fe5e50dc6aa9df3b36bea0383ab
  languageName: node
  linkType: hard

"asap@npm:^2.0.0":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"assign-symbols@npm:^1.0.0":
  version: 1.0.0
  resolution: "assign-symbols@npm:1.0.0"
  checksum: 29a654b8a6da6889a190d0d0efef4b1bfb5948fa06cbc245054aef05139f889f2f7c75b989917e3fde853fc4093b88048e4de8578a73a76f113d41bfd66e5775
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: f63d439cc383db1b9c5c6080d1e240bd14dae745f15d11ec5da863e182bbeca70df6c8191cffef5deba0b566ef98834610a68be79ac6379c95eeb26e1b310e25
  languageName: node
  linkType: hard

"async-hook-jl@npm:^1.7.6":
  version: 1.7.6
  resolution: "async-hook-jl@npm:1.7.6"
  dependencies:
    stack-chain: ^1.3.7
  checksum: 4bd9aee5181fef66e58711c615f9be1e4575848c977ed1a5eff62046b64a6ff5641951a0ea70344c2026d7944674a6e8c1d888f9865e421e9587610e7a3be301
  languageName: node
  linkType: hard

"async@npm:^3.2.4":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 4c058baf6df1bc5a1697cf182e2029c58cd99975288a13f9e70068ef5d6f4e1f1fd7c4d2c3c4912eae44797d1725be9700995736deca441b39f3e66d8dee97ef
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: ada635b519dc0c576bb0b3ca63a73b50eefacf390abb3f062558342a8d68f2db91d0c8db54ce81b0d89de3b0f000de71f3ae7d761fd7d8cc624278fe443d6c7e
  languageName: node
  linkType: hard

"aws-ssl-profiles@npm:^1.1.1":
  version: 1.1.2
  resolution: "aws-ssl-profiles@npm:1.1.2"
  checksum: e5f59a4146fe3b88ad2a84f814886c788557b80b744c8cbcb1cbf8cf5ba19cc006a7a12e88819adc614ecda9233993f8f1d1f3b612cbc2f297196df9e8f4f66e
  languageName: node
  linkType: hard

"axios@npm:^1.8.4":
  version: 1.8.4
  resolution: "axios@npm:1.8.4"
  dependencies:
    follow-redirects: ^1.15.6
    form-data: ^4.0.0
    proxy-from-env: ^1.1.0
  checksum: 450993c2ba975ffccaf0d480b68839a3b2435a5469a71fa2fb0b8a55cdb2c2ae47e609360b9c1e2b2534b73dfd69e2733a1cf9f8215bee0bcd729b72f801b0ce
  languageName: node
  linkType: hard

"babel-jest@npm:^28.1.3":
  version: 28.1.3
  resolution: "babel-jest@npm:28.1.3"
  dependencies:
    "@jest/transform": ^28.1.3
    "@types/babel__core": ^7.1.14
    babel-plugin-istanbul: ^6.1.1
    babel-preset-jest: ^28.1.3
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    slash: ^3.0.0
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 612a6317c176d2d890d9e7c5fc1379a6b2aca784522c1242db9dbcc6e18f2cdaa793e3d649346d37333576b37953fadd53a415787e32ec0fac8b79c35aaafd11
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@istanbuljs/load-nyc-config": ^1.0.0
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-instrument: ^5.0.4
    test-exclude: ^6.0.0
  checksum: 1075657feb705e00fd9463b329921856d3775d9867c5054b449317d39153f8fbcebd3e02ebf00432824e647faff3683a9ca0a941325ef1afe9b3c4dd51b24beb
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^28.1.3":
  version: 28.1.3
  resolution: "babel-plugin-jest-hoist@npm:28.1.3"
  dependencies:
    "@babel/template": ^7.3.3
    "@babel/types": ^7.3.3
    "@types/babel__core": ^7.1.14
    "@types/babel__traverse": ^7.0.6
  checksum: 4a47f1673bdfcc15b0968d5577119b1abc6eb199a2d627be56c60872fba7b65455cbc7d631896d33e6ec27831bf43600a1d66616d3bc3a37a8784c1596339eeb
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-bigint": ^7.8.3
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-import-attributes": ^7.24.7
    "@babel/plugin-syntax-import-meta": ^7.10.4
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 0b838d4412e3322cb4436f246e24e9c00bebcedfd8f00a2f51489db683bd35406bbd55a700759c28d26959c6e03f84dd6a1426f576f440267c1d7a73c5717281
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^28.1.3":
  version: 28.1.3
  resolution: "babel-preset-jest@npm:28.1.3"
  dependencies:
    babel-plugin-jest-hoist: ^28.1.3
    babel-preset-current-node-syntax: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: b30f4102012f9474be4649ea8dba848614ae995418173c5d4a0e606785f03320aea1e8889b5f163f0336c06d5901100b47cd77a45c54fcbf149ff06ad4fa907c
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.0, base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"base@npm:^0.11.1":
  version: 0.11.2
  resolution: "base@npm:0.11.2"
  dependencies:
    cache-base: ^1.0.1
    class-utils: ^0.3.5
    component-emitter: ^1.2.1
    define-property: ^1.0.0
    isobject: ^3.0.1
    mixin-deep: ^1.2.0
    pascalcase: ^0.1.1
  checksum: 30a2c0675eb52136b05ef496feb41574d9f0bb2d6d677761da579c00a841523fccf07f1dbabec2337b5f5750f428683b8ca60d89e56a1052c4ae1c0cd05de64d
  languageName: node
  linkType: hard

"bcrypt@npm:^5.1.1":
  version: 5.1.1
  resolution: "bcrypt@npm:5.1.1"
  dependencies:
    "@mapbox/node-pre-gyp": ^1.0.11
    node-addon-api: ^5.0.0
  checksum: 743231158c866bddc46f25eb8e9617fe38bc1a6f5f3052aba35e361d349b7f8fb80e96b45c48a4c23c45c29967ccd11c81cf31166454fc0ab019801c336cab40
  languageName: node
  linkType: hard

"big-integer@npm:^1.6.17":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 9604224b4c2ab3c43c075d92da15863077a9f59e5d4205f4e7e76acd0cd47e8d469ec5e5dba8d9b32aa233951893b29329ca56ac80c20ce094b4a647a66abae0
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.1.2
  resolution: "bignumber.js@npm:9.1.2"
  checksum: e17786545433f3110b868725c449fa9625366a6e675cd70eb39b60938d6adbd0158cb4b3ad4f306ce817165d37e63f4aa3098ba4110db1d9a3b9f66abfbaf10d
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"binary@npm:~0.3.0":
  version: 0.3.0
  resolution: "binary@npm:0.3.0"
  dependencies:
    buffers: ~0.1.1
    chainsaw: ~0.1.0
  checksum: 752c2c2ff9f23506b3428cc8accbfcc92fec07bf8a31a1953e9c7e2193eb5db8a67252034ab93e8adab2a1c43f3eeb3da0bacae0320e9814f3ca127942c55871
  languageName: node
  linkType: hard

"bl@npm:^4.0.3, bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 02847e1d2cb089c9dc6958add42e3cdeaf07d13f575973963335ac0fdece563a50ac770ac4c8fa06492d2dd276f6cc3b7f08c7cd9c7a7ad0f8d388b2a28def5f
  languageName: node
  linkType: hard

"bluebird@npm:~3.4.1":
  version: 3.4.7
  resolution: "bluebird@npm:3.4.7"
  checksum: ac7e3df09a433b985a0ba61a0be4fc23e3874bf62440ffbca2f275a8498b00c11336f1f633631f38419b2c842515473985f9c4aaa9e4c9b36105535026d94144
  languageName: node
  linkType: hard

"body-parser@npm:1.20.1":
  version: 1.20.1
  resolution: "body-parser@npm:1.20.1"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.4
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.11.0
    raw-body: 2.5.1
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: a202d493e2c10a33fb7413dac7d2f713be579c4b88343cd814b6df7a38e5af1901fc31044e04de176db56b16d9772aa25a7723f64478c20f4d91b1ac223bf3b8
  languageName: node
  linkType: hard

"body-parser@npm:1.20.2":
  version: 1.20.2
  resolution: "body-parser@npm:1.20.2"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.5
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.11.0
    raw-body: 2.5.2
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: 06f1438fff388a2e2354c96aa3ea8147b79bfcb1262dfcc2aae68ec13723d01d5781680657b74e9f83c808266d5baf52804032fbde2b7382b89bd8cdb273ace9
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 04efeecc7927a9ec33c667fa0965dea19f4ac60b3fea60793c2e6cf06c1dcd2f7ae1dbc656f450c5f50783b1c75cf9dc173ba6f3b7db2feee01f8c4b793e1bd3
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: 695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^2.3.1":
  version: 2.3.2
  resolution: "braces@npm:2.3.2"
  dependencies:
    arr-flatten: ^1.1.0
    array-unique: ^0.3.2
    extend-shallow: ^2.0.1
    fill-range: ^4.0.0
    isobject: ^3.0.1
    repeat-element: ^1.1.2
    snapdragon: ^0.8.1
    snapdragon-node: ^2.0.1
    split-string: ^3.0.2
    to-regex: ^3.0.1
  checksum: 72b27ea3ea2718f061c29e70fd6e17606e37c65f5801abddcf0b0052db1de7d60f3bf92cfc220ab57b44bd0083a5f69f9d03b3461d2816cfe9f9398207acc728
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: 7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist@npm:^4.14.5, browserslist@npm:^4.24.0":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: ^1.0.30001688
    electron-to-chromium: ^1.5.73
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.1
  bin:
    browserslist: cli.js
  checksum: db7ebc1733cf471e0b490b4f47e3e2ea2947ce417192c9246644e92c667dd56a71406cc58f62ca7587caf828364892e9952904a02b7aead752bc65b62a37cfe9
  languageName: node
  linkType: hard

"bs-logger@npm:0.x":
  version: 0.2.6
  resolution: "bs-logger@npm:0.2.6"
  dependencies:
    fast-json-stable-stringify: 2.x
  checksum: 80e89aaaed4b68e3374ce936f2eb097456a0dddbf11f75238dbd53140b1e39259f0d248a5089ed456f1158984f22191c3658d54a713982f676709fbe1a6fa5a0
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: ^0.4.0
  checksum: 24d8dfb7b6d457d73f32744e678a60cc553e4ec0e9e1a01cf614b44d85c3c87e188d3cc78ef0442ce5032ee6818de20a0162ba1074725c0d08908f62ea979227
  languageName: node
  linkType: hard

"buffer-crc32@npm:^0.2.1, buffer-crc32@npm:^0.2.13":
  version: 0.2.13
  resolution: "buffer-crc32@npm:0.2.13"
  checksum: cb0a8ddf5cf4f766466db63279e47761eb825693eeba6a5a95ee4ec8cb8f81ede70aa7f9d8aeec083e781d47154290eb5d4d26b3f7a465ec57fb9e7d59c47150
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: fb2294e64d23c573d0dd1f1e7a466c3e978fe94a4e0f8183937912ca374619773bef8e2aceb854129d2efecbbc515bbd0cc78d2734a3e3031edb0888531bbc8e
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer-indexof-polyfill@npm:~1.0.0":
  version: 1.0.2
  resolution: "buffer-indexof-polyfill@npm:1.0.2"
  checksum: b8376d5f8b2c230d02fce36762b149b6c436aa03aca5e02b934ea13ce72a7e731c785fa30fb30e9c713df5173b4f8e89856574e70ce86b2f1d94d7d90166eab0
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: 27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.2.1
  checksum: 2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"buffers@npm:~0.1.1":
  version: 0.1.1
  resolution: "buffers@npm:0.1.1"
  checksum: c7a3284ddb4f5c65431508be65535e3739215f7996aa03e5d3a3fcf03144d35ffca7d9825572e6c6c6007f5308b8553c7b2941fcf5e56fac20dedea7178f5f71
  languageName: node
  linkType: hard

"busboy@npm:^1.0.0, busboy@npm:^1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: fa7e836a2b82699b6e074393428b91ae579d4f9e21f5ac468e1b459a244341d722d2d22d10920cdd849743dbece6dca11d72de939fb75a7448825cf2babfba1f
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: 01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"cache-base@npm:^1.0.1":
  version: 1.0.1
  resolution: "cache-base@npm:1.0.1"
  dependencies:
    collection-visit: ^1.0.0
    component-emitter: ^1.2.1
    get-value: ^2.0.6
    has-value: ^1.0.0
    isobject: ^3.0.1
    set-value: ^2.0.0
    to-object-path: ^0.3.0
    union-value: ^1.0.0
    unset-value: ^1.0.0
  checksum: a7142e25c73f767fa520957dcd179b900b86eac63b8cfeaa3b2a35e18c9ca5968aa4e2d2bed7a3e7efd10f13be404344cfab3a4156217e71f9bdb95940bb9c8c
  languageName: node
  linkType: hard

"cachedir@npm:2.2.0":
  version: 2.2.0
  resolution: "cachedir@npm:2.2.0"
  checksum: dcfe95858e854e593686e106482cc7da62ae1714b9e31ec6d4af40b1615c6e67c5bdad5ef284d9a6cc6ce2e124ff27beb125fc538c0a5db32ff9c2c0e065806c
  languageName: node
  linkType: hard

"cachedir@npm:2.3.0":
  version: 2.3.0
  resolution: "cachedir@npm:2.3.0"
  checksum: 8380a4a4aa824b20cbc246c38ae2b3379a865f52ea1f31f7b057d07545ea1ab27f93c4323d4bd1bd398991489f18a226880c3166b19ecbf49a77b18c519d075a
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: 47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    get-intrinsic: ^1.2.6
  checksum: 45257b8e7621067304b30dbd638e856cac913d31e8e00a80d6cf172911acd057846572d0b256b45e652d515db6601e2974a1b1a040e91b4fc36fb3dd86fa69cf
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase-keys@npm:^6.2.2":
  version: 6.2.2
  resolution: "camelcase-keys@npm:6.2.2"
  dependencies:
    camelcase: ^5.3.1
    map-obj: ^4.0.0
    quick-lru: ^4.0.1
  checksum: bf1a28348c0f285c6c6f68fb98a9d088d3c0269fed0cdff3ea680d5a42df8a067b4de374e7a33e619eb9d5266a448fe66c2dd1f8e0c9209ebc348632882a3526
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001700
  resolution: "caniuse-lite@npm:1.0.30001700"
  checksum: 3d391bcdd193208166d3ad759de240b9c18ac3759dbd57195770f0fcd2eedcd47d5e853609aba1eee5a2def44b0a14eee457796bdb3451a27de0c8b27355017c
  languageName: node
  linkType: hard

"chainsaw@npm:~0.1.0":
  version: 0.1.0
  resolution: "chainsaw@npm:0.1.0"
  dependencies:
    traverse: ">=0.3.0 <0.4"
  checksum: c27b8b10fd372b07d80b3f63615ce5ecb9bb1b0be6934fe5de3bb0328f9ffad5051f206bd7a0b426b85778fee0c063a1f029fb32cc639f3b2ee38d6b39f52c5c
  languageName: node
  linkType: hard

"chalk@npm:3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: ee650b0a065b3d7a6fda258e75d3a86fc8e4effa55871da730a9e42ccb035bf5fd203525e5a1ef45ec2582ecc4f65b47eb11357c526b84dd29a14fb162c414d2
  languageName: node
  linkType: hard

"chalk@npm:^2.4.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: e6543f02ec877732e3a2d1c3c3323ddb4d39fbab687c23f526e25bd4c6a9bf3b83a696e8c769d078e04e5754921648f7821b2a2acfd16c550435fd630026e073
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.1, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: 4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chalk@npm:^5.3.0":
  version: 5.4.1
  resolution: "chalk@npm:5.4.1"
  checksum: b23e88132c702f4855ca6d25cb5538b1114343e41472d5263ee8a37cccfccd9c4216d111e1097c6a27830407a1dc81fecdf2a56f2c63033d4dbbd88c10b0dcef
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: 57a09a86371331e0be35d9083ba429e86c4f4648ecbe27455dbfb343037c16ee6fdc7f6b61f433a57cc5ded5561d71c56a150e018f40c2ffb7bc93a26dae341e
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 96e4731b9ec8050cbb56ab684e8c48d6c33f7826b755802d14e3ebfdc51c57afeece3ea39bc6b09acc359e4363525388b915e16640c1378053820f5e70d0f27d
  languageName: node
  linkType: hard

"chokidar@npm:3.5.3":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 1076953093e0707c882a92c66c0f56ba6187831aa51bb4de878c1fec59ae611a3bf02898f190efec8e77a086b8df61c2b2a3ea324642a0558bdf8ee6c5dc9ca1
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.2, chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: 3058da7a5f4934b87cf6a90ef5fb68ebc5f7d06f143ed5a4650208e5d7acae47bc03ec844b29fbf5ba7e46e8daa6acecc878f7983a4f4bb7271593da91e61ff5
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 6f0109e36e111684291d46123d491bc4e7b7a1934c3a20dea28cba89f1d4a03acd892f5f6a81ed3855c38647e285a150e3c9ba062e38943bef57fee6c1554c3a
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.4.3
  resolution: "cjs-module-lexer@npm:1.4.3"
  checksum: 076b3af85adc4d65dbdab1b5b240fe5b45d44fcf0ef9d429044dd94d19be5589376805c44fb2d4b3e684e5fe6a9b7cf3e426476a6507c45283c5fc6ff95240be
  languageName: node
  linkType: hard

"class-transformer@npm:^0.5.1":
  version: 0.5.1
  resolution: "class-transformer@npm:0.5.1"
  checksum: 19809914e51c6db42c036166839906420bb60367df14e15f49c45c8c1231bf25ae661ebe94736ee29cc688b77101ef851a8acca299375cc52fc141b64acde18a
  languageName: node
  linkType: hard

"class-utils@npm:^0.3.5":
  version: 0.3.6
  resolution: "class-utils@npm:0.3.6"
  dependencies:
    arr-union: ^3.1.0
    define-property: ^0.2.5
    isobject: ^3.0.0
    static-extend: ^0.1.1
  checksum: d44f4afc7a3e48dba4c2d3fada5f781a1adeeff371b875c3b578bc33815c6c29d5d06483c2abfd43a32d35b104b27b67bfa39c2e8a422fa858068bd756cfbd42
  languageName: node
  linkType: hard

"class-validator@npm:^0.14.1":
  version: 0.14.1
  resolution: "class-validator@npm:0.14.1"
  dependencies:
    "@types/validator": ^13.11.8
    libphonenumber-js: ^1.10.53
    validator: ^13.9.0
  checksum: 946e914e47548b5081449c720ea6a4877bac63dc960e14fca4b990b56e64efe3802d12f07ec22d6420c290245b72ea2d646939239f2a3b597794e6c4c2a4f2ae
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 1f90262d5f6230a17e27d0c190b09d47ebe7efdd76a03b5a1127863f7b3c9aec4c3e6c8bb3a7bbf81d553d56a1fd35728f5a8ef4c63f867ac8d690109742a8c1
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: ^2.0.0
  checksum: 09ee6d8b5b818d840bf80ec9561eaf696672197d3a02a7daee2def96d5f52ce6e0bbe7afca754ccf14f04830b5a1b4556273e983507d5029f95bba3016618eda
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 92a2f98ff9037d09be3dfe1f0d749664797fb674bf388375a2207a1203b69d41847abf16434203e0089212479e47a358b13a0222ab9fccfe8e2644a7ccebd111
  languageName: node
  linkType: hard

"cli-highlight@npm:^2.1.11":
  version: 2.1.11
  resolution: "cli-highlight@npm:2.1.11"
  dependencies:
    chalk: ^4.0.0
    highlight.js: ^10.7.1
    mz: ^2.4.0
    parse5: ^5.1.1
    parse5-htmlparser2-tree-adapter: ^6.0.0
    yargs: ^16.0.0
  bin:
    highlight: bin/highlight
  checksum: b5b4af3b968aa9df77eee449a400fbb659cf47c4b03a395370bd98d5554a00afaa5819b41a9a8a1ca0d37b0b896a94e57c65289b37359a25b700b1f56eb04852
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 907a1c227ddf0d7a101e7ab8b300affc742ead4b4ebe920a5bf1bc6d45dce2958fcd195eb28fa25275062fe6fa9b109b93b63bc8033396ed3bcb50297008b3a3
  languageName: node
  linkType: hard

"cli-table3@npm:0.6.2":
  version: 0.6.2
  resolution: "cli-table3@npm:0.6.2"
  dependencies:
    "@colors/colors": 1.5.0
    string-width: ^4.2.0
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: aaa87929d86ba36e651e0280ab34cc28660e13da9dd2b6f8aa36e800c40e331c32bff53597cb9126e8a2e88e7a9025aff9c240350fe69876207d51ba452ef5e0
  languageName: node
  linkType: hard

"cli-table3@npm:0.6.3":
  version: 0.6.3
  resolution: "cli-table3@npm:0.6.3"
  dependencies:
    "@colors/colors": 1.5.0
    string-width: ^4.2.0
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: 39e580cb346c2eaf1bd8f4ff055ae644e902b8303c164a1b8894c0dc95941f92e001db51f49649011be987e708d9fa3183ccc2289a4d376a057769664048cc0c
  languageName: node
  linkType: hard

"cli-truncate@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-truncate@npm:2.1.0"
  dependencies:
    slice-ansi: ^3.0.0
    string-width: ^4.2.0
  checksum: dfaa3df675bcef7a3254773de768712b590250420345a4c7ac151f041a4bacb4c25864b1377bee54a39b5925a030c00eabf014e312e3a4ac130952ed3b3879e9
  languageName: node
  linkType: hard

"cli-truncate@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-truncate@npm:3.1.0"
  dependencies:
    slice-ansi: ^5.0.0
    string-width: ^5.0.0
  checksum: a19088878409ec0e5dc2659a5166929629d93cfba6d68afc9cde2282fd4c751af5b555bf197047e31c87c574396348d011b7aa806fec29c4139ea4f7f00b324c
  languageName: node
  linkType: hard

"cli-width@npm:^2.0.0":
  version: 2.2.1
  resolution: "cli-width@npm:2.2.1"
  checksum: e3a6d422d657ca111c630f69ee0f1a499e8f114eea158ccb2cdbedd19711edffa217093bbd43dafb34b68d1b1a3b5334126e51d059b9ec1d19afa53b42b3ef86
  languageName: node
  linkType: hard

"cli-width@npm:^3.0.0":
  version: 3.0.0
  resolution: "cli-width@npm:3.0.0"
  checksum: 125a62810e59a2564268c80fdff56c23159a7690c003e34aeb2e68497dccff26911998ff49c33916fcfdf71e824322cc3953e3f7b48b27267c7a062c81348a9a
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^7.0.0
  checksum: 6035f5daf7383470cef82b3d3db00bec70afb3423538c50394386ffbbab135e26c3689c41791f911fa71b62d13d3863c712fdd70f0fbdffd938a1e6fd09aac00
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"cls-hooked@npm:^4.2.2":
  version: 4.2.2
  resolution: "cls-hooked@npm:4.2.2"
  dependencies:
    async-hook-jl: ^1.7.6
    emitter-listener: ^1.0.1
    semver: ^5.4.1
  checksum: ee6c0c837f00aeea0ed6c05f67f64bc3f6d9e2055fbad703e571a588e47fdefc11034a2f99d1657880b000ec4e9657dd752f8be8bc2b55bac8c5ec092afb1b4e
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: c0e85ea0ca8bf0a50cbdca82efc5af0301240ca88ebe3644a6ffb8ffe911f34d40f8fbcf8f1d52c5ddd66706abd4d3bfcd64259f1e8e2371d4f47573b0dc8c28
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: ed7008e2e8b6852c5483b444a3ae6e976e088d4335a85aa0a9db2861c5f1d31bd2d7ff97a60469b3388deeba661a619753afbe201279fb159b4b9548ab8269a1
  languageName: node
  linkType: hard

"collection-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "collection-visit@npm:1.0.0"
  dependencies:
    map-visit: ^1.0.0
    object-visit: ^1.0.0
  checksum: add72a8d1c37cb90e53b1aaa2c31bf1989bfb733f0b02ce82c9fa6828c7a14358dba2e4f8e698c02f69e424aeccae1ffb39acdeaf872ade2f41369e84a2fcf8a
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: 5ad3c534949a8c68fca8fbc6f09068f435f0ad290ab8b2f76841b9e6af7e0bb57b98cb05b0e19fe33f5d91e5a8611ad457e5f69e0a484caad1f7487fd0e8253c
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 566a3d42cca25b9b3cd5528cd7754b8e89c0eb646b7f214e8e2eaddb69994ac5f0557d9c175eb5d8f0ad73531140d9c47525085ee752a91a2ab15ab459caf6d6
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-support@npm:^1.1.2":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 8ffeaa270a784dc382f62d9be0a98581db43e11eee301af14734a6d089bd456478b1a8b3e7db7ca7dc5b18a75f828f775c44074020b51c05fc00e6d0992b1cc6
  languageName: node
  linkType: hard

"colorette@npm:^2.0.16":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: e94116ff33b0ff56f3b83b9ace895e5bf87c2a7a47b3401b8c3f3226e050d5ef76cf4072fb3325f9dc24d1698f9b730baf4e05eeaf861d74a1883073f4c98a40
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:4.1.1":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: 84a76c08fe6cc08c9c93f62ac573d2907d8e79138999312c92d4155bc2325d487d64d13f669b2000c9f8caf70493c1be2dac74fec3c51d5a04f8bc3ae1830bab
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 8b043bb8322ea1c39664a1598a95e0495bfe4ca2fad0d84a92d7d1d8d213e2a155b441d2470c8e08de7c4a28cf2bc6e169211c49e1b21d9f7edc6ae4d9356060
  languageName: node
  linkType: hard

"commitizen@npm:4.1.2":
  version: 4.1.2
  resolution: "commitizen@npm:4.1.2"
  dependencies:
    cachedir: 2.2.0
    cz-conventional-changelog: 3.2.0
    dedent: 0.7.0
    detect-indent: 6.0.0
    find-node-modules: 2.0.0
    find-root: 1.1.0
    fs-extra: 8.1.0
    glob: 7.1.4
    inquirer: 6.5.0
    is-utf8: ^0.2.1
    lodash: 4.17.15
    minimist: 1.2.5
    strip-bom: 4.0.0
    strip-json-comments: 3.0.1
  bin:
    commitizen: bin/commitizen
    git-cz: bin/git-cz
  checksum: 59ed95035ae532832d9c0f5374c4131b2b3799248ae87109a0abd198d8897bb9b03ccc3c8949733cdf754249cc076bcb01516e5c4a2b3799cee33e5ed33e90d4
  languageName: node
  linkType: hard

"commitizen@npm:^4.0.3":
  version: 4.3.1
  resolution: "commitizen@npm:4.3.1"
  dependencies:
    cachedir: 2.3.0
    cz-conventional-changelog: 3.3.0
    dedent: 0.7.0
    detect-indent: 6.1.0
    find-node-modules: ^2.1.2
    find-root: 1.1.0
    fs-extra: 9.1.0
    glob: 7.2.3
    inquirer: 8.2.5
    is-utf8: ^0.2.1
    lodash: 4.17.21
    minimist: 1.2.7
    strip-bom: 4.0.0
    strip-json-comments: 3.1.1
  bin:
    commitizen: bin/commitizen
    cz: bin/git-cz
    git-cz: bin/git-cz
  checksum: 3422c6f8b24075a650582f3939def379954714d3fc613dd60a927923f52b93742a196346f0669b63adf367e40127c0e481573ab1aa6cb8b96a06dfc903e923ab
  languageName: node
  linkType: hard

"compare-func@npm:^2.0.0":
  version: 2.0.0
  resolution: "compare-func@npm:2.0.0"
  dependencies:
    array-ify: ^1.0.0
    dot-prop: ^5.1.0
  checksum: 78bd4dd4ed311a79bd264c9e13c36ed564cde657f1390e699e0f04b8eee1fc06ffb8698ce2dfb5fbe7342d509579c82d4e248f08915b708f77f7b72234086cc3
  languageName: node
  linkType: hard

"component-emitter@npm:^1.2.1, component-emitter@npm:^1.3.0":
  version: 1.3.1
  resolution: "component-emitter@npm:1.3.1"
  checksum: e4900b1b790b5e76b8d71b328da41482118c0f3523a516a41be598dc2785a07fd721098d9bf6e22d89b19f4fa4e1025160dc00317ea111633a3e4f75c2b86032
  languageName: node
  linkType: hard

"compress-commons@npm:^4.1.2":
  version: 4.1.2
  resolution: "compress-commons@npm:4.1.2"
  dependencies:
    buffer-crc32: ^0.2.13
    crc32-stream: ^4.0.2
    normalize-path: ^3.0.0
    readable-stream: ^3.6.0
  checksum: e5fa03cb374ed89028e20226c70481e87286240392d5c6856f4e7fef40605c1892748648e20ed56597d390d76513b1b9bb4dbd658a1bbff41c9fa60107c74d3f
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"concat-stream@npm:^1.5.2":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^2.2.2
    typedarray: ^0.0.6
  checksum: 2e9864e18282946dabbccb212c5c7cec0702745e3671679eb8291812ca7fd12023f7d8cb36493942a62f770ac96a7f90009dc5c82ad69893438371720fa92617
  languageName: node
  linkType: hard

"concat-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "concat-stream@npm:2.0.0"
  dependencies:
    buffer-from: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^3.0.2
    typedarray: ^0.0.6
  checksum: 29565dd9198fe1d8cf57f6cc71527dbc6ad67e12e4ac9401feb389c53042b2dceedf47034cbe702dfc4fd8df3ae7e6bfeeebe732cc4fa2674e484c13f04c219a
  languageName: node
  linkType: hard

"consola@npm:^2.15.0":
  version: 2.15.3
  resolution: "consola@npm:2.15.3"
  checksum: 34a337e6b4a1349ee4d7b4c568484344418da8fdb829d7d71bfefcd724f608f273987633b6eef465e8de510929907a092e13cb7a28a5d3acb3be446fcc79fd5e
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.0.0, console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 7ab51d30b52d461412cd467721bb82afe695da78fff8f29fe6f6b9cbaac9a2328e27a22a966014df9532100f6dd85370460be8130b9c677891ba36d96a343f50
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"conventional-changelog-angular@npm:^6.0.0":
  version: 6.0.0
  resolution: "conventional-changelog-angular@npm:6.0.0"
  dependencies:
    compare-func: ^2.0.0
  checksum: a661ff7b79d4b829ccf8f424ef1bb210e777c1152a1ba5b2ba0a8639529c315755b82a6f84684f1b552c4e8ed6696bfe57317c5f7b868274e9a72b2bf13081ba
  languageName: node
  linkType: hard

"conventional-changelog-conventionalcommits@npm:^5.0.0":
  version: 5.0.0
  resolution: "conventional-changelog-conventionalcommits@npm:5.0.0"
  dependencies:
    compare-func: ^2.0.0
    lodash: ^4.17.15
    q: ^1.5.1
  checksum: 02cc9313b44953332e9d45833615675cbc4d0f4129b27ea7218f8f4fc2f35124748725969c0cb3dc645713d19684540b87c5af25bd17ce3dccd7ef4e05e42442
  languageName: node
  linkType: hard

"conventional-commit-types@npm:^3.0.0":
  version: 3.0.0
  resolution: "conventional-commit-types@npm:3.0.0"
  checksum: 609703fea60b55549de8ef07052a95a894b48cefa4d187f4500a632284f20e799becf18689689e9eccefc1457860d031c77600169e5df49c679d29ae436c3422
  languageName: node
  linkType: hard

"conventional-commits-parser@npm:^4.0.0":
  version: 4.0.0
  resolution: "conventional-commits-parser@npm:4.0.0"
  dependencies:
    JSONStream: ^1.3.5
    is-text-path: ^1.0.1
    meow: ^8.1.2
    split2: ^3.2.2
  bin:
    conventional-commits-parser: cli.js
  checksum: 12e390cc80ad8a825c5775a329b95e11cf47a6df7b8a3875d375e28b8cb27c4f32955842ea73e4e357cff9757a6be99fdffe4fda87a23e9d8e73f983425537a0
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.4.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: 281da55454bf8126cbc6625385928c43479f2060984180c42f3a86c8b8c12720a24eac260624a7d1e090004028d2dee78602330578ceec1a08e27cb8bb0a8a5b
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-parser@npm:1.4.6":
  version: 1.4.6
  resolution: "cookie-parser@npm:1.4.6"
  dependencies:
    cookie: 0.4.1
    cookie-signature: 1.0.6
  checksum: 9c2ade5459290802cd472a2d2a6e46fbd7de3e8514e02bfed5edfde892d77733c7f89d9d2015f752a9087680429b416972d7aba748bf6824e21eb680c8556383
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.4.1":
  version: 0.4.1
  resolution: "cookie@npm:0.4.1"
  checksum: 4d7bc798df3d0f34035977949cd6b7d05bbab47d7dcb868667f460b578a550cd20dec923832b8a3a107ef35aba091a3975e14f79efacf6e39282dc0fed6db4a1
  languageName: node
  linkType: hard

"cookie@npm:0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: c01ca3ef8d7b8187bae434434582288681273b5a9ed27521d4d7f9f7928fe0c920df0decd9f9d3bbd2d14ac432b8c8cf42b98b3bdd5bfe0e6edddeebebe8b61d
  languageName: node
  linkType: hard

"cookiejar@npm:^2.1.4":
  version: 2.1.4
  resolution: "cookiejar@npm:2.1.4"
  checksum: 2dae55611c6e1678f34d93984cbd4bda58f4fe3e5247cc4993f4a305cd19c913bbaf325086ed952e892108115073a747596453d3dc1c34947f47f731818b8ad1
  languageName: node
  linkType: hard

"copy-descriptor@npm:^0.1.0":
  version: 0.1.1
  resolution: "copy-descriptor@npm:0.1.1"
  checksum: 161f6760b7348c941007a83df180588fe2f1283e0867cc027182734e0f26134e6cc02de09aa24a95dc267b2e2025b55659eef76c8019df27bc2d883033690181
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cors@npm:2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: ^4
    vary: ^1
  checksum: 373702b7999409922da80de4a61938aabba6929aea5b6fd9096fefb9e8342f626c0ebd7507b0e8b0b311380744cc985f27edebc0a26e0ddb784b54e1085de761
  languageName: node
  linkType: hard

"cosmiconfig-typescript-loader@npm:^4.0.0":
  version: 4.4.0
  resolution: "cosmiconfig-typescript-loader@npm:4.4.0"
  peerDependencies:
    "@types/node": "*"
    cosmiconfig: ">=7"
    ts-node: ">=10"
    typescript: ">=4"
  checksum: a204eb354943f84ab0434d108fdf593db84c477f107f3ccb586e2d659c1d87f03071d8983c96d4ce2a59cc524ec845697f0432876339e4c28bde84b665cd92a6
  languageName: node
  linkType: hard

"cosmiconfig-typescript-loader@npm:^6.1.0":
  version: 6.1.0
  resolution: "cosmiconfig-typescript-loader@npm:6.1.0"
  dependencies:
    jiti: ^2.4.1
  peerDependencies:
    "@types/node": "*"
    cosmiconfig: ">=9"
    typescript: ">=5"
  checksum: 5e3baf85a9da7dcdd7ef53a54d1293400eed76baf0abb3a41bf9fcc789f1a2653319443471f9a1dc32951f1de4467a6696ccd0f88640e7827f1af6ff94ceaf1a
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.1":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.2.1
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.10.0
  checksum: b923ff6af581638128e5f074a5450ba12c0300b71302398ea38dbeabd33bbcaa0245ca9adbedfcf284a07da50f99ede5658c80bb3e39e2ce770a99d28a21ef03
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.0.0":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
    path-type: ^4.0.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 0382a9ed13208f8bfc22ca2f62b364855207dffdb73dc26e150ade78c3093f1cf56172df2dd460c8caf2afa91c0ed4ec8a88c62f8f9cd1cf423d26506aa8797a
  languageName: node
  linkType: hard

"cosmiconfig@npm:^9.0.0":
  version: 9.0.0
  resolution: "cosmiconfig@npm:9.0.0"
  dependencies:
    env-paths: ^2.2.1
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 1c1703be4f02a250b1d6ca3267e408ce16abfe8364193891afc94c2d5c060b69611fdc8d97af74b7e6d5d1aac0ab2fb94d6b079573146bc2d756c2484ce5f0ee
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 11dcf4a2e77ee793835d49f2c028838eae58b44f50d1ff08394a610bfd817523f105d6ae4d9b5bef0aad45510f633eb23c903e9902e4409bed1ce70cb82b9bf0
  languageName: node
  linkType: hard

"crc32-stream@npm:^4.0.2":
  version: 4.0.3
  resolution: "crc32-stream@npm:4.0.3"
  dependencies:
    crc-32: ^1.2.0
    readable-stream: ^3.4.0
  checksum: 127b0c66a947c54db37054fca86085722140644d3a75ebc61d4477bad19304d2936386b0461e8ee9e1c24b00e804cd7c2e205180e5bcb4632d20eccd60533bc4
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: 157cbc59b2430ae9a90034a5f3a1b398b6738bf510f713edc4d4e45e169bc514d3d99dd34d8d01ca7ae7830b5b8b537e46ae8f3c8f932371b0875c0151d7ec91
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"cz-conventional-changelog@npm:3.2.0":
  version: 3.2.0
  resolution: "cz-conventional-changelog@npm:3.2.0"
  dependencies:
    "@commitlint/load": ">6.1.1"
    chalk: ^2.4.1
    commitizen: ^4.0.3
    conventional-commit-types: ^3.0.0
    lodash.map: ^4.5.1
    longest: ^2.0.1
    word-wrap: ^1.0.3
  dependenciesMeta:
    "@commitlint/load":
      optional: true
  checksum: d4942a212a55cace81f609a9156febfdb73c6ded98553de30a2cf6e8d05600763721a7d8edafdae497d4bce4b7eb7cb32f6f1cb3114aa931ed5086e5f894e7fb
  languageName: node
  linkType: hard

"cz-conventional-changelog@npm:3.3.0":
  version: 3.3.0
  resolution: "cz-conventional-changelog@npm:3.3.0"
  dependencies:
    "@commitlint/load": ">6.1.1"
    chalk: ^2.4.1
    commitizen: ^4.0.3
    conventional-commit-types: ^3.0.0
    lodash.map: ^4.5.1
    longest: ^2.0.1
    word-wrap: ^1.0.3
  dependenciesMeta:
    "@commitlint/load":
      optional: true
  checksum: 895d64bb60b7014ec98fdbc211b454e3a1d585b10a818a4a3cf4c0f4b8576712d2daf4f8eb670e6c68e10bbb72ed73ab73b1a9e4673be41405591454e5bf5734
  languageName: node
  linkType: hard

"dargs@npm:^7.0.0":
  version: 7.0.0
  resolution: "dargs@npm:7.0.0"
  checksum: ec7f6a8315a8fa2f8b12d39207615bdf62b4d01f631b96fbe536c8ad5469ab9ed710d55811e564d0d5c1d548fc8cb6cc70bf0939f2415790159f5a75e0f96c92
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.9, dayjs@npm:^1.8.34":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.3.3":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: 121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: db94f1a182bf886f57b4755f85b3a74c39b5114b9377b7ab375dc2cfa3454f09490cc6c30f829df3fc8042bc8b8995f6567ce5cd96f3bc3688bd24027197d9de
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: 37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"decamelize-keys@npm:^1.1.0":
  version: 1.1.1
  resolution: "decamelize-keys@npm:1.1.1"
  dependencies:
    decamelize: ^1.1.0
    map-obj: ^1.0.0
  checksum: 4ca385933127437658338c65fb9aead5f21b28d3dd3ccd7956eb29aab0953b5d3c047fbc207111672220c71ecf7a4d34f36c92851b7bbde6fca1a02c541bdd7d
  languageName: node
  linkType: hard

"decamelize@npm:^1.1.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.0":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"dedent@npm:0.7.0, dedent@npm:^0.7.0":
  version: 0.7.0
  resolution: "dedent@npm:0.7.0"
  checksum: 7c3aa00ddfe3e5fcd477958e156156a5137e3bb6ff1493ca05edff4decf29a90a057974cc77e75951f8eb801c1816cb45aea1f52d628cdd000b82b36ab839d1b
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-property@npm:^0.2.5":
  version: 0.2.5
  resolution: "define-property@npm:0.2.5"
  dependencies:
    is-descriptor: ^0.1.0
  checksum: 9986915c0893818dedc9ca23eaf41370667762fd83ad8aa4bf026a28563120dbaacebdfbfbf2b18d3b929026b9c6ee972df1dbf22de8fafb5fe6ef18361e4750
  languageName: node
  linkType: hard

"define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "define-property@npm:1.0.0"
  dependencies:
    is-descriptor: ^1.0.0
  checksum: d7cf09db10d55df305f541694ed51dafc776ad9bb8a24428899c9f2d36b11ab38dce5527a81458d1b5e7c389f8cbe803b4abad6e91a0037a329d153b84fc975e
  languageName: node
  linkType: hard

"define-property@npm:^2.0.2":
  version: 2.0.2
  resolution: "define-property@npm:2.0.2"
  dependencies:
    is-descriptor: ^1.0.2
    isobject: ^3.0.1
  checksum: f91a08ad008fa764172a2c072adc7312f10217ade89ddaea23018321c6d71b2b68b8c229141ed2064179404e345c537f1a2457c379824813695b51a6ad3e4969
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: ba05874b91148e1db4bf254750c042bf2215febd23a6d3cda2e64896aef79745fbd4b9996488bd3cafb39ce19dbce0fd6e3b6665275638befffe1c9b312b91b5
  languageName: node
  linkType: hard

"denque@npm:^2.1.0":
  version: 2.1.0
  resolution: "denque@npm:2.1.0"
  checksum: f9ef81aa0af9c6c614a727cb3bd13c5d7db2af1abf9e6352045b86e85873e629690f6222f4edd49d10e4ccf8f078bbeec0794fafaf61b659c0589d0c511ec363
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-file@npm:^1.0.0":
  version: 1.0.0
  resolution: "detect-file@npm:1.0.0"
  checksum: c782a5f992047944c39d337c82f5d1d21d65d1378986d46c354df9d9ec6d5f356bca0182969c11b08b9b8a7af8727b3c2d5a9fad0b022be4a3bf4c216f63ed07
  languageName: node
  linkType: hard

"detect-indent@npm:6.0.0":
  version: 6.0.0
  resolution: "detect-indent@npm:6.0.0"
  checksum: 6ca3511e6189d63d22e80ee81d847d81b800dc6a23dd23b10e3d39ca8407b5cf763e8dbef25ac18b3b75dd14c4ab00ad39316656eec5ae3932abf745967d9bf4
  languageName: node
  linkType: hard

"detect-indent@npm:6.1.0":
  version: 6.1.0
  resolution: "detect-indent@npm:6.1.0"
  checksum: dd83cdeda9af219cf77f5e9a0dc31d828c045337386cfb55ce04fad94ba872ee7957336834154f7647b89b899c3c7acc977c57a79b7c776b506240993f97acc7
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0":
  version: 2.0.3
  resolution: "detect-libc@npm:2.0.3"
  checksum: 88095bda8f90220c95f162bf92cad70bd0e424913e655c20578600e35b91edc261af27531cf160a331e185c0ced93944bc7e09939143225f56312d7fd800fdb7
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: c38cfc8eeb9fda09febb44bcd85e467c970d4e3bf526095394e5a4f18bc26dd0cf6b22c69c1fa9969261521c593836db335c2795218f6d781a512aea2fb8209d
  languageName: node
  linkType: hard

"dezalgo@npm:^1.0.4":
  version: 1.0.4
  resolution: "dezalgo@npm:1.0.4"
  dependencies:
    asap: ^2.0.0
    wrappy: 1
  checksum: 8a870ed42eade9a397e6141fe5c025148a59ed52f1f28b1db5de216b4d57f0af7a257070c3af7ce3d5508c1ce9dd5009028a76f4b2cc9370dc56551d2355fad8
  languageName: node
  linkType: hard

"diff-sequences@npm:^28.1.1":
  version: 28.1.1
  resolution: "diff-sequences@npm:28.1.1"
  checksum: 26f29fa3f6b8c9040c3c6f6dab85413d90a09c8e6cb17b318bbcf64f225d7dcb1fb64392f3a9919a90888b434c4f6c8a4cc4f807aad02bbabae912c5d13c31f7
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: 81b91f9d39c4eaca068eb0c1eb0e4afbdc5bb2941d197f513dd596b820b956fef43485876226d65d497bebc15666aa2aa82c679e84f65d5f2bfbf14ee46e32c1
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: c96bdccabe9d62ab6fea9399fdff04a66e6563c1d6fb3a3a063e8d53c3bb136ba63e84250bbf63d00086a769ad53aef92d2bd483f03f837fc97b71cbee6b2520
  languageName: node
  linkType: hard

"dot-prop@npm:^5.1.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: ^2.0.0
  checksum: 93f0d343ef87fe8869320e62f2459f7e70f49c6098d948cc47e060f4a3f827d0ad61e83cb82f2bd90cd5b9571b8d334289978a43c0f98fea4f0e99ee8faa0599
  languageName: node
  linkType: hard

"dotenv-expand@npm:12.0.1":
  version: 12.0.1
  resolution: "dotenv-expand@npm:12.0.1"
  dependencies:
    dotenv: ^16.4.5
  checksum: 51996bfa670073d7a441b8fbed26ac991026fba2c05e9a937a898ce7d2a2e7166f7b6ec4eb8879e576defb5d1ad399ed1300db8f803d6977577fea55b4d82dac
  languageName: node
  linkType: hard

"dotenv@npm:16.4.7, dotenv@npm:^16.0.3":
  version: 16.4.7
  resolution: "dotenv@npm:16.4.7"
  checksum: be9f597e36a8daf834452daa1f4cc30e5375a5968f98f46d89b16b983c567398a330580c88395069a77473943c06b877d1ca25b4afafcdd6d4adb549e8293462
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 5bc94c919fbd955bf0ba44d33922a1e93d1078e64a1db5c30faeded1d996e7a83c55332cb8ea4fae5a9ca4d0be44cbceb95c5811e70f9f095298df09d1997dd9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"duplexer2@npm:~0.1.4":
  version: 0.1.4
  resolution: "duplexer2@npm:0.1.4"
  dependencies:
    readable-stream: ^2.0.2
  checksum: 0765a4cc6fe6d9615d43cc6dbccff6f8412811d89a6f6aa44828ca9422a0a469625ce023bf81cee68f52930dbedf9c5716056ff264ac886612702d134b5e39b4
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11, ecdsa-sig-formatter@npm:^1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: ebfbf19d4b8be938f4dd4a83b8788385da353d63307ede301a9252f9f7f88672e76f2191618fd8edfc2f24679236064176fab0b78131b161ee73daa37125408c
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.102
  resolution: "electron-to-chromium@npm:1.5.102"
  checksum: db07dab3ee3b7fbc39ad26203925669ade86b12a62d09fa14ae48a354a0f34d162ac9a2ca9d6f70ceb1b16821b01b155e56467702bcc915da1e1dd147dd034b4
  languageName: node
  linkType: hard

"emitter-listener@npm:^1.0.1":
  version: 1.1.2
  resolution: "emitter-listener@npm:1.1.2"
  dependencies:
    shimmer: ^1.2.0
  checksum: d16f4f2da4b46cee09c900260d8527c58b32b6e1288c734a561f867dac8bf4da7f6aa429b6db1e5a90f688d754d86456bd22ee99f2fac4d9d955ef6ef8c19e55
  languageName: node
  linkType: hard

"emittery@npm:^0.10.2":
  version: 0.10.2
  resolution: "emittery@npm:0.10.2"
  checksum: 2caeea7501a0cca9b0e9d8d0a84d7d059cd2319ab02016bb6f81ae8bc2f3353c6734ed50a5fe0e4e2b96ebcc1623c1344b6beec51a4feda34b121942dd50ba55
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: 36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.4.1":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: ^1.4.0
  checksum: b0701c92a10b89afb1cb45bf54a5292c6f008d744eb4382fa559d54775ff31617d1d7bc3ef617575f552e24fad2c7c1a1835948c66b3f3a4be0a6c1f35c883d8
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.0.0, enhanced-resolve@npm:^5.10.0, enhanced-resolve@npm:^5.7.0, enhanced-resolve@npm:^5.9.3":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: 4cffd9b125225184e2abed9fdf0ed3dbd2224c873b165d0838fd066cde32e0918626cba2f1f4bf6860762f13a7e2364fd89a82b99566be2873d813573ac71846
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0, env-paths@npm:^2.2.1":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-module-lexer@npm:^0.9.0":
  version: 0.9.3
  resolution: "es-module-lexer@npm:0.9.3"
  checksum: be77d73aee709fdc68d22b9938da81dfee3bc45e8d601629258643fe5bfdab253d6e2540035e035cfa8cf52a96366c1c19b46bcc23b4507b1d44e5907d2e7f6c
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 2530479fe8db57eace5e8646c9c2a9c80fa279614986d16dcc6bcaceb63ae77f05a851ba6c43756d816c61d7f4534baf56e3c705e3e0d884818a46808811c507
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.3.0":
  version: 8.10.0
  resolution: "eslint-config-prettier@npm:8.10.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 19f8c497d9bdc111a17a61b25ded97217be3755bbc4714477dfe535ed539dddcaf42ef5cf8bb97908b058260cf89a3d7c565cb0be31096cbcd39f4c2fa5fe43c
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^4.0.0":
  version: 4.2.1
  resolution: "eslint-plugin-prettier@npm:4.2.1"
  dependencies:
    prettier-linter-helpers: ^1.0.0
  peerDependencies:
    eslint: ">=7.28.0"
    prettier: ">=2.0.0"
  peerDependenciesMeta:
    eslint-config-prettier:
      optional: true
  checksum: c5e7316baeab9d96ac39c279f16686e837277e5c67a8006c6588bcff317edffdc1532fb580441eb598bc6770f6444006756b68a6575dff1cd85ebe227252d0b7
  languageName: node
  linkType: hard

"eslint-plugin-simple-import-sort@npm:10.0.0":
  version: 10.0.0
  resolution: "eslint-plugin-simple-import-sort@npm:10.0.0"
  peerDependencies:
    eslint: ">=5.0.0"
  checksum: 1ae0814d23816d51d010cfbc5ee0a0dde8d825a3093876b2e8219a0562d53f4d4794508551e503ebe2ea98904cb35204dbe54dfbf9d7fc8b8e3ea25c52aa68ac
  languageName: node
  linkType: hard

"eslint-plugin-unused-imports@npm:2.0.0":
  version: 2.0.0
  resolution: "eslint-plugin-unused-imports@npm:2.0.0"
  dependencies:
    eslint-rule-composer: ^0.3.0
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^5.0.0
    eslint: ^8.0.0
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
  checksum: 016241cda51ccf953b1e04a50b8c18543d46bae08670573dca3cf0ea195be38964f66e9d179a1f4c50cfcad5b9b76e21c3142fde171587a42fc6824727b0ca3d
  languageName: node
  linkType: hard

"eslint-rule-composer@npm:^0.3.0":
  version: 0.3.0
  resolution: "eslint-rule-composer@npm:0.3.0"
  checksum: 1f0c40d209e1503a955101a0dbba37e7fc67c8aaa47a5b9ae0b0fcbae7022c86e52b3df2b1b9ffd658e16cd80f31fff92e7222460a44d8251e61d49e0af79a07
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1, eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: d30ef9dc1c1cbdece34db1539a4933fe3f9b14e1ffb27ecc85987902ee663ad7c9473bbd49a9a03195a373741e62e2f807c4938992e019b511993d163450e70a
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: 613c267aea34b5a6d6c00514e8545ef1f1433108097e857225fed40d397dd6b1809dffd11c2fde23b37ca53d7bf935fe04d2a18e6fc932b31837b6ad67e1c116
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint@npm:^8.0.1":
  version: 8.57.1
  resolution: "eslint@npm:8.57.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.6.1
    "@eslint/eslintrc": ^2.1.4
    "@eslint/js": 8.57.1
    "@humanwhocodes/config-array": ^0.13.0
    "@humanwhocodes/module-importer": ^1.0.1
    "@nodelib/fs.walk": ^1.2.8
    "@ungap/structured-clone": ^1.2.0
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    doctrine: ^3.0.0
    escape-string-regexp: ^4.0.0
    eslint-scope: ^7.2.2
    eslint-visitor-keys: ^3.4.3
    espree: ^9.6.1
    esquery: ^1.4.2
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    globals: ^13.19.0
    graphemer: ^1.4.0
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    js-yaml: ^4.1.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: 1fd31533086c1b72f86770a4d9d7058ee8b4643fd1cfd10c7aac1ecb8725698e88352a87805cf4b2ce890aa35947df4b4da9655fb7fdfa60dbb448a43f6ebcf1
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: 1a2e9b4699b715347f62330bcc76aee224390c28bb02b31a3752e9d07549c473f5f986720483c6469cf3cfb3c9d05df612ffc69eb1ee94b54b739e67de9bb460
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: 81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 9cb46463ef8a8a4905d3708a652d60122a0c20bb58dec7e0e12ab0e7235123d74214fc0141d743c381813e1b992767e2708194f6f6e0f9fd00c1b4e0887b8b6d
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"events@npm:^3.2.0, events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"exceljs@npm:^4.4.0":
  version: 4.4.0
  resolution: "exceljs@npm:4.4.0"
  dependencies:
    archiver: ^5.0.0
    dayjs: ^1.8.34
    fast-csv: ^4.3.1
    jszip: ^3.10.1
    readable-stream: ^3.6.0
    saxes: ^5.0.1
    tmp: ^0.2.0
    unzipper: ^0.10.11
    uuid: ^8.3.0
  checksum: 47aa3e2b1238719946b788bbe00ea7068e746df64697ec7b93662061f10c8081a69190f0c2110a69af8b0eedf26e40120479f4e93b8ce3957a83ab92dfe57f10
  languageName: node
  linkType: hard

"execa@npm:^4.0.2":
  version: 4.1.0
  resolution: "execa@npm:4.1.0"
  dependencies:
    cross-spawn: ^7.0.0
    get-stream: ^5.0.0
    human-signals: ^1.1.1
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.0
    onetime: ^5.1.0
    signal-exit: ^3.0.2
    strip-final-newline: ^2.0.0
  checksum: 02211601bb1c52710260edcc68fb84c3c030dc68bafc697c90ada3c52cc31375337de8c24826015b8382a58d63569ffd203b79c94fef217d65503e3e8d2c52ba
  languageName: node
  linkType: hard

"execa@npm:^5.0.0, execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: 71d2ad9b36bc25bb8b104b17e830b40a08989be7f7d100b13269aaae7c3784c3e6e1e88a797e9e87523993a25ba27c8958959a554535370672cfb4d824af8989
  languageName: node
  linkType: hard

"expand-brackets@npm:^2.1.4":
  version: 2.1.4
  resolution: "expand-brackets@npm:2.1.4"
  dependencies:
    debug: ^2.3.3
    define-property: ^0.2.5
    extend-shallow: ^2.0.1
    posix-character-classes: ^0.1.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: 3e2fb95d2d7d7231486493fd65db913927b656b6fcdfcce41e139c0991a72204af619ad4acb1be75ed994ca49edb7995ef241dbf8cf44dc3c03d211328428a87
  languageName: node
  linkType: hard

"expand-tilde@npm:^2.0.0, expand-tilde@npm:^2.0.2":
  version: 2.0.2
  resolution: "expand-tilde@npm:2.0.2"
  dependencies:
    homedir-polyfill: ^1.0.1
  checksum: 205a60497422746d1c3acbc1d65bd609b945066f239a2b785e69a7a651ac4cbeb4e08555b1ea0023abbe855e6fcb5bbf27d0b371367fdccd303d4fb2b4d66845
  languageName: node
  linkType: hard

"expect@npm:^28.1.3":
  version: 28.1.3
  resolution: "expect@npm:28.1.3"
  dependencies:
    "@jest/expect-utils": ^28.1.3
    jest-get-type: ^28.0.2
    jest-matcher-utils: ^28.1.3
    jest-message-util: ^28.1.3
    jest-util: ^28.1.3
  checksum: fce8aa5462294fc7a32b17eef697e9999989b383e62f88b76e69badc59d4abb231dd6131aebaf27c4683be2fb0aa345e125bf2f15545e30a31dc85ec98673608
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:4.18.2":
  version: 4.18.2
  resolution: "express@npm:4.18.2"
  dependencies:
    accepts: ~1.3.8
    array-flatten: 1.1.1
    body-parser: 1.20.1
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.5.0
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: 2.0.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: 1.2.0
    fresh: 0.5.2
    http-errors: 2.0.0
    merge-descriptors: 1.0.1
    methods: ~1.1.2
    on-finished: 2.4.1
    parseurl: ~1.3.3
    path-to-regexp: 0.1.7
    proxy-addr: ~2.0.7
    qs: 6.11.0
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.18.0
    serve-static: 1.15.0
    setprototypeof: 1.2.0
    statuses: 2.0.1
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 75af556306b9241bc1d7bdd40c9744b516c38ce50ae3210658efcbf96e3aed4ab83b3432f06215eae5610c123bc4136957dc06e50dfc50b7d4d775af56c4c59c
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: ^0.1.0
  checksum: ee1cb0a18c9faddb42d791b2d64867bd6cfd0f3affb711782eb6e894dd193e2934a7f529426aac7c8ddb31ac5d38000a00aa2caf08aa3dfc3e1c8ff6ba340bd9
  languageName: node
  linkType: hard

"extend-shallow@npm:^3.0.0, extend-shallow@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend-shallow@npm:3.0.2"
  dependencies:
    assign-symbols: ^1.0.0
    is-extendable: ^1.0.1
  checksum: f39581b8f98e3ad94995e33214fff725b0297cf09f2725b6f624551cfb71e0764accfd0af80becc0182af5014d2a57b31b85ec999f9eb8a6c45af81752feac9a
  languageName: node
  linkType: hard

"extend@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"external-editor@npm:^3.0.3":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: c98f1ba3efdfa3c561db4447ff366a6adb5c1e2581462522c56a18bf90dfe4da382f9cd1feee3e330108c3595a854b218272539f311ba1b3298f841eb0fbf339
  languageName: node
  linkType: hard

"extglob@npm:^2.0.4":
  version: 2.0.4
  resolution: "extglob@npm:2.0.4"
  dependencies:
    array-unique: ^0.3.2
    define-property: ^1.0.0
    expand-brackets: ^2.1.4
    extend-shallow: ^2.0.1
    fragment-cache: ^0.2.1
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: e1a891342e2010d046143016c6c03d58455c2c96c30bf5570ea07929984ee7d48fad86b363aee08f7a8a638f5c3a66906429b21ecb19bc8e90df56a001cd282c
  languageName: node
  linkType: hard

"fast-csv@npm:^4.3.1":
  version: 4.3.6
  resolution: "fast-csv@npm:4.3.6"
  dependencies:
    "@fast-csv/format": 4.3.5
    "@fast-csv/parse": 4.3.6
  checksum: 45118395a75dbb4fb3a074ee76f03abefe8414accbde6613ec3f326c3fef7098b8dc6297de65b6f15755f7520079aac58249cc939010033285161af8b1e007c9
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 5c19af237edb5d5effda008c891a18a585f74bf12953be57923f17a3a4d0979565fc64dbc73b9e20926b9d895f5b690c618cbb969af0cf022e3222471220ad29
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:2.1.0, fast-json-stable-stringify@npm:2.x, fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:2.1.1, fast-safe-stringify@npm:^2.1.1":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: d90ec1c963394919828872f21edaa3ad6f1dddd288d2bd4e977027afff09f5db40f94e39536d4646f7e01761d704d72d51dce5af1b93717f3489ef808f5f4e4d
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 74a513c2af0584448aee71ce56005185f81239eab7a2343110e5bad50c39ad4fb19c5a6f99783ead1cac7ccaf3461a6034fda89fffa2b30b6d99b9f21c2f9d29
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.4.1":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: ^1.0.5
  bin:
    fxparser: src/cli/cli.js
  checksum: 7f334841fe41bfb0bf5d920904ccad09cefc4b5e61eaf4c225bf1e1bb69ee77ef2147d8942f783ee8249e154d1ca8a858e10bda78a5d78b8bed3f48dcee9bf33
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.0
  resolution: "fastq@npm:1.19.0"
  dependencies:
    reusify: ^1.0.4
  checksum: d6a001638f1574a696660fcbba5300d017760432372c801632c325ca7c16819604841c92fd3ccadcdacec0966ca336363a5ff57bc5f0be335d8ea7ac6087b98f
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: 2.1.1
  checksum: feae89ac148adb8f6ae8ccd87632e62b13563e6fb114cacb5265c51f585b17e2e268084519fb2edd133872f1d47a18e6bfd7e5e08625c0d41b93149694187581
  languageName: node
  linkType: hard

"figures@npm:^2.0.0":
  version: 2.0.0
  resolution: "figures@npm:2.0.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 5dc5a75fec3e7e04ae65d6ce51d28b3e70d4656c51b06996b6fdb2cb5b542df512e3b3c04482f5193a964edddafa5521479ff948fa84e12ff556e53e094ab4ce
  languageName: node
  linkType: hard

"figures@npm:^3.0.0":
  version: 3.2.0
  resolution: "figures@npm:3.2.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 9c421646ede432829a50bc4e55c7a4eb4bcb7cc07b5bab2f471ef1ab9a344595bbebb6c5c21470093fbb730cd81bbca119624c40473a125293f656f49cb47629
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-type@npm:^16.5.4":
  version: 16.5.4
  resolution: "file-type@npm:16.5.4"
  dependencies:
    readable-web-to-node-stream: ^3.0.0
    strtok3: ^6.2.4
    token-types: ^4.1.1
  checksum: a6c9ab8bc05bc9c212bec239fb0d5bf59ddc9b3912f00c4ef44622e67ae4e553a1cc8372e9e595e14859035188eb305d05d488fa3c5c2a2ad90bb7745b3004ef
  languageName: node
  linkType: hard

"fill-range@npm:^4.0.0":
  version: 4.0.0
  resolution: "fill-range@npm:4.0.0"
  dependencies:
    extend-shallow: ^2.0.1
    is-number: ^3.0.0
    repeat-string: ^1.6.1
    to-regex-range: ^2.1.0
  checksum: ccd57b7c43d7e28a1f8a60adfa3c401629c08e2f121565eece95e2386ebc64dedc7128d8c3448342aabf19db0c55a34f425f148400c7a7be9a606ba48749e089
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.2.0":
  version: 1.2.0
  resolution: "finalhandler@npm:1.2.0"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: 2.4.1
    parseurl: ~1.3.3
    statuses: 2.0.1
    unpipe: ~1.0.0
  checksum: 64b7e5ff2ad1fcb14931cd012651631b721ce657da24aedb5650ddde9378bf8e95daa451da43398123f5de161a81e79ff5affe4f9f2a6d2df4a813d6d3e254b7
  languageName: node
  linkType: hard

"find-node-modules@npm:2.0.0":
  version: 2.0.0
  resolution: "find-node-modules@npm:2.0.0"
  dependencies:
    findup-sync: ^3.0.0
    merge: ^1.2.1
  checksum: a47345273ab62b0aed073691cbaab73bfa622a5aaacfddd7417be0d9f7f36f64bfd96c04f9221271e8d07aa0df5fd30b6cde72eabb5831e24b573ec9adbbcca2
  languageName: node
  linkType: hard

"find-node-modules@npm:^2.1.2":
  version: 2.1.3
  resolution: "find-node-modules@npm:2.1.3"
  dependencies:
    findup-sync: ^4.0.0
    merge: ^2.1.1
  checksum: 61fd8300635f6b6237985f05ef9ba01dbd29482c625c8c34a321fe5e9e69a48f4ab9e03c3026cd22eb2b6618d01309b515a7cf73dd886fc2cf099f2e4ecaf598
  languageName: node
  linkType: hard

"find-root@npm:1.1.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: 1abc7f3bf2f8d78ff26d9e00ce9d0f7b32e5ff6d1da2857bcdf4746134c422282b091c672cde0572cac3840713487e0a7a636af9aa1b74cb11894b447a521efa
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"findup-sync@npm:^3.0.0":
  version: 3.0.0
  resolution: "findup-sync@npm:3.0.0"
  dependencies:
    detect-file: ^1.0.0
    is-glob: ^4.0.0
    micromatch: ^3.0.4
    resolve-dir: ^1.0.1
  checksum: ff6f37328a7629775db2abf0fcd40e7c117baf37f23006f206c18bcd9ca0ce99d8c24ae86df540370ec76c1080ab59fe82cb71d2c7c1ad819ccccee726af7e92
  languageName: node
  linkType: hard

"findup-sync@npm:^4.0.0":
  version: 4.0.0
  resolution: "findup-sync@npm:4.0.0"
  dependencies:
    detect-file: ^1.0.0
    is-glob: ^4.0.0
    micromatch: ^4.0.2
    resolve-dir: ^1.0.1
  checksum: 3e7de4d0afda35ecdd6260ce9d31524161817466ad6218b092dc73554848ce9618b69ec0f841dc82e320a4b3bfaba19c71c154f5b249ffed28143ba95a743d37
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.3
    rimraf: ^3.0.2
  checksum: b76f611bd5f5d68f7ae632e3ae503e678d205cf97a17c6ab5b12f6ca61188b5f1f7464503efae6dc18683ed8f0b41460beb48ac4b9ac63fe6201296a91ba2f75
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"for-in@npm:^1.0.2":
  version: 1.0.2
  resolution: "for-in@npm:1.0.2"
  checksum: 42bb609d564b1dc340e1996868b67961257fd03a48d7fdafd4f5119530b87f962be6b4d5b7e3a3fc84c9854d149494b1d358e0b0ce9837e64c4c6603a49451d6
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 028f1d41000553fcfa6c4bb5c372963bf3d9bf0b1f25a87d1a6253014343fb69dfb1b42d9625d7cf44c8ba429940f3d0ff718b62105d4d4a4f6ef8ca0a53faa2
  languageName: node
  linkType: hard

"fork-ts-checker-webpack-plugin@npm:7.2.11":
  version: 7.2.11
  resolution: "fork-ts-checker-webpack-plugin@npm:7.2.11"
  dependencies:
    "@babel/code-frame": ^7.16.7
    chalk: ^4.1.2
    chokidar: ^3.5.3
    cosmiconfig: ^7.0.1
    deepmerge: ^4.2.2
    fs-extra: ^10.0.0
    memfs: ^3.4.1
    minimatch: ^3.0.4
    schema-utils: ^3.1.1
    semver: ^7.3.5
    tapable: ^2.2.1
  peerDependencies:
    typescript: ">3.6.0"
    vue-template-compiler: "*"
    webpack: ^5.11.0
  peerDependenciesMeta:
    vue-template-compiler:
      optional: true
  checksum: 84f600a8f55e78ec6edac249090a6761db2f93d8c6af18e195f176bb9b0de263ef4b1f9b29abcc3dffb4751e3e8bb4dff96ada24095995d82af0f058d43c1fd6
  languageName: node
  linkType: hard

"fork-ts-checker-webpack-plugin@npm:7.3.0":
  version: 7.3.0
  resolution: "fork-ts-checker-webpack-plugin@npm:7.3.0"
  dependencies:
    "@babel/code-frame": ^7.16.7
    chalk: ^4.1.2
    chokidar: ^3.5.3
    cosmiconfig: ^7.0.1
    deepmerge: ^4.2.2
    fs-extra: ^10.0.0
    memfs: ^3.4.1
    minimatch: ^3.0.4
    node-abort-controller: ^3.0.1
    schema-utils: ^3.1.1
    semver: ^7.3.5
    tapable: ^2.2.1
  peerDependencies:
    typescript: ">3.6.0"
    vue-template-compiler: "*"
    webpack: ^5.11.0
  peerDependenciesMeta:
    vue-template-compiler:
      optional: true
  checksum: 00a3dad0815178db485317d8909dc1171c0bb97e43dac004a74048b36ddc0260db188fcb5eebb01a54fb280a82acf55e5a5d09e1e55ffa80b77ad41e5c8ba539
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    es-set-tostringtag: ^2.1.0
    mime-types: ^2.1.12
  checksum: e534b0cf025c831a0929bf4b9bbe1a9a6b03e273a8161f9947286b9b13bf8fb279c6944aae0070c4c311100c6d6dbb815cd955dc217728caf73fad8dc5b8ee9c
  languageName: node
  linkType: hard

"formidable@npm:^2.1.2":
  version: 2.1.2
  resolution: "formidable@npm:2.1.2"
  dependencies:
    dezalgo: ^1.0.4
    hexoid: ^1.0.0
    once: ^1.4.0
    qs: ^6.11.0
  checksum: efba03d11127098daa6ef54c3c0fad25693973eb902fa88ccaaa203baebe8c74d12ba0fe1e113eccf79b9172510fa337e4e107330b124fb3a8c74697b4aa2ce3
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fragment-cache@npm:^0.2.1":
  version: 0.2.1
  resolution: "fragment-cache@npm:0.2.1"
  dependencies:
    map-cache: ^0.2.2
  checksum: 5891d1c1d1d5e1a7fb3ccf28515c06731476fa88f7a50f4ede8a0d8d239a338448e7f7cc8b73db48da19c229fa30066104fe6489862065a4f1ed591c42fbeabf
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: a0cde99085f0872f4d244e83e03a46aa387b74f5a5af750896c6b05e9077fac00e9932fdf5aef84f2f16634cd473c63037d7a512576da7d5c2b9163d1909f3a8
  languageName: node
  linkType: hard

"fs-extra@npm:10.1.0, fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: 5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-extra@npm:8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: 259f7b814d9e50d686899550c4f9ded85c46c643f7fe19be69504888e007fcbc08f306fae8ec495b8b998635e997c9e3e175ff2eeed230524ef1c1684cc96423
  languageName: node
  linkType: hard

"fs-extra@npm:9.1.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: ^1.0.0
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: 9b808bd884beff5cb940773018179a6b94a966381d005479f00adda6b44e5e3d4abf765135773d849cc27efe68c349e4a7b86acd7d3306d5932c14f3a4b17a92
  languageName: node
  linkType: hard

"fs-extra@npm:^11.0.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: 5f95e996186ff45463059feb115a22fb048bdaf7e487ecee8a8646c78ed8fdca63630e3077d4c16ce677051f5e60d3355a06f3cd61f3ca43f48cc58822a44d0a
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs-monkey@npm:^1.0.4":
  version: 1.0.6
  resolution: "fs-monkey@npm:1.0.6"
  checksum: 6f2508e792a47e37b7eabd5afc79459c1ea72bce2a46007d2b7ed0bfc3a4d64af38975c6eb7e93edb69ac98bbb907c13ff1b1579b2cf52d3d02dbc0303fca79f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@^2.3.2#~builtin<compat/fsevents>, fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"fstream@npm:^1.0.12":
  version: 1.0.12
  resolution: "fstream@npm:1.0.12"
  dependencies:
    graceful-fs: ^4.1.2
    inherits: ~2.0.0
    mkdirp: ">=0.5 0"
    rimraf: 2
  checksum: f52a0687a0649c6b93973eb7f1d5495e445fa993f797ba1af186e666b6aebe53916a8c497dce7037c74d0d4a33c56b0ab1f98f10ad71cca84ba8661110d25ee2
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"gauge@npm:^3.0.0":
  version: 3.0.2
  resolution: "gauge@npm:3.0.2"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.2
    console-control-strings: ^1.0.0
    has-unicode: ^2.0.1
    object-assign: ^4.1.1
    signal-exit: ^3.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.2
  checksum: 75230ccaf216471e31025c7d5fcea1629596ca20792de50c596eb18ffb14d8404f927cd55535aab2eeecd18d1e11bd6f23ec3c2e9878d2dda1dc74bccc34b913
  languageName: node
  linkType: hard

"gaxios@npm:^6.0.0, gaxios@npm:^6.1.1":
  version: 6.7.1
  resolution: "gaxios@npm:6.7.1"
  dependencies:
    extend: ^3.0.2
    https-proxy-agent: ^7.0.1
    is-stream: ^2.0.0
    node-fetch: ^2.6.9
    uuid: ^9.0.1
  checksum: 53e92088470661c5bc493a1de29d05aff58b1f0009ec5e7903f730f892c3642a93e264e61904383741ccbab1ce6e519f12a985bba91e13527678b32ee6d7d3fd
  languageName: node
  linkType: hard

"gcp-metadata@npm:^6.1.0":
  version: 6.1.1
  resolution: "gcp-metadata@npm:6.1.1"
  dependencies:
    gaxios: ^6.1.1
    google-logging-utils: ^0.0.2
    json-bigint: ^1.0.0
  checksum: 71f6ad4800aa622c246ceec3955014c0c78cdcfe025971f9558b9379f4019f5e65772763428ee8c3244fa81b8631977316eaa71a823493f82e5c44d7259ffac8
  languageName: node
  linkType: hard

"generate-function@npm:^2.3.1":
  version: 2.3.1
  resolution: "generate-function@npm:2.3.1"
  dependencies:
    is-property: ^1.0.2
  checksum: 4645cf1da90375e46a6f1dc51abc9933e5eafa4cd1a44c2f7e3909a30a4e9a1a08c14cd7d5b32da039da2dba2a085e1ed4597b580c196c3245b2d35d8bc0de5d
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    function-bind: ^1.1.2
    get-proto: ^1.0.0
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: b475dec9f8bff6f7422f51ff4b7b8d0b68e6776ee83a753c1d627e3008c3442090992788038b37eff72e93e43dceed8c1acbdf2d6751672687ec22127933080d
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^5.0.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 43797ffd815fbb26685bf188c8cfebecb8af87b3925091dd7b9a9c915993293d78e3c9e1bce125928ff92f2d0796f3889b92b5ec6d58d1041b574682132e0a80
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"get-value@npm:^2.0.3, get-value@npm:^2.0.6":
  version: 2.0.6
  resolution: "get-value@npm:2.0.6"
  checksum: f069c132791b357c8fc4adfe9e2929b0a2c6e95f98ca7bc6fcbc27f8a302e552f86b4ae61ec56d9e9ac2544b93b6a39743d479866a37b43fcc104088ba74f0d9
  languageName: node
  linkType: hard

"git-raw-commits@npm:^2.0.11":
  version: 2.0.11
  resolution: "git-raw-commits@npm:2.0.11"
  dependencies:
    dargs: ^7.0.0
    lodash: ^4.17.15
    meow: ^8.0.0
    split2: ^3.0.0
    through2: ^4.0.0
  bin:
    git-raw-commits: cli.js
  checksum: c9cee7ce11a6703098f028d7e47986d5d3e4147d66640086734d6ee2472296b8711f91b40ad458e95acac1bc33cf2898059f1dc890f91220ff89c5fcc609ab64
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: 317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: 0486925072d7a916f052842772b61c3e86247f0a80cc0deb9b5a3e8a1a9faad5b04fb6f58986a09f34d3e96cd2a22a24b7e9882fb1cf904c31e9a310de96c429
  languageName: node
  linkType: hard

"glob@npm:7.1.4":
  version: 7.1.4
  resolution: "glob@npm:7.1.4"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 7f6fcbf600eb2298cce34c65f6d8bbe6933ddd4f88aa5b38a9c6feec82b615bb33b63b120725303e89c4b50284413c21d2ff883414717a5c7d0c9f7cd7a0e5fe
  languageName: node
  linkType: hard

"glob@npm:7.2.3, glob@npm:^7.0.0, glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.2.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"global-directory@npm:^4.0.1":
  version: 4.0.1
  resolution: "global-directory@npm:4.0.1"
  dependencies:
    ini: 4.1.1
  checksum: f9cbeef41db4876f94dd0bac1c1b4282a7de9c16350ecaaf83e7b2dd777b32704cc25beeb1170b5a63c42a2c9abfade74d46357fe0133e933218bc89e613d4b2
  languageName: node
  linkType: hard

"global-dirs@npm:^0.1.1":
  version: 0.1.1
  resolution: "global-dirs@npm:0.1.1"
  dependencies:
    ini: ^1.3.4
  checksum: 3608072e58962396c124ad5a1cfb3f99ee76c998654a3432d82977b3c3eeb09dc8a5a2a9849b2b8113906c8d0aad89ce362c22e97cec5fe34405bbf4f3cdbe7a
  languageName: node
  linkType: hard

"global-modules@npm:^1.0.0":
  version: 1.0.0
  resolution: "global-modules@npm:1.0.0"
  dependencies:
    global-prefix: ^1.0.1
    is-windows: ^1.0.1
    resolve-dir: ^1.0.0
  checksum: 7d91ecf78d4fcbc966b2d89c1400df273afea795bc8cadf39857ee1684e442065621fd79413ff5fcd9e90c6f1b2dc0123e644fa0b7811f987fd54c6b9afad858
  languageName: node
  linkType: hard

"global-prefix@npm:^1.0.1":
  version: 1.0.2
  resolution: "global-prefix@npm:1.0.2"
  dependencies:
    expand-tilde: ^2.0.2
    homedir-polyfill: ^1.0.1
    ini: ^1.3.4
    is-windows: ^1.0.1
    which: ^1.2.14
  checksum: d8037e300f1dc04d5d410d16afa662e71bfad22dcceba6c9727bb55cc273b8988ca940b3402f62e5392fd261dd9924a9a73a865ef2000219461f31f3fc86be06
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: d3c11aeea898eb83d5ec7a99508600fbe8f83d2cf00cbb77f873dbf2bcb39428eff1b538e4915c993d8a3b3473fa71eeebfe22c9bb3a3003d1e26b1f2c8a42cd
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"google-auth-library@npm:^9.9.0":
  version: 9.15.1
  resolution: "google-auth-library@npm:9.15.1"
  dependencies:
    base64-js: ^1.3.0
    ecdsa-sig-formatter: ^1.0.11
    gaxios: ^6.1.1
    gcp-metadata: ^6.1.0
    gtoken: ^7.0.0
    jws: ^4.0.0
  checksum: 6eef36d9a9cb7decd11e920ee892579261c6390104b3b24d3e0f3889096673189fe2ed0ee43fd563710e2560de98e63ad5aa4967b91e7f4e69074a422d5f7b65
  languageName: node
  linkType: hard

"google-logging-utils@npm:^0.0.2":
  version: 0.0.2
  resolution: "google-logging-utils@npm:0.0.2"
  checksum: 9a4bbd470dd101c77405e450fffca8592d1d7114f245a121288d04a957aca08c9dea2dd1a871effe71e41540d1bb0494731a0b0f6fea4358e77f06645e4268c1
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.2, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"gtoken@npm:^7.0.0":
  version: 7.1.0
  resolution: "gtoken@npm:7.1.0"
  dependencies:
    gaxios: ^6.0.0
    jws: ^4.0.0
  checksum: 0a3dcacb1a3c4578abe1ee01c7d0bf20bffe8ded3ee73fc58885d53c00f6eb43b4e1372ff179f0da3ed5cfebd5b7c6ab8ae2776f1787e90d943691b4fe57c716
  languageName: node
  linkType: hard

"hard-rejection@npm:^2.1.0":
  version: 2.1.0
  resolution: "hard-rejection@npm:2.1.0"
  checksum: febc3343a1ad575aedcc112580835b44a89a89e01f400b4eda6e8110869edfdab0b00cd1bd4c3bfec9475a57e79e0b355aecd5be46454b6a62b9a359af60e564
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: ebdb2f4895c26bb08a8a100b62d362e49b2190bcfd84b76bc4be1a3bd4d254ec52d0dd9f2fbcc093fc5eb878b20c52146f9dfd33e2686ed28982187be593b47c
  languageName: node
  linkType: hard

"has-value@npm:^0.3.1":
  version: 0.3.1
  resolution: "has-value@npm:0.3.1"
  dependencies:
    get-value: ^2.0.3
    has-values: ^0.1.4
    isobject: ^2.0.0
  checksum: 7a7c2e9d07bc9742c81806150adb154d149bc6155267248c459cd1ce2a64b0759980d26213260e4b7599c8a3754551179f155ded88d0533a0d2bc7bc29028432
  languageName: node
  linkType: hard

"has-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-value@npm:1.0.0"
  dependencies:
    get-value: ^2.0.6
    has-values: ^1.0.0
    isobject: ^3.0.0
  checksum: 17cdccaf50f8aac80a109dba2e2ee5e800aec9a9d382ef9deab66c56b34269e4c9ac720276d5ffa722764304a1180ae436df077da0dd05548cfae0209708ba4d
  languageName: node
  linkType: hard

"has-values@npm:^0.1.4":
  version: 0.1.4
  resolution: "has-values@npm:0.1.4"
  checksum: a8f00ad862c20289798c35243d5bd0b0a97dd44b668c2204afe082e0265f2d0bf3b89fc8cc0ef01a52b49f10aa35cf85c336ee3a5f1cac96ed490f5e901cdbf2
  languageName: node
  linkType: hard

"has-values@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-values@npm:1.0.0"
  dependencies:
    is-number: ^3.0.0
    kind-of: ^4.0.0
  checksum: a6f2a1cc6b2e43eacc68e62e71ad6890def7f4b13d2ef06b4ad3ee156c23e470e6df144b9b467701908e17633411f1075fdff0cab45fb66c5e0584d89b25f35e
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: 3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hexoid@npm:^1.0.0":
  version: 1.0.0
  resolution: "hexoid@npm:1.0.0"
  checksum: 9c45e8ba676b9eb88455631ebceec4c829a8374a583410dc735472ab9808bf11339fcd074633c3fa30e420901b894d8a92ffd5e2e21eddd41149546e05a91f69
  languageName: node
  linkType: hard

"highlight.js@npm:^10.7.1":
  version: 10.7.3
  resolution: "highlight.js@npm:10.7.3"
  checksum: 073837eaf816922427a9005c56c42ad8786473dc042332dfe7901aa065e92bc3d94ebf704975257526482066abb2c8677cc0326559bb8621e046c21c5991c434
  languageName: node
  linkType: hard

"homedir-polyfill@npm:^1.0.1":
  version: 1.0.3
  resolution: "homedir-polyfill@npm:1.0.3"
  dependencies:
    parse-passwd: ^1.0.0
  checksum: 3c099844f94b8b438f124bd5698bdcfef32b2d455115fb8050d7148e7f7b95fc89ba9922586c491f0e1cdebf437b1053c84ecddb8d596e109e9ac69c5b4a9e27
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: 317cbc6b1bbbe23c2a40ae23f3dafe9fa349ce42a89a36f930e3f9c0530c179a3882d2ef1e4141a4c3674d6faaea862138ec55b43ad6f75e387fda2483a13c70
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.0.1":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: ^6.0.0
  checksum: 150fbcb001600336d17fdbae803264abed013548eea7946c2264c49ebe2ebd8c4441ba71dd23dd8e18c65de79d637f98b22d4760ba5fb2e0b15d62543d0fff07
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^1.1.1":
  version: 1.1.1
  resolution: "human-signals@npm:1.1.1"
  checksum: 18810ed239a7a5e23fb6c32d0fd4be75d7cd337a07ad59b8dbf0794cb0761e6e628349ee04c409e605fe55344716eab5d0a47a62ba2a2d0d367c89a2b4247b1e
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"husky@npm:7.0.4":
  version: 7.0.4
  resolution: "husky@npm:7.0.4"
  bin:
    husky: lib/bin.js
  checksum: aacb2b8fbfed0ec161f94e9b08d422c51fec073def4e165e57da42f47c10f520a5f0a88b42efc667784e314a1af83cf1994b582cd6f4b0333739921a601c6187
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13, ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore-by-default@npm:^1.0.1":
  version: 1.0.1
  resolution: "ignore-by-default@npm:1.0.1"
  checksum: 9ab6e70e80f7cc12735def7ecb5527cfa56ab4e1152cd64d294522827f2dcf1f6d85531241537dc3713544e88dd888f65cb3c49c7b2cddb9009087c75274e533
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: f8ba7ede69bee9260241ad078d2d535848745ff5f6995c7c7cb41cfdc9ccc213f66e10fa5afb881f90298b24a3f7344b637b592beb4f54e582770cdce3f1f039
  languageName: node
  linkType: hard

"import-fresh@npm:^3.0.0, import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 94cd6367a672b7e0cb026970c85b76902d2710a64896fa6de93bd5c571dd03b228c5759308959de205083e3b1c61e799f019c9e36ee8e9c523b993e1057f0433
  languageName: node
  linkType: hard

"import-meta-resolve@npm:^4.0.0":
  version: 4.1.0
  resolution: "import-meta-resolve@npm:4.1.0"
  checksum: 42f3284b0460635ddf105c4ad99c6716099c3ce76702602290ad5cbbcd295700cbc04e4bdf47bacf9e3f1a4cec2e1ff887dabc20458bef398f9de22ddff45ef5
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: 7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.0, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ini@npm:4.1.1":
  version: 4.1.1
  resolution: "ini@npm:4.1.1"
  checksum: 7fddc8dfd3e63567d4fdd5d999d1bf8a8487f1479d0b34a1d01f28d391a9228d261e19abc38e1a6a1ceb3400c727204fce05725d5eb598dfcf2077a1e3afe211
  languageName: node
  linkType: hard

"ini@npm:^1.3.4":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"inquirer@npm:6.5.0":
  version: 6.5.0
  resolution: "inquirer@npm:6.5.0"
  dependencies:
    ansi-escapes: ^3.2.0
    chalk: ^2.4.2
    cli-cursor: ^2.1.0
    cli-width: ^2.0.0
    external-editor: ^3.0.3
    figures: ^2.0.0
    lodash: ^4.17.12
    mute-stream: 0.0.7
    run-async: ^2.2.0
    rxjs: ^6.4.0
    string-width: ^2.1.0
    strip-ansi: ^5.1.0
    through: ^2.3.6
  checksum: e6f2c169ac35c3dd7147685122c907b9630521f6b928c4488e35b3a32eb485a72b2546e216d4319fd423d7530d000f2b6082b618f481577168aa4a980215cd71
  languageName: node
  linkType: hard

"inquirer@npm:7.3.3":
  version: 7.3.3
  resolution: "inquirer@npm:7.3.3"
  dependencies:
    ansi-escapes: ^4.2.1
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-width: ^3.0.0
    external-editor: ^3.0.3
    figures: ^3.0.0
    lodash: ^4.17.19
    mute-stream: 0.0.8
    run-async: ^2.4.0
    rxjs: ^6.6.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
    through: ^2.3.6
  checksum: 96e75974cfd863fe6653c075e41fa5f1a290896df141189816db945debabcd92d3277145f11aef8d2cfca5409ab003ccdd18a099744814057b52a2f27aeb8c94
  languageName: node
  linkType: hard

"inquirer@npm:8.2.0":
  version: 8.2.0
  resolution: "inquirer@npm:8.2.0"
  dependencies:
    ansi-escapes: ^4.2.1
    chalk: ^4.1.1
    cli-cursor: ^3.1.0
    cli-width: ^3.0.0
    external-editor: ^3.0.3
    figures: ^3.0.0
    lodash: ^4.17.21
    mute-stream: 0.0.8
    ora: ^5.4.1
    run-async: ^2.4.0
    rxjs: ^7.2.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
    through: ^2.3.6
  checksum: efdd5e40c70fb66a8f87b39d75130cd4d1f8097338c73720eb254e3b66a36a17e136fdad6830aec22a2fdaaf426f0068cb31093ee8b8b60154177babd6c6ea2f
  languageName: node
  linkType: hard

"inquirer@npm:8.2.4":
  version: 8.2.4
  resolution: "inquirer@npm:8.2.4"
  dependencies:
    ansi-escapes: ^4.2.1
    chalk: ^4.1.1
    cli-cursor: ^3.1.0
    cli-width: ^3.0.0
    external-editor: ^3.0.3
    figures: ^3.0.0
    lodash: ^4.17.21
    mute-stream: 0.0.8
    ora: ^5.4.1
    run-async: ^2.4.0
    rxjs: ^7.5.5
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
    through: ^2.3.6
    wrap-ansi: ^7.0.0
  checksum: e8c6185548a2da6a04b6d2096d9173451ae8aa01432bfd8a5ffcd29fb871ed7764419a4fd693fbfb99621891b54c131f5473f21660d4808d25c6818618f2de73
  languageName: node
  linkType: hard

"inquirer@npm:8.2.5":
  version: 8.2.5
  resolution: "inquirer@npm:8.2.5"
  dependencies:
    ansi-escapes: ^4.2.1
    chalk: ^4.1.1
    cli-cursor: ^3.1.0
    cli-width: ^3.0.0
    external-editor: ^3.0.3
    figures: ^3.0.0
    lodash: ^4.17.21
    mute-stream: 0.0.8
    ora: ^5.4.1
    run-async: ^2.4.0
    rxjs: ^7.5.5
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
    through: ^2.3.6
    wrap-ansi: ^7.0.0
  checksum: e3e64e10f5daeeb8f770f1310acceb4aab593c10d693e7676ecd4a5b023d5b865b484fec7ead516e5e394db70eff687ef85459f75890f11a99ceadc0f4adce18
  languageName: node
  linkType: hard

"inquirer@npm:^6.5.2":
  version: 6.5.2
  resolution: "inquirer@npm:6.5.2"
  dependencies:
    ansi-escapes: ^3.2.0
    chalk: ^2.4.2
    cli-cursor: ^2.1.0
    cli-width: ^2.0.0
    external-editor: ^3.0.3
    figures: ^2.0.0
    lodash: ^4.17.12
    mute-stream: 0.0.7
    run-async: ^2.2.0
    rxjs: ^6.4.0
    string-width: ^2.1.0
    strip-ansi: ^5.1.0
    through: ^2.3.6
  checksum: a5aa53a8f88405cf1cff63111493f87a5b3b5deb5ea4a0dbcd73ccc06a51a6bba0c3f1a0747f8880e9e3ec2437c69f90417be16368abf636b1d29eebe35db0ac
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 08c5ad30032edeec638485bc3f6db7d0094d9b3e85e0f950866600af3c52e9fd69715416d29564731c479d9f4d43ff3e4d302a178196bdc0e6837ec147640450
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: 331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-accessor-descriptor@npm:1.0.1"
  dependencies:
    hasown: ^2.0.0
  checksum: d034034074c5ffeb6c868e091083182279db1a956f49f8d1494cecaa0f8b99d706556ded2a9b20d9aa290549106eef8204d67d8572902e06dcb1add6db6b524d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-buffer@npm:^1.1.5":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: ae18aa0b6e113d6c490ad1db5e8df9bdb57758382b313f5a22c9c61084875c6396d50bbf49315f5b1926d142d74dfb8d31b40d993a383e0a158b15fea7a82234
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0, is-core-module@npm:^2.5.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-data-descriptor@npm:1.0.1"
  dependencies:
    hasown: ^2.0.0
  checksum: ad3acc372e3227f87eb8cdba112c343ca2a67f1885aecf64f02f901cb0858a1fc9488ad42135ab102e9d9e71a62b3594740790bb103a9ba5da830a131a89e3e8
  languageName: node
  linkType: hard

"is-descriptor@npm:^0.1.0":
  version: 0.1.7
  resolution: "is-descriptor@npm:0.1.7"
  dependencies:
    is-accessor-descriptor: ^1.0.1
    is-data-descriptor: ^1.0.1
  checksum: f5960b9783f508aec570465288cb673d4b3cc4aae4e6de970c3afd9a8fc1351edcb85d78b2cce2ec5251893a423f73263cab3bb94cf365a8d71b5d510a116392
  languageName: node
  linkType: hard

"is-descriptor@npm:^1.0.0, is-descriptor@npm:^1.0.2":
  version: 1.0.3
  resolution: "is-descriptor@npm:1.0.3"
  dependencies:
    is-accessor-descriptor: ^1.0.1
    is-data-descriptor: ^1.0.1
  checksum: b4ee667ea787d3a0be4e58536087fd0587de2b0b6672fbfe288f5b8d831ac4b79fd987f31d6c2d4e5543a42c97a87428bc5215ce292a1a47070147793878226f
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0, is-extendable@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: dd5ca3994a28e1740d1e25192e66eed128e0b2ff161a7ea348e87ae4f616554b486854de423877a2a2c171d5f7cd6e8093b91f54533bc88a59ee1c9838c43879
  languageName: node
  linkType: hard

"is-extendable@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-extendable@npm:1.0.1"
  dependencies:
    is-plain-object: ^2.0.4
  checksum: 1d6678a5be1563db6ecb121331c819c38059703f0179f52aa80c242c223ee9c6b66470286636c0e63d7163e4d905c0a7d82a096e0b5eaeabb51b9f8d0af0d73f
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: e58f3e4a601fc0500d8b2677e26e9fe0cd450980e66adb29d85b6addf7969731e38f8e43ed2ec868a09c101a55ac3d8b78902209269f38c5286bc98f5bc1b4d9
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-fullwidth-code-point@npm:4.0.0"
  checksum: df2a717e813567db0f659c306d61f2f804d480752526886954a2a3e2246c7745fd07a52b5fecf2b68caf0a6c79dcdace6166fdf29cc76ed9975cc334f0a018b8
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: 2957cab387997a466cd0bf5c1b6047bd21ecb32bdcfd8996b15747aa01002c1c88731802f1b3d34ac99f4f6874b626418bd118658cf39380fe5fff32a3af9c4d
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: 17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: dd47904dbf286cd20aa58c5192161be1a67138485b9836d5a70433b21a45442e9611b8498b8ab1f839fc962c7620667a50535fdfb4a6bc7989b8858645c06b4d
  languageName: node
  linkType: hard

"is-number@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-number@npm:3.0.0"
  dependencies:
    kind-of: ^3.0.2
  checksum: e639c54640b7f029623df24d3d103901e322c0c25ea5bde97cd723c2d0d4c05857a8364ab5c58d963089dbed6bf1d0ffe975cb6aef917e2ad0ccbca653d31b4f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: 85044ed7ba8bd169e2c2af3a178cacb92a97aa75de9569d02efef7f443a824b5e153eba72b9ae3aca6f8ce81955271aa2dc7da67a8b720575d3e38104208cb4e
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: cf7d4ac35fb96bab6a1d2c3598fe5ebb29aafb52c0aaa482b5a3ed9d8ba3edc11631e3ec2637660c44b3ce0e61a08d54946e8af30dec0b60a7c27296c68ffd05
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: daaee1805add26f781b413fdf192fc91d52409583be30ace35c82607d440da63cc4cac0ac55136716688d6c0a2c6ef3edb2254fecbd1fe06056d6bd15975ee8c
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.3, is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-property@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-property@npm:1.0.2"
  checksum: 33ab65a136e4ba3f74d4f7d9d2a013f1bd207082e11cedb160698e8d5394644e873c39668d112a402175ccbc58a087cef87198ed46829dbddb479115a0257283
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-text-path@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-text-path@npm:1.0.1"
  dependencies:
    text-extensions: ^1.0.0
  checksum: 61c8650c29548febb6bf69e9541fc11abbbb087a0568df7bc471ba264e95fb254def4e610631cbab4ddb0a1a07949d06416f4ebeaf37875023fb184cdb87ee84
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: 00cbe3455c3756be68d2542c416cab888aebd5012781d6819749fefb15162ff23e38501fe681b3d751c73e8ff561ac09a5293eba6f58fdf0178462ce6dcb3453
  languageName: node
  linkType: hard

"is-utf8@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-utf8@npm:0.2.1"
  checksum: 3ed45e5b4ddfa04ed7e32c63d29c61b980ecd6df74698f45978b8c17a54034943bcbffb6ae243202e799682a66f90fef526f465dd39438745e9fe70794c1ef09
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.1, is-windows@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: b32f418ab3385604a66f1b7a3ce39d25e8881dee0bd30816dc8344ef6ff9df473a732bcc1ec4e84fe99b2f229ae474f7133e8e93f9241686cfcf7eebe53ba7a5
  languageName: node
  linkType: hard

"isarray@npm:1.0.0, isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^2.0.0":
  version: 2.1.0
  resolution: "isobject@npm:2.1.0"
  dependencies:
    isarray: 1.0.0
  checksum: c4cafec73b3b2ee11be75dff8dafd283b5728235ac099b07d7873d5182553a707768e208327bbc12931b9422d8822280bf88d894a0024ff5857b3efefb480e7b
  languageName: node
  linkType: hard

"isobject@npm:^3.0.0, isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4, istanbul-lib-instrument@npm:^5.1.0":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": ^7.12.3
    "@babel/parser": ^7.14.7
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-coverage: ^3.2.0
    semver: ^6.3.0
  checksum: 8a1bdf3e377dcc0d33ec32fe2b6ecacdb1e4358fd0eb923d4326bb11c67622c0ceb99600a680f3dad5d29c66fc1991306081e339b4d43d0b8a2ab2e1d910a6ee
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: ^3.0.0
    make-dir: ^4.0.0
    supports-color: ^7.1.0
  checksum: 84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: ^4.1.1
    istanbul-lib-coverage: ^3.0.0
    source-map: ^0.6.1
  checksum: 19e4cc405016f2c906dff271a76715b3e881fa9faeb3f09a86cb99b8512b3a5ed19cadfe0b54c17ca0e54c1142c9c6de9330d65506e35873994e06634eebeb66
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: ^2.0.0
    istanbul-lib-report: ^3.0.0
  checksum: a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"iterare@npm:1.2.1":
  version: 1.2.1
  resolution: "iterare@npm:1.2.1"
  checksum: 02667d486e3e83ead028ba8484d927498c2ceab7e8c6a69dd881fd02abc4114f00b13abb36b592252fbb578b6e6f99ca1dfc2835408b9158c9a112a9964f453f
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jest-changed-files@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-changed-files@npm:28.1.3"
  dependencies:
    execa: ^5.0.0
    p-limit: ^3.1.0
  checksum: fec92f6348456c3157ac74abcfe8b341d7d8ddbb51efc1bc7d76b9e613c6a0b1bf627b505b5f49ec4d7829885a6cf2615920eeeda7f55bc0aed4695cf02e1085
  languageName: node
  linkType: hard

"jest-circus@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-circus@npm:28.1.3"
  dependencies:
    "@jest/environment": ^28.1.3
    "@jest/expect": ^28.1.3
    "@jest/test-result": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/node": "*"
    chalk: ^4.0.0
    co: ^4.6.0
    dedent: ^0.7.0
    is-generator-fn: ^2.0.0
    jest-each: ^28.1.3
    jest-matcher-utils: ^28.1.3
    jest-message-util: ^28.1.3
    jest-runtime: ^28.1.3
    jest-snapshot: ^28.1.3
    jest-util: ^28.1.3
    p-limit: ^3.1.0
    pretty-format: ^28.1.3
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 6f20ff8b5f100c7bafb6f71a2bd42e81804f0af848d628864508340239c56957a731bcdd83dba3e962a81c1f05ce9daa4ecee207a02e0ec73a908a2ec62f1f19
  languageName: node
  linkType: hard

"jest-cli@npm:^28.1.2":
  version: 28.1.3
  resolution: "jest-cli@npm:28.1.3"
  dependencies:
    "@jest/core": ^28.1.3
    "@jest/test-result": ^28.1.3
    "@jest/types": ^28.1.3
    chalk: ^4.0.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    import-local: ^3.0.2
    jest-config: ^28.1.3
    jest-util: ^28.1.3
    jest-validate: ^28.1.3
    prompts: ^2.0.1
    yargs: ^17.3.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 7d47b89785fd6cf7c21560fcf09280bfb80057e3e7f85d4da2828d780a6ff81a1a41611e55eb3831564530edc3060159d23fd20d60d6640161d4652233c0c6a3
  languageName: node
  linkType: hard

"jest-config@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-config@npm:28.1.3"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/test-sequencer": ^28.1.3
    "@jest/types": ^28.1.3
    babel-jest: ^28.1.3
    chalk: ^4.0.0
    ci-info: ^3.2.0
    deepmerge: ^4.2.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-circus: ^28.1.3
    jest-environment-node: ^28.1.3
    jest-get-type: ^28.0.2
    jest-regex-util: ^28.0.2
    jest-resolve: ^28.1.3
    jest-runner: ^28.1.3
    jest-util: ^28.1.3
    jest-validate: ^28.1.3
    micromatch: ^4.0.4
    parse-json: ^5.2.0
    pretty-format: ^28.1.3
    slash: ^3.0.0
    strip-json-comments: ^3.1.1
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: d5c160e22036f14aaf2e48a72d69d31aa4f499be204e8d97e88b06f913dc93c0f55d3bb9deef8519481365349db91e1803353fe62e7ceba439cd650083a0a0e4
  languageName: node
  linkType: hard

"jest-diff@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-diff@npm:28.1.3"
  dependencies:
    chalk: ^4.0.0
    diff-sequences: ^28.1.1
    jest-get-type: ^28.0.2
    pretty-format: ^28.1.3
  checksum: 17a101ceb7e8f25c3ef64edda15cb1a259c2835395637099f3cc44f578fbd94ced7a13d11c0cbe8c5c1c3959a08544f0a913bec25a305b6dfc9847ce488e7198
  languageName: node
  linkType: hard

"jest-docblock@npm:^28.1.1":
  version: 28.1.1
  resolution: "jest-docblock@npm:28.1.1"
  dependencies:
    detect-newline: ^3.0.0
  checksum: 147b7e537ff025c0be2909192c56fb9bfda09bc2603075491798bd0315d503687efa7c75131f2909a0fde30af9dc309b7ef58eb21413e2380c471b3db133949a
  languageName: node
  linkType: hard

"jest-each@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-each@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    chalk: ^4.0.0
    jest-get-type: ^28.0.2
    jest-util: ^28.1.3
    pretty-format: ^28.1.3
  checksum: 63e1ecf86297085527b369a517af3dba8614937adc1870de041f6f0c3d5dff4d60d94be32949cf9945d9ce401bd28bea2c5efa9e090c39777cfd1627b71d6bc7
  languageName: node
  linkType: hard

"jest-environment-node@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-environment-node@npm:28.1.3"
  dependencies:
    "@jest/environment": ^28.1.3
    "@jest/fake-timers": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/node": "*"
    jest-mock: ^28.1.3
    jest-util: ^28.1.3
  checksum: d7d313ee28d6063f0740cf5dd94f3ae206f0897ac8e562e52159ec1b26c24233c75893b3cbf1b885dcc8abb50e82a20d07f77c28917be8fd20156dd15602892f
  languageName: node
  linkType: hard

"jest-get-type@npm:^28.0.2":
  version: 28.0.2
  resolution: "jest-get-type@npm:28.0.2"
  checksum: f64a40cfa10d79a56b383919033d35c8c4daee6145a1df31ec5ef2283fa7e8adbd443c6fcb4cfd0f60bbbd89f046c2323952f086b06e875cbbbc1a7d543a6e5e
  languageName: node
  linkType: hard

"jest-haste-map@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-haste-map@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    "@types/graceful-fs": ^4.1.3
    "@types/node": "*"
    anymatch: ^3.0.3
    fb-watchman: ^2.0.0
    fsevents: ^2.3.2
    graceful-fs: ^4.2.9
    jest-regex-util: ^28.0.2
    jest-util: ^28.1.3
    jest-worker: ^28.1.3
    micromatch: ^4.0.4
    walker: ^1.0.8
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 6a2beedd31f5d67b508d57fbfdd8858cfbd2f59a61737fc74cac4b9f60120faeda8c40189afba331324b08e10bc2281521292cdb6713fb3cab7770828f4e83d9
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-leak-detector@npm:28.1.3"
  dependencies:
    jest-get-type: ^28.0.2
    pretty-format: ^28.1.3
  checksum: 038cca2fa8cb24ede34834308c86eca40a6c20f02ad5b81d059072c444c421c60058c2610107bd6a50043ef3fe6283d63ddb0946dea4d2a8a874ceb1281a009e
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^28.0.0, jest-matcher-utils@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-matcher-utils@npm:28.1.3"
  dependencies:
    chalk: ^4.0.0
    jest-diff: ^28.1.3
    jest-get-type: ^28.0.2
    pretty-format: ^28.1.3
  checksum: 026fbe664cfdaed5a5c9facfc86ccc9bed3718a7d1fe061e355eb6158019a77f74e9b843bc99f9a467966cbebe60bde8b43439174cbf64997d4ad404f8f809d0
  languageName: node
  linkType: hard

"jest-message-util@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-message-util@npm:28.1.3"
  dependencies:
    "@babel/code-frame": ^7.12.13
    "@jest/types": ^28.1.3
    "@types/stack-utils": ^2.0.0
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    micromatch: ^4.0.4
    pretty-format: ^28.1.3
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 9f56a11b4171e43e2375446e624eec86f82820d9a35de3cd8b065b5ce2d7f65d2bbbdfc0ffe5fa358ff866693a68ec4f6b0cb8ad953fd6f35f9895eb370c6ed7
  languageName: node
  linkType: hard

"jest-mock@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-mock@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    "@types/node": "*"
  checksum: 1d936755925863bd896bfc9c0ed733faf9ff13ab51cdcb4c53bd07e6857e464bb5c0723f9d157837c47dbf880a3a4b9cff2805051207a37caec04d65e6c509fb
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: 86eec0c78449a2de733a6d3e316d49461af6a858070e113c97f75fb742a48c2396ea94150cbca44159ffd4a959f743a47a8b37a792ef6fdad2cf0a5cba973fac
  languageName: node
  linkType: hard

"jest-regex-util@npm:^28.0.2":
  version: 28.0.2
  resolution: "jest-regex-util@npm:28.0.2"
  checksum: d79d255b8a2217bdb0b638cbb5e61a41ab788e62a6217fce5276ab9763c1327b9e0a4f10ebdb230c76848125aa9cc97c8751cfad15db7ec0441d44acfbaf5084
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-resolve-dependencies@npm:28.1.3"
  dependencies:
    jest-regex-util: ^28.0.2
    jest-snapshot: ^28.1.3
  checksum: 534f5f1a204c00858e909ba4f66cbf7f3fcb0b787399ae803c66f2fb344eac1d0f3e802c579ca110a54a1271ec3b4eb7095ef14d56ffeae2b88da0e6ca6cd8a0
  languageName: node
  linkType: hard

"jest-resolve@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-resolve@npm:28.1.3"
  dependencies:
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^28.1.3
    jest-pnp-resolver: ^1.2.2
    jest-util: ^28.1.3
    jest-validate: ^28.1.3
    resolve: ^1.20.0
    resolve.exports: ^1.1.0
    slash: ^3.0.0
  checksum: 3d37b33137266eadc9febb5c8f6ab59030818bf4cc426cf013e260a79189d49e48dee004a796ce48d631e1353bc03463bd630f55ce01af0cffef73c3d23d6f91
  languageName: node
  linkType: hard

"jest-runner@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-runner@npm:28.1.3"
  dependencies:
    "@jest/console": ^28.1.3
    "@jest/environment": ^28.1.3
    "@jest/test-result": ^28.1.3
    "@jest/transform": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/node": "*"
    chalk: ^4.0.0
    emittery: ^0.10.2
    graceful-fs: ^4.2.9
    jest-docblock: ^28.1.1
    jest-environment-node: ^28.1.3
    jest-haste-map: ^28.1.3
    jest-leak-detector: ^28.1.3
    jest-message-util: ^28.1.3
    jest-resolve: ^28.1.3
    jest-runtime: ^28.1.3
    jest-util: ^28.1.3
    jest-watcher: ^28.1.3
    jest-worker: ^28.1.3
    p-limit: ^3.1.0
    source-map-support: 0.5.13
  checksum: 423dd2b4d7c61e27572bb558f68ac838f94927131626e709489636224593d274ad7b8ced6c7abecd2c0075ac9d01bf4e7ef09f1a60c495f66ad855f093575ced
  languageName: node
  linkType: hard

"jest-runtime@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-runtime@npm:28.1.3"
  dependencies:
    "@jest/environment": ^28.1.3
    "@jest/fake-timers": ^28.1.3
    "@jest/globals": ^28.1.3
    "@jest/source-map": ^28.1.2
    "@jest/test-result": ^28.1.3
    "@jest/transform": ^28.1.3
    "@jest/types": ^28.1.3
    chalk: ^4.0.0
    cjs-module-lexer: ^1.0.0
    collect-v8-coverage: ^1.0.0
    execa: ^5.0.0
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^28.1.3
    jest-message-util: ^28.1.3
    jest-mock: ^28.1.3
    jest-regex-util: ^28.0.2
    jest-resolve: ^28.1.3
    jest-snapshot: ^28.1.3
    jest-util: ^28.1.3
    slash: ^3.0.0
    strip-bom: ^4.0.0
  checksum: f315b5dafd1af501afb643b274311fc906cd27236ba87bc004cf0494619fd4fad70bbc8d1b30a7335a17531367cefac0b0941cfd35c255d6ce4aecd686e76508
  languageName: node
  linkType: hard

"jest-snapshot@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-snapshot@npm:28.1.3"
  dependencies:
    "@babel/core": ^7.11.6
    "@babel/generator": ^7.7.2
    "@babel/plugin-syntax-typescript": ^7.7.2
    "@babel/traverse": ^7.7.2
    "@babel/types": ^7.3.3
    "@jest/expect-utils": ^28.1.3
    "@jest/transform": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/babel__traverse": ^7.0.6
    "@types/prettier": ^2.1.5
    babel-preset-current-node-syntax: ^1.0.0
    chalk: ^4.0.0
    expect: ^28.1.3
    graceful-fs: ^4.2.9
    jest-diff: ^28.1.3
    jest-get-type: ^28.0.2
    jest-haste-map: ^28.1.3
    jest-matcher-utils: ^28.1.3
    jest-message-util: ^28.1.3
    jest-util: ^28.1.3
    natural-compare: ^1.4.0
    pretty-format: ^28.1.3
    semver: ^7.3.5
  checksum: 2dcf7a7e7a2ffff8decfab61e4a9b7c333ad4766a21cfb77d63d5bd01c298df31c511ac5c0754715e280e4cdeae9ca91f2c765c86e8764a59c142063bcc8dee6
  languageName: node
  linkType: hard

"jest-util@npm:^28.0.0, jest-util@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-util@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    "@types/node": "*"
    chalk: ^4.0.0
    ci-info: ^3.2.0
    graceful-fs: ^4.2.9
    picomatch: ^2.2.3
  checksum: 7d4946424032a2ccb2ad669905debb44b0bf040dff7a1fe82d283c679ae4638a86ca48d6a276d65a76451252338ad84e76ef2cfde03f577f091fe2b3102aedc9
  languageName: node
  linkType: hard

"jest-validate@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-validate@npm:28.1.3"
  dependencies:
    "@jest/types": ^28.1.3
    camelcase: ^6.2.0
    chalk: ^4.0.0
    jest-get-type: ^28.0.2
    leven: ^3.1.0
    pretty-format: ^28.1.3
  checksum: 57a69c560f7ea8b69d0b26fb895f43de1e46f361c512cb74495b17a10d2999a341dba6a83b67dd3d8899a86242662db113ef8f3e0bc5cbf032a9982535b378e0
  languageName: node
  linkType: hard

"jest-watcher@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-watcher@npm:28.1.3"
  dependencies:
    "@jest/test-result": ^28.1.3
    "@jest/types": ^28.1.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    emittery: ^0.10.2
    jest-util: ^28.1.3
    string-length: ^4.0.1
  checksum: c61da8c35f8fc74224335471675649966787b12ae4469b5049cb46facafb30f16b63a52d0d1137701b651cd514abcae005680bfc542d85979ddbae4dbc6c10ad
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 8c4737ffd03887b3c6768e4cc3ca0269c0336c1e4b1b120943958ddb035ed2a0fc6acab6dc99631720a3720af4e708ff84fb45382ad1e83c27946adf3623969b
  languageName: node
  linkType: hard

"jest-worker@npm:^28.1.3":
  version: 28.1.3
  resolution: "jest-worker@npm:28.1.3"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: d6715268fd6c9fd8431987d42e4ae0981dc6352fd7a5c90aadb9c67562dc6161486a98960f5d1bd36dbafb202d8d98a6fdb181711acbc5e55ee6ab85fa94c931
  languageName: node
  linkType: hard

"jest@npm:28.1.2":
  version: 28.1.2
  resolution: "jest@npm:28.1.2"
  dependencies:
    "@jest/core": ^28.1.2
    "@jest/types": ^28.1.1
    import-local: ^3.0.2
    jest-cli: ^28.1.2
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 33443cb881aaac5a28b282c2fd66392b4157e7831f39c4dcd280051f7d0090e6373a4161867a67106b4980d4afbd467d09fe67feb93602b42125f086365fa063
  languageName: node
  linkType: hard

"jiti@npm:^2.4.1":
  version: 2.4.2
  resolution: "jiti@npm:2.4.2"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: 4ceac133a08c8faff7eac84aabb917e85e8257f5ad659e843004ce76e981c457c390a220881748ac67ba1b940b9b729b30fb85cbaf6e7989f04b6002c94da331
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:4.1.0, js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"json-bigint@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-bigint@npm:1.0.0"
  dependencies:
    bignumber.js: ^9.0.0
  checksum: e3f34e43be3284b573ea150a3890c92f06d54d8ded72894556357946aeed9877fd795f62f37fe16509af189fd314ab1104d0fd0f163746ad231b9f378f5b33f4
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 71e30015d7f3d6dc1c316d6298047c8ef98a06d31ad064919976583eb61e1018a60a0067338f0f79cabc00d84af3fcc489bd48ce8a46ea165d9541ba17fb30c6
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json5@npm:^1.0.1, json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 9ee316bf21f000b00752e6c2a3b79ecf5324515a5c60ee88983a1910a45426b643a4f3461657586e8aeca87aaf96f0a519b0516d2ae527a6c3e7eed80f68717f
  languageName: node
  linkType: hard

"json5@npm:^2.2.1, json5@npm:^2.2.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonc-parser@npm:3.0.0":
  version: 3.0.0
  resolution: "jsonc-parser@npm:3.0.0"
  checksum: 373632ab71f773ce6081dd70297c40fbb05aacde07ab8a5852c78c0a13d1fadb7ad886202e87bfc0168dd78568cda2d4eb36e785dac330f93e5e772d7a67f33f
  languageName: node
  linkType: hard

"jsonc-parser@npm:3.2.0":
  version: 3.2.0
  resolution: "jsonc-parser@npm:3.2.0"
  checksum: 5a12d4d04dad381852476872a29dcee03a57439574e4181d91dca71904fcdcc5e8e4706c0a68a2c61ad9810e1e1c5806b5100d52d3e727b78f5cdc595401045b
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 89bc68080cd0a0e276d4b5ab1b79cacd68f562467008d176dc23e16e97d4efec9e21741d92ba5087a8433526a45a7e6a9d5ef25408696c402ca1cfbc01a90bf0
  languageName: node
  linkType: hard

"jsonwebtoken@npm:9.0.2, jsonwebtoken@npm:^9.0.0, jsonwebtoken@npm:^9.0.2":
  version: 9.0.2
  resolution: "jsonwebtoken@npm:9.0.2"
  dependencies:
    jws: ^3.2.2
    lodash.includes: ^4.3.0
    lodash.isboolean: ^3.0.3
    lodash.isinteger: ^4.0.4
    lodash.isnumber: ^3.0.3
    lodash.isplainobject: ^4.0.6
    lodash.isstring: ^4.0.1
    lodash.once: ^4.0.0
    ms: ^2.1.1
    semver: ^7.5.4
  checksum: d287a29814895e866db2e5a0209ce730cbc158441a0e5a70d5e940eb0d28ab7498c6bf45029cc8b479639bca94056e9a7f254e2cdb92a2f5750c7f358657a131
  languageName: node
  linkType: hard

"jszip@npm:^3.10.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: ~3.3.0
    pako: ~1.0.2
    readable-stream: ~2.3.6
    setimmediate: ^1.0.5
  checksum: 58e01ec9c4960383fb8b38dd5f67b83ccc1ec215bf74c8a5b32f42b6e5fb79fada5176842a11409c4051b5b94275044851814a31076bf49e1be218d3ef57c863
  languageName: node
  linkType: hard

"jwa@npm:^1.4.1":
  version: 1.4.1
  resolution: "jwa@npm:1.4.1"
  dependencies:
    buffer-equal-constant-time: 1.0.1
    ecdsa-sig-formatter: 1.0.11
    safe-buffer: ^5.0.1
  checksum: 5c533540bf38702e73cf14765805a94027c66a0aa8b16bc3e89d8d905e61a4ce2791e87e21be97d1293a5ee9d4f3e5e47737e671768265ca4f25706db551d5e9
  languageName: node
  linkType: hard

"jwa@npm:^2.0.0":
  version: 2.0.0
  resolution: "jwa@npm:2.0.0"
  dependencies:
    buffer-equal-constant-time: 1.0.1
    ecdsa-sig-formatter: 1.0.11
    safe-buffer: ^5.0.1
  checksum: 6baab823b93c038ba1d2a9e531984dcadbc04e9eb98d171f4901b7a40d2be15961a359335de1671d78cb6d987f07cbe5d350d8143255977a889160c4d90fcc3c
  languageName: node
  linkType: hard

"jws@npm:^3.2.2":
  version: 3.2.2
  resolution: "jws@npm:3.2.2"
  dependencies:
    jwa: ^1.4.1
    safe-buffer: ^5.0.1
  checksum: e770704533d92df358adad7d1261fdecad4d7b66fa153ba80d047e03ca0f1f73007ce5ed3fbc04d2eba09ba6e7e6e645f351e08e5ab51614df1b0aa4f384dfff
  languageName: node
  linkType: hard

"jws@npm:^4.0.0":
  version: 4.0.0
  resolution: "jws@npm:4.0.0"
  dependencies:
    jwa: ^2.0.0
    safe-buffer: ^5.0.1
  checksum: f1ca77ea5451e8dc5ee219cb7053b8a4f1254a79cb22417a2e1043c1eb8a569ae118c68f24d72a589e8a3dd1824697f47d6bd4fb4bebb93a3bdf53545e721661
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"kind-of@npm:^3.0.2, kind-of@npm:^3.0.3, kind-of@npm:^3.2.0":
  version: 3.2.2
  resolution: "kind-of@npm:3.2.2"
  dependencies:
    is-buffer: ^1.1.5
  checksum: 7e34bc29d4b02c997f92f080de34ebb92033a96736bbb0bb2410e033a7e5ae6571f1fa37b2d7710018f95361473b816c604234197f4f203f9cf149d8ef1574d9
  languageName: node
  linkType: hard

"kind-of@npm:^4.0.0":
  version: 4.0.0
  resolution: "kind-of@npm:4.0.0"
  dependencies:
    is-buffer: ^1.1.5
  checksum: d6c44c75ee36898142dfc7106afbd50593216c37f96acb81a7ab33ca1a6938ce97d5692b8fc8fccd035f83811a9d97749d68771116441a48eedd0b68e2973165
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2, kind-of@npm:^6.0.3":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"lazystream@npm:^1.0.0":
  version: 1.0.1
  resolution: "lazystream@npm:1.0.1"
  dependencies:
    readable-stream: ^2.0.5
  checksum: ea4e509a5226ecfcc303ba6782cc269be8867d372b9bcbd625c88955df1987ea1a20da4643bf9270336415a398d33531ebf0d5f0d393b9283dc7c98bfcbd7b69
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"libphonenumber-js@npm:^1.10.53":
  version: 1.11.20
  resolution: "libphonenumber-js@npm:1.11.20"
  checksum: fb1010bfa5f8682dadfbd9608f76207510e13c456db96d0d534cf727efa2de005cafbb16d016046965894af0787bf567eb5a1a2688d7bb2d5f8b2eebc42437c6
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: ~3.0.5
  checksum: 56dd113091978f82f9dc5081769c6f3b947852ecf9feccaf83e14a123bc630c2301439ce6182521e5fbafbde88e88ac38314327a4e0493a1bea7e0699a7af808
  languageName: node
  linkType: hard

"lilconfig@npm:2.0.4":
  version: 2.0.4
  resolution: "lilconfig@npm:2.0.4"
  checksum: bdd3d4bd82c6381a3e600962cfc285610564888f126e2cec3cd0fdc41a1892266fa17f32f372a4a6c9c57c265d377ab58a36e2b68a91eedd377389b41334f112
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"lint-staged@npm:12.3.7":
  version: 12.3.7
  resolution: "lint-staged@npm:12.3.7"
  dependencies:
    cli-truncate: ^3.1.0
    colorette: ^2.0.16
    commander: ^8.3.0
    debug: ^4.3.3
    execa: ^5.1.1
    lilconfig: 2.0.4
    listr2: ^4.0.1
    micromatch: ^4.0.4
    normalize-path: ^3.0.0
    object-inspect: ^1.12.0
    pidtree: ^0.5.0
    string-argv: ^0.3.1
    supports-color: ^9.2.1
    yaml: ^1.10.2
  bin:
    lint-staged: bin/lint-staged.js
  checksum: 85f8e39c3e6697710588cc52096d14c1ffbdfb2ac1eac89752c04ba68f420d0dbb755017cc837dad6bab181abab6eb5b3dd4504905449b6f88e82a36b88b1c7e
  languageName: node
  linkType: hard

"listenercount@npm:~1.0.1":
  version: 1.0.1
  resolution: "listenercount@npm:1.0.1"
  checksum: 280c38501984f0a83272187ea472aff18a2aa3db40d8e05be5f797dc813c3d9351ae67a64e09d23d36e6061288b291c989390297db6a99674de2394c6930284c
  languageName: node
  linkType: hard

"listr2@npm:^4.0.1":
  version: 4.0.5
  resolution: "listr2@npm:4.0.5"
  dependencies:
    cli-truncate: ^2.1.0
    colorette: ^2.0.16
    log-update: ^4.0.0
    p-map: ^4.0.0
    rfdc: ^1.3.0
    rxjs: ^7.5.5
    through: ^2.3.8
    wrap-ansi: ^7.0.0
  peerDependencies:
    enquirer: ">= 2.3.0 < 3"
  peerDependenciesMeta:
    enquirer:
      optional: true
  checksum: 0e64dc5e66fbd4361f6b35c49489ed842a1d7de30cf2b5c06bf4569669449288698b8ea93f7842aaf3c510963a1e554bca31376b9054d1521445d1ce4c917ea1
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a44d78aae0907a72f73966fe8b82d1439c8c485238bd5a864b1b9a2a3257832effa858790241e6b37876b5446a78889adf2fcc8dd897ce54c089ecc0a0ce0bf0
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: fcba15d21a458076dd309fce6b1b4bf611d84a0ec252cb92447c948c533ac250b95d2e00955801ebc367e5af5ed288b996d75d37d2035260a937008e14eaf432
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: d5b77aeb702caa69b17be1358faece33a84497bcca814897383c58b28a2f8dfc381b1d9edbec239f8b425126a3bbe4916223da2a576bb0411c2cefd67df80707
  languageName: node
  linkType: hard

"lodash.difference@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.difference@npm:4.5.0"
  checksum: 5d52859218a7df427547ff1fadbc397879709fe6c788b037df7d6d92b676122c92bd35ec85d364edb596b65dfc6573132f420c9b4ee22bb6b9600cd454c90637
  languageName: node
  linkType: hard

"lodash.escaperegexp@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.escaperegexp@npm:4.1.2"
  checksum: 484ad4067fa9119bb0f7c19a36ab143d0173a081314993fe977bd00cf2a3c6a487ce417a10f6bac598d968364f992153315f0dbe25c9e38e3eb7581dd333e087
  languageName: node
  linkType: hard

"lodash.flatten@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.flatten@npm:4.4.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"lodash.groupby@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.groupby@npm:4.6.0"
  checksum: 3d136cad438ad6c3a078984ef60e057a3498b1312aa3621b00246ecb99e8f2c4d447e2815460db7a0b661a4fe4e2eeee96c84cb661a824bad04b6cf1f7bc6e9b
  languageName: node
  linkType: hard

"lodash.includes@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.includes@npm:4.3.0"
  checksum: 7ca498b9b75bf602d04e48c0adb842dfc7d90f77bcb2a91a2b2be34a723ad24bc1c8b3683ec6b2552a90f216c723cdea530ddb11a3320e08fa38265703978f4b
  languageName: node
  linkType: hard

"lodash.isboolean@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isboolean@npm:3.0.3"
  checksum: 0aac604c1ef7e72f9a6b798e5b676606042401dd58e49f051df3cc1e3adb497b3d7695635a5cbec4ae5f66456b951fdabe7d6b387055f13267cde521f10ec7f7
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: dfdb2356db19631a4b445d5f37868a095e2402292d59539a987f134a8778c62a2810c2452d11ae9e6dcac71fc9de40a6fedcb20e2952a15b431ad8b29e50e28f
  languageName: node
  linkType: hard

"lodash.isfunction@npm:^3.0.9":
  version: 3.0.9
  resolution: "lodash.isfunction@npm:3.0.9"
  checksum: e88620922f5f104819496884779ca85bfc542efb2946df661ab3e2cd38da5c8375434c6adbedfc76dd3c2b04075d2ba8ec215cfdedf08ddd2e3c3467e8a26ccd
  languageName: node
  linkType: hard

"lodash.isinteger@npm:^4.0.4":
  version: 4.0.4
  resolution: "lodash.isinteger@npm:4.0.4"
  checksum: 4c3e023a2373bf65bf366d3b8605b97ec830bca702a926939bcaa53f8e02789b6a176e7f166b082f9365bfec4121bfeb52e86e9040cb8d450e64c858583f61b7
  languageName: node
  linkType: hard

"lodash.isnil@npm:^4.0.0":
  version: 4.0.0
  resolution: "lodash.isnil@npm:4.0.0"
  checksum: 1a410a62eb2e797f077d038c11cbf1ea18ab36f713982849f086f86e050234d69988c76fa18d00278c0947daec67e9ecbc666326b8a06b43e36d3ece813a8120
  languageName: node
  linkType: hard

"lodash.isnumber@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isnumber@npm:3.0.3"
  checksum: 2d01530513a1ee4f72dd79528444db4e6360588adcb0e2ff663db2b3f642d4bb3d687051ae1115751ca9082db4fdef675160071226ca6bbf5f0c123dbf0aa12d
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: afd70b5c450d1e09f32a737bed06ff85b873ecd3d3d3400458725283e3f2e0bb6bf48e67dbe7a309eb371a822b16a26cca4a63c8c52db3fc7dc9d5f9dd324cbb
  languageName: node
  linkType: hard

"lodash.isstring@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.isstring@npm:4.0.1"
  checksum: 09eaf980a283f9eef58ef95b30ec7fee61df4d6bf4aba3b5f096869cc58f24c9da17900febc8ffd67819b4e29de29793190e88dc96983db92d84c95fa85d1c92
  languageName: node
  linkType: hard

"lodash.isundefined@npm:^3.0.1":
  version: 3.0.1
  resolution: "lodash.isundefined@npm:3.0.1"
  checksum: 00ca2ae6fc83e10f806769130ee62b5bf419a4aaa52d1a084164b4cf2b2ab1dbf7246e05c72cf0df2ebf4ea38ab565a688c1a7362b54331bb336ea8b492f327f
  languageName: node
  linkType: hard

"lodash.kebabcase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.kebabcase@npm:4.1.1"
  checksum: da5d8f41dbb5bc723d4bf9203d5096ca8da804d6aec3d2b56457156ba6c8d999ff448d347ebd97490da853cb36696ea4da09a431499f1ee8deb17b094ecf4e33
  languageName: node
  linkType: hard

"lodash.map@npm:^4.5.1":
  version: 4.6.0
  resolution: "lodash.map@npm:4.6.0"
  checksum: 919fe767fa58d3f8369ddd84346636eda71c88a8ef6bde1ca0d87dd37e71614da2ed8bcfc3018ca5b7741ebaf7c01c2d7078b510dca8ab6a0d0ecafd3dc1abcb
  languageName: node
  linkType: hard

"lodash.memoize@npm:4.x":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: c8713e51eccc650422716a14cece1809cfe34bc5ab5e242b7f8b4e2241c2483697b971a604252807689b9dd69bfe3a98852e19a5b89d506b000b4187a1285df8
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash.mergewith@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.mergewith@npm:4.6.2"
  checksum: 4adbed65ff96fd65b0b3861f6899f98304f90fd71e7f1eb36c1270e05d500ee7f5ec44c02ef979b5ddbf75c0a0b9b99c35f0ad58f4011934c4d4e99e5200b3b5
  languageName: node
  linkType: hard

"lodash.once@npm:^4.0.0":
  version: 4.1.1
  resolution: "lodash.once@npm:4.1.1"
  checksum: 46a9a0a66c45dd812fcc016e46605d85ad599fe87d71a02f6736220554b52ffbe82e79a483ad40f52a8a95755b0d1077fba259da8bfb6694a7abbf4a48f1fc04
  languageName: node
  linkType: hard

"lodash.snakecase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.snakecase@npm:4.1.1"
  checksum: f0b3f2497eb20eea1a1cfc22d645ecaeb78ac14593eb0a40057977606d2f35f7aaff0913a06553c783b535aafc55b718f523f9eb78f8d5293f492af41002eaf9
  languageName: node
  linkType: hard

"lodash.startcase@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.startcase@npm:4.4.0"
  checksum: bd82aa87a45de8080e1c5ee61128c7aee77bf7f1d86f4ff94f4a6d7438fc9e15e5f03374b947be577a93804c8ad6241f0251beaf1452bf716064eeb657b3a9f0
  languageName: node
  linkType: hard

"lodash.union@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.union@npm:4.6.0"
  checksum: 6da7f72d1facd472f6090b49eefff984c9f9179e13172039c0debca6851d21d37d83c7ad5c43af23bd220f184cd80e6897e8e3206509fae491f9068b02ae6319
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: 262d400bb0952f112162a320cc4a75dea4f66078b9e7e3075ffbc9c6aa30b3e9df3cf20e7da7d566105e1ccf7804e4fbd7d804eee0b53de05d83f16ffbf41c5e
  languageName: node
  linkType: hard

"lodash.upperfirst@npm:^4.3.1":
  version: 4.3.1
  resolution: "lodash.upperfirst@npm:4.3.1"
  checksum: 435625da4b3ee74e7a1367a780d9107ab0b13ef4359fc074b2a1a40458eb8d91b655af62f6795b7138d493303a98c0285340160341561d6896e4947e077fa975
  languageName: node
  linkType: hard

"lodash@npm:4.17.15":
  version: 4.17.15
  resolution: "lodash@npm:4.17.15"
  checksum: c029ab298357d007252d5802348f1deb343099ba734ae6e6776a908e4995db5b8ed4ae51ff83a2937a1f6e3c41a0c8941eb941926e1a7f2ddfc3d235ec1e8aa7
  languageName: node
  linkType: hard

"lodash@npm:4.17.21, lodash@npm:^4.17.12, lodash@npm:^4.17.15, lodash@npm:^4.17.19, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: 67f445a9ffa76db1989d0fa98586e5bc2fd5247260dafb8ad93d9f0ccd5896d53fb830b0e54dade5ad838b9de2006c826831a3c528913093af20dff8bd24aca6
  languageName: node
  linkType: hard

"log-update@npm:^4.0.0":
  version: 4.0.0
  resolution: "log-update@npm:4.0.0"
  dependencies:
    ansi-escapes: ^4.3.0
    cli-cursor: ^3.1.0
    slice-ansi: ^4.0.0
    wrap-ansi: ^6.2.0
  checksum: 18b299e230432a156f2535660776406d15ba8bb7817dd3eaadd58004b363756d4ecaabcd658f9949f90b62ea7d3354423be3fdeb7a201ab951ec0e8d6139af86
  languageName: node
  linkType: hard

"long@npm:^5.2.1":
  version: 5.3.2
  resolution: "long@npm:5.3.2"
  checksum: 7130fe1cbce2dca06734b35b70d380ca3f70271c7f8852c922a7c62c86c4e35f0c39290565eca7133c625908d40e126ac57c02b1b1a4636b9457d77e1e60b981
  languageName: node
  linkType: hard

"longest@npm:^2.0.1":
  version: 2.0.1
  resolution: "longest@npm:2.0.1"
  checksum: f381993a55acfbb76c7f75cfc14f45502b323e2a9881db6a834a3082f5587f8cd375f1334e562d8b7dcb1f91d10782af5f768c404774acc7ac42c0cefd9f25f8
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: 89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"lru-cache@npm:^7.14.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: b3a452b491433db885beed95041eb104c157ef7794b9c9b4d647be503be91769d11206bb573849a16b4cc0d03cbd15ffd22df7960997788b74c1d399ac7a4fed
  languageName: node
  linkType: hard

"lru.min@npm:^1.0.0":
  version: 1.1.2
  resolution: "lru.min@npm:1.1.2"
  checksum: 64f0cbb155899b62e57b5f0f1e69d5427252cf87cd1dd2ba87d6768da7636ba1e459bd6b97a7632cf50ee9ede927809dab5c50ab76651d56c3cbf970d1b08f5c
  languageName: node
  linkType: hard

"macos-release@npm:^2.5.0":
  version: 2.5.1
  resolution: "macos-release@npm:2.5.1"
  checksum: fd03674e0b91e88a82cabecb75d75bc562863b186a22eac857f7d90c117486e44e02bede0926315637749aaaa934415bd1c2d0c0b53b78a86b729f3c165c5850
  languageName: node
  linkType: hard

"magic-string@npm:0.25.7":
  version: 0.25.7
  resolution: "magic-string@npm:0.25.7"
  dependencies:
    sourcemap-codec: ^1.4.4
  checksum: d5da35f01d5437d7d6c030fe8185285a78b97144d07944d62187bd985ee2f6dcc8c9a538ded6a3afe186f5d6f2e705b45f9f307b19020aff530447bd32f24375
  languageName: node
  linkType: hard

"magic-string@npm:0.27.0":
  version: 0.27.0
  resolution: "magic-string@npm:0.27.0"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.4.13
  checksum: cddacfea14441ca57ae8a307bc3cf90bac69efaa4138dd9a80804cffc2759bf06f32da3a293fb13eaa96334b7d45b7768a34f1d226afae25d2f05b05a3bb37d8
  languageName: node
  linkType: hard

"magic-string@npm:0.30.0":
  version: 0.30.0
  resolution: "magic-string@npm:0.30.0"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.4.13
  checksum: 5fac57cf190bee966d3b5c55e0c23d6148b043a43220de91a369c4a81301b483418712b38440d15055a2ac04beec63dea4866a4e5c84ad6b919186e1c5c61241
  languageName: node
  linkType: hard

"make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 56aaafefc49c2dfef02c5c95f9b196c4eb6988040cf2c712185c7fe5c99b4091591a7fc4d4eafaaefa70ff763a26f6ab8c3ff60b9e75ea19876f49b18667ecaa
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: ^7.5.3
  checksum: 69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-error@npm:1.x, make-error@npm:^1.1.1":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: 171e458d86854c6b3fc46610cfacf0b45149ba043782558c6875d9f42f222124384ad0b468c92e996d815a8a2003817a710c0a160e49c1c394626f76fa45396f
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: 1.0.5
  checksum: b0e6e599780ce6bab49cc413eba822f7d1f0dfebd1c103eaa3785c59e43e22c59018323cf9e1708f0ef5329e94a745d163fcbb6bff8e4c6742f9be9e86f3500c
  languageName: node
  linkType: hard

"map-cache@npm:^0.2.2":
  version: 0.2.2
  resolution: "map-cache@npm:0.2.2"
  checksum: 05e3eb005c1b80b9f949ca007687640e8c5d0fc88dc45c3c3ab4902a3bec79d66a58f3e3b04d6985d90cd267c629c7b46c977e9c34433e8c11ecfcbb9f0fa290
  languageName: node
  linkType: hard

"map-obj@npm:^1.0.0":
  version: 1.0.1
  resolution: "map-obj@npm:1.0.1"
  checksum: ccca88395e7d38671ed9f5652ecf471ecd546924be2fb900836b9da35e068a96687d96a5f93dcdfa94d9a27d649d2f10a84595590f89a347fb4dda47629dcc52
  languageName: node
  linkType: hard

"map-obj@npm:^4.0.0":
  version: 4.3.0
  resolution: "map-obj@npm:4.3.0"
  checksum: 1c19e1c88513c8abdab25c316367154c6a0a6a0f77e3e8c391bb7c0e093aefed293f539d026dc013d86219e5e4c25f23b0003ea588be2101ccd757bacc12d43b
  languageName: node
  linkType: hard

"map-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "map-visit@npm:1.0.0"
  dependencies:
    object-visit: ^1.0.0
  checksum: fb3475e5311939a6147e339999113db607adc11c7c3cd3103e5e9dbf502898416ecba6b1c7c649c6d4d12941de00cee58b939756bdf20a9efe7d4fa5a5738b73
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"memfs@npm:^3.4.1":
  version: 3.5.3
  resolution: "memfs@npm:3.5.3"
  dependencies:
    fs-monkey: ^1.0.4
  checksum: 038fc81bce17ea92dde15aaa68fa0fdaf4960c721ce3ffc7c2cb87a259333f5159784ea48b3b72bf9e054254d9d0d0d5209d0fdc3d07d08653a09933b168fbd7
  languageName: node
  linkType: hard

"meow@npm:^8.0.0, meow@npm:^8.1.2":
  version: 8.1.2
  resolution: "meow@npm:8.1.2"
  dependencies:
    "@types/minimist": ^1.2.0
    camelcase-keys: ^6.2.2
    decamelize-keys: ^1.1.0
    hard-rejection: ^2.1.0
    minimist-options: 4.1.0
    normalize-package-data: ^3.0.0
    read-pkg-up: ^7.0.1
    redent: ^3.0.0
    trim-newlines: ^3.0.0
    type-fest: ^0.18.0
    yargs-parser: ^20.2.3
  checksum: 9a8d90e616f783650728a90f4ea1e5f763c1c5260369e6596b52430f877f4af8ecbaa8c9d952c93bbefd6d5bda4caed6a96a20ba7d27b511d2971909b01922a2
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: b67d07bd44cfc45cebdec349bb6e1f7b077ee2fd5beb15d1f7af073849208cb6f144fe403e29a36571baf3f4e86469ac39acf13c318381e958e186b2766f54ec
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"merge@npm:^1.2.1":
  version: 1.2.1
  resolution: "merge@npm:1.2.1"
  checksum: 09f0ed1e85f336feb32d93d1243cb8930be22c7e6e4aa0c59a08a6f680d274dbe021c7d2a35ee8383cab2524efbb3f51985c3052e3dcf134951656511edf4693
  languageName: node
  linkType: hard

"merge@npm:^2.1.1":
  version: 2.1.1
  resolution: "merge@npm:2.1.1"
  checksum: 9e722a88f661fb4d32bfbab37dcc10c2057d3e3ec7bda5325a13cbfb82a59916963ec99374cca7f5bd3ff8c65a6ffbd9e1061bc0c45c6e3bf211c78af659cb44
  languageName: node
  linkType: hard

"methods@npm:^1.1.2, methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^3.0.4":
  version: 3.1.10
  resolution: "micromatch@npm:3.1.10"
  dependencies:
    arr-diff: ^4.0.0
    array-unique: ^0.3.2
    braces: ^2.3.1
    define-property: ^2.0.2
    extend-shallow: ^3.0.2
    extglob: ^2.0.4
    fragment-cache: ^0.2.1
    kind-of: ^6.0.2
    nanomatch: ^1.2.9
    object.pick: ^1.3.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.2
  checksum: 531a32e7ac92bef60657820202be71b63d0f945c08a69cc4c239c0b19372b751483d464a850a2e3a5ff6cc9060641e43d44c303af104c1a27493d137d8af017f
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.0, micromatch@npm:^4.0.2, micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:2.6.0":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: ad55214aec6094c0af4c0beec1a13787556f8116ed88807cf3f05828500f21f93a9482326bcd5a077ae91e3e8795b4e76b5b4c8bb12237ff0e4043a365516cba
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: 7e207bd5c20401b292de291f02913230cb1163abca162044f7db1d951fa245b174dc00869d40dd9a9f32a885ad6a5f3e767ee104cf278f399cb4e92d3f582d5c
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: 0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.1.0":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist-options@npm:4.1.0":
  version: 4.1.0
  resolution: "minimist-options@npm:4.1.0"
  dependencies:
    arrify: ^1.0.1
    is-plain-obj: ^1.1.0
    kind-of: ^6.0.3
  checksum: 7871f9cdd15d1e7374e5b013e2ceda3d327a06a8c7b38ae16d9ef941e07d985e952c589e57213f7aa90a8744c60aed9524c0d85e501f5478382d9181f2763f54
  languageName: node
  linkType: hard

"minimist@npm:1.2.5":
  version: 1.2.5
  resolution: "minimist@npm:1.2.5"
  checksum: c143b0c199af4df7a55c7a37b6465cdd438acdc6a3a345ba0fe9d94dfcc2042263f650879bc73be607c843deeaeaadf39c864e55bc6d80b36a025eca1a062ee7
  languageName: node
  linkType: hard

"minimist@npm:1.2.6":
  version: 1.2.6
  resolution: "minimist@npm:1.2.6"
  checksum: d0b566204044481c4401abbd24cc75814e753b37268e7fe7ccc78612bf3e37bf1e45a6c43fb0b119445ea1c413c000bde013f320b7211974f2f49bcbec1d0dbf
  languageName: node
  linkType: hard

"minimist@npm:1.2.7":
  version: 1.2.7
  resolution: "minimist@npm:1.2.7"
  checksum: 8808da67ca50ee19ab2d69051d77ee78572e67297fd8a1635ecc757a15106ccdfb5b8c4d11d84750120142f1684e5329a141295728c755e5d149eedd73cc6572
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: 5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 7fa30ce7c373fb6f94c086b374fff1589fd7e78451855d2d06c2e2d9df936d131e73e952163063016592ed3081444bd8d1ea608533313b0149156ce23311da4b
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: 64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: ^7.0.4
    rimraf: ^5.0.5
  checksum: 82f8bf70da8af656909a8ee299d7ed3b3372636749d29e105f97f20e88971be31f5ed7642f2e898f00283b68b701cc01307401cdc209b0efc5dd3818220e5093
  languageName: node
  linkType: hard

"mixin-deep@npm:^1.2.0":
  version: 1.3.2
  resolution: "mixin-deep@npm:1.3.2"
  dependencies:
    for-in: ^1.0.2
    is-extendable: ^1.0.1
  checksum: cb39ffb73c377222391af788b4c83d1a6cecb2d9fceb7015384f8deb46e151a9b030c21ef59a79cb524d4557e3f74c7248ab948a62a6e7e296b42644863d183b
  languageName: node
  linkType: hard

"mkdirp@npm:>=0.5 0, mkdirp@npm:^0.5.4":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: ^1.2.6
  bin:
    mkdirp: bin/cmd.js
  checksum: e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^2.1.3":
  version: 2.1.6
  resolution: "mkdirp@npm:2.1.6"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 96f551c651dd8f5f9435d53df1a7b9bfc553be769ee6da5192c37c1f303a376ef1c6996f96913d4a8d357060451d4526a346031d1919f92c58806a5fa3cd8dfe
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"multer@npm:1.4.4-lts.1":
  version: 1.4.4-lts.1
  resolution: "multer@npm:1.4.4-lts.1"
  dependencies:
    append-field: ^1.0.0
    busboy: ^1.0.0
    concat-stream: ^1.5.2
    mkdirp: ^0.5.4
    object-assign: ^4.1.1
    type-is: ^1.6.4
    xtend: ^4.0.0
  checksum: 63277d3483869f424274ef8ce6ab7ff4ce9d2c1cc69e707fc8b5d9b2b348ae6f742809e0b357a591dea885d147594bcd06528d3d6bbe32046115d4a7e126b954
  languageName: node
  linkType: hard

"mute-stream@npm:0.0.7":
  version: 0.0.7
  resolution: "mute-stream@npm:0.0.7"
  checksum: c687cfe99289166fe17dcbd0cf49612c5d267410a7819b654a82df45016967d7b2b0b18b35410edef86de6bb089a00413557dc0182c5e78a4af50ba5d61edb42
  languageName: node
  linkType: hard

"mute-stream@npm:0.0.8":
  version: 0.0.8
  resolution: "mute-stream@npm:0.0.8"
  checksum: 18d06d92e5d6d45e2b63c0e1b8f25376af71748ac36f53c059baa8b76ffac31c5ab225480494e7d35d30215ecdb18fed26ec23cafcd2f7733f2f14406bcd19e2
  languageName: node
  linkType: hard

"mysql2@npm:^3.14.0":
  version: 3.14.0
  resolution: "mysql2@npm:3.14.0"
  dependencies:
    aws-ssl-profiles: ^1.1.1
    denque: ^2.1.0
    generate-function: ^2.3.1
    iconv-lite: ^0.6.3
    long: ^5.2.1
    lru.min: ^1.0.0
    named-placeholders: ^1.1.3
    seq-queue: ^0.0.5
    sqlstring: ^2.3.2
  checksum: d11d21339bb02283cc69fad73cf8fa7ba116a2f2a559f68ec8deadd3a9642344e5be0dda6dd76af490c5f8fe13948903465e3419960180a757fd8fa68fc29653
  languageName: node
  linkType: hard

"mz@npm:^2.4.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 103114e93f87362f0b56ab5b2e7245051ad0276b646e3902c98397d18bb8f4a77f2ea4a2c9d3ad516034ea3a56553b60d3f5f78220001ca4c404bd711bd0af39
  languageName: node
  linkType: hard

"named-placeholders@npm:^1.1.3":
  version: 1.1.3
  resolution: "named-placeholders@npm:1.1.3"
  dependencies:
    lru-cache: ^7.14.1
  checksum: cd83b4bbdf358b2285e3c51260fac2039c9d0546632b8a856b3eeabd3bfb3d5b597507ab319b97c281a4a70d748f38bc66fa218a61cb44f55ad997ad5d9c9935
  languageName: node
  linkType: hard

"nanomatch@npm:^1.2.9":
  version: 1.2.13
  resolution: "nanomatch@npm:1.2.13"
  dependencies:
    arr-diff: ^4.0.0
    array-unique: ^0.3.2
    define-property: ^2.0.2
    extend-shallow: ^3.0.2
    fragment-cache: ^0.2.1
    is-windows: ^1.0.2
    kind-of: ^6.0.2
    object.pick: ^1.3.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: 0f5cefa755ca2e20c86332821995effb24acb79551ddaf51c1b9112628cad234a0d8fd9ac6aa56ad1f8bfad6ff6ae86e851acb960943249d9fa44b091479953a
  languageName: node
  linkType: hard

"natural-compare-lite@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare-lite@npm:1.4.0"
  checksum: f6cef26f5044515754802c0fc475d81426f3b90fe88c20fabe08771ce1f736ce46e0397c10acb569a4dd0acb84c7f1ee70676122f95d5bfdd747af3a6c6bbaa8
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"nestjs-form-data@npm:^1.8.7":
  version: 1.9.93
  resolution: "nestjs-form-data@npm:1.9.93"
  dependencies:
    busboy: ^1.6.0
    concat-stream: ^2.0.0
    file-type: ^16.5.4
    mkdirp: ^1.0.4
    type-is: ^1.6.18
    uid: ^2.0.0
  peerDependencies:
    "@nestjs/common": ^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0
    "@nestjs/core": ^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0
    class-transformer: ^0.4.0 || ^0.5.1
    class-validator: ^0.13.2 || ^0.14.0
    reflect-metadata: ^0.1.13 || ^0.2.0
    rxjs: ^6.6.3 || ^7.2.0 || ^7.5.0
  checksum: 3393abe4398011fb65ef32841847ebfe0c9ed7e74c202d22529b51694bd7bf9f152411a60d41c5d2e72dbb459d3c430704d6e0ef97ea75ebce573795c6bb9f3e
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.0.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: f7ad0e7a8e33809d4f3a0d1d65036a711c39e9d23e0319d80ebe076b9a3b4432b4d6b86a7fab65521de3f6872ffed36fc35d1327487c48eb88c517803403eda3
  languageName: node
  linkType: hard

"node-addon-api@npm:^5.0.0":
  version: 5.1.0
  resolution: "node-addon-api@npm:5.1.0"
  dependencies:
    node-gyp: latest
  checksum: 0eb269786124ba6fad9df8007a149e03c199b3e5a3038125dfb3e747c2d5113d406a4e33f4de1ea600aa2339be1f137d55eba1a73ee34e5fff06c52a5c296d1d
  languageName: node
  linkType: hard

"node-emoji@npm:1.11.0":
  version: 1.11.0
  resolution: "node-emoji@npm:1.11.0"
  dependencies:
    lodash: ^4.17.21
  checksum: 5dac6502dbef087092d041fcc2686d8be61168593b3a9baf964d62652f55a3a9c2277f171b81cccb851ccef33f2d070f45e633fab1fda3264f8e1ae9041c673f
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.7, node-fetch@npm:^2.6.9":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.1.0
  resolution: "node-gyp@npm:11.1.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: c38977ce502f1ea41ba2b8721bd5b49bc3d5b3f813eabfac8414082faf0620ccb5211e15c4daecc23ed9f5e3e9cc4da00e575a0bcfc2a95a069294f2afa1e0cd
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: a6a4d8369e2f2720e9c645255ffde909c0fbd41c92ea92a5607fc17055955daac99c1ff589d421eee12a0d24e99f7bfc2aabfeb1a4c14742f6c099a51863f31a
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nodemailer@npm:^6.10.0":
  version: 6.10.0
  resolution: "nodemailer@npm:6.10.0"
  checksum: 39fd35d65b021b94c968eeac82a66dd843021b6ba53c659d01b1dd4cda73b6a2f96e20facfe500efa4b8d3f1cb23df10245c6c86b2bde5f806691ed17ce87826
  languageName: node
  linkType: hard

"nodemon@npm:^2.0.20":
  version: 2.0.22
  resolution: "nodemon@npm:2.0.22"
  dependencies:
    chokidar: ^3.5.2
    debug: ^3.2.7
    ignore-by-default: ^1.0.1
    minimatch: ^3.1.2
    pstree.remy: ^1.1.8
    semver: ^5.7.1
    simple-update-notifier: ^1.0.7
    supports-color: ^5.5.0
    touch: ^3.1.0
    undefsafe: ^2.0.5
  bin:
    nodemon: bin/nodemon.js
  checksum: 37e960b995b66e6d9e3b0e435ecc07a45200c4c566c4820a4deb6e7cc234b305e076a8ff0b4dc9c01ee690c663ae82a217d60c591dcbbcd4af1e3a7d0ad2b2c6
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: 1
  bin:
    nopt: bin/nopt.js
  checksum: fc5c4f07155cb455bf5fc3dd149fac421c1a40fd83c6bfe83aa82b52f02c17c5e88301321318adaa27611c8a6811423d51d29deaceab5fa158b585a61a551061
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: ^3.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 357cb1646deb42f8eb4c7d42c4edf0eec312f3628c2ef98501963cc4bbe7277021b2b1d977f982b2edce78f5a1014613ce9cf38085c3df2d76730481357ca504
  languageName: node
  linkType: hard

"normalize-package-data@npm:^3.0.0":
  version: 3.0.3
  resolution: "normalize-package-data@npm:3.0.3"
  dependencies:
    hosted-git-info: ^4.0.1
    is-core-module: ^2.5.0
    semver: ^7.3.4
    validate-npm-package-license: ^3.0.1
  checksum: e5d0f739ba2c465d41f77c9d950e291ea4af78f8816ddb91c5da62257c40b76d8c83278b0d08ffbcd0f187636ebddad20e181e924873916d03e6e5ea2ef026be
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.0, npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"npmlog@npm:^5.0.1":
  version: 5.0.1
  resolution: "npmlog@npm:5.0.1"
  dependencies:
    are-we-there-yet: ^2.0.0
    console-control-strings: ^1.1.0
    gauge: ^3.0.0
    set-blocking: ^2.0.0
  checksum: 489ba519031013001135c463406f55491a17fc7da295c18a04937fe3a4d523fd65e88dd418a28b967ab743d913fdeba1e29838ce0ad8c75557057c481f7d49fa
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-copy@npm:^0.1.0":
  version: 0.1.0
  resolution: "object-copy@npm:0.1.0"
  dependencies:
    copy-descriptor: ^0.1.0
    define-property: ^0.2.5
    kind-of: ^3.0.3
  checksum: 79314b05e9d626159a04f1d913f4c4aba9eae8848511cf5f4c8e3b04bb3cc313b65f60357f86462c959a14c2d58380fedf89b6b32ecec237c452a5ef3900a293
  languageName: node
  linkType: hard

"object-inspect@npm:^1.12.0, object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-visit@npm:^1.0.0":
  version: 1.0.1
  resolution: "object-visit@npm:1.0.1"
  dependencies:
    isobject: ^3.0.0
  checksum: 086b475bda24abd2318d2b187c3e928959b89f5cb5883d6fe5a42d03719b61fc18e765f658de9ac8730e67ba9ff26d61e73d991215948ff9ecefe771e0071029
  languageName: node
  linkType: hard

"object.pick@npm:^1.3.0":
  version: 1.3.0
  resolution: "object.pick@npm:1.3.0"
  dependencies:
    isobject: ^3.0.1
  checksum: cd316ec986e49895a28f2df9182de9cdeee57cd2a952c122aacc86344c28624fe002d9affc4f48b5014ec7c033da9942b08821ddb44db8c5bac5b3ec54bdc31e
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: 46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: 5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: ^1.0.0
  checksum: b4e44a8c34e70e02251bfb578a6e26d6de6eedbed106cd78211d2fd64d28b6281d54924696554e4e966559644243753ac5df73c87f283b0927533d3315696215
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: 4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"ora@npm:5.4.1, ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 10ff14aace236d0e2f044193362b22edce4784add08b779eccc8f8ef97195cae1248db8ec1ec5f5ff076f91acbe573f5f42a98c19b78dba8c54eefff983cae85
  languageName: node
  linkType: hard

"os-name@npm:4.0.1":
  version: 4.0.1
  resolution: "os-name@npm:4.0.1"
  dependencies:
    macos-release: ^2.5.0
    windows-release: ^4.0.0
  checksum: 2a78bb1a25afa04ec53a972ed164948432fee93d9e039afaec3a27ffe30473ffc85afb03c0776ca3e01c8d806f99f61cb85ad3fbc060bc3e37a549c0a4867f3f
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: f438450224f8e2687605a8dd318f0db694b6293c5d835ae509a69e97c8de38b6994645337e5577f5001115470414638978cc49da1cdcc25106dad8738dc69990
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: 592c05bd6262c466ce269ff172bb8de7c6975afca9b50c975135b974e9bdaafbfe80e61aaaf5be6d1200ba08b30ead04b88cfa7e25ff1e3b93ab28c9f62a2c75
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pako@npm:~1.0.2":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0, parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse-passwd@npm:^1.0.0":
  version: 1.0.0
  resolution: "parse-passwd@npm:1.0.0"
  checksum: 1c05c05f95f184ab9ca604841d78e4fe3294d46b8e3641d305dcc28e930da0e14e602dbda9f3811cd48df5b0e2e27dbef7357bf0d7c40e41b18c11c3a8b8d17b
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5-htmlparser2-tree-adapter@npm:6.0.1"
  dependencies:
    parse5: ^6.0.1
  checksum: dfa5960e2aaf125707e19a4b1bc333de49232eba5a6ffffb95d313a7d6087c3b7a274b58bee8d3bd41bdf150638815d1d601a42bbf2a0345208c3c35b1279556
  languageName: node
  linkType: hard

"parse5@npm:^5.1.1":
  version: 5.1.1
  resolution: "parse5@npm:5.1.1"
  checksum: b0f87a77a7fea5f242e3d76917c983bbea47703b9371801d51536b78942db6441cbda174bf84eb30e47315ddc6f8a0b57d68e562c790154430270acd76c1fa03
  languageName: node
  linkType: hard

"parse5@npm:^6.0.1":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 595821edc094ecbcfb9ddcb46a3e1fe3a718540f8320eff08b8cf6742a5114cce2d46d45f95c26191c11b184dcaf4e2960abcd9c5ed9eb9393ac9a37efcfdecb
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"pascalcase@npm:^0.1.1":
  version: 0.1.1
  resolution: "pascalcase@npm:0.1.1"
  checksum: 48dfe90618e33810bf58211d8f39ad2c0262f19ad6354da1ba563935b5f429f36409a1fb9187c220328f7a4dc5969917f8e3e01ee089b5f1627b02aefe39567b
  languageName: node
  linkType: hard

"passport-jwt@npm:^4.0.1":
  version: 4.0.1
  resolution: "passport-jwt@npm:4.0.1"
  dependencies:
    jsonwebtoken: ^9.0.0
    passport-strategy: ^1.0.0
  checksum: d7e2b472d399f596a1db31310f8e63d10777ab7468b9a378c964156e5f0a772598b007417356ead578cfdaf60dc2bba39a55f0033ca865186fdb2a2b198e2e7e
  languageName: node
  linkType: hard

"passport-strategy@npm:1.x.x, passport-strategy@npm:^1.0.0":
  version: 1.0.0
  resolution: "passport-strategy@npm:1.0.0"
  checksum: cf4cd32e1bf2538a239651581292fbb91ccc83973cde47089f00d2014c24bed63d3e65af21da8ddef649a8896e089eb9c3ac9ca639f36c797654ae9ee4ed65e1
  languageName: node
  linkType: hard

"passport@npm:^0.7.0":
  version: 0.7.0
  resolution: "passport@npm:0.7.0"
  dependencies:
    passport-strategy: 1.x.x
    pause: 0.0.1
    utils-merge: ^1.0.1
  checksum: 08c940b86e4adbfe43e753f8097300a5a9d1ce9a3aa002d7b12d27770943a1a87202c54597c0f04dbfd4117d67de76303433577512fc19c7e364fec37b0d3fc5
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 50a1ddb1af41a9e68bd67ca8e331a705899d16fb720a1ea3a41e310480948387daf603abb14d7b0826c58f10146d49050a1291ba6a82b78a382d1c02c0b8f905
  languageName: node
  linkType: hard

"path-to-regexp@npm:3.2.0":
  version: 3.2.0
  resolution: "path-to-regexp@npm:3.2.0"
  checksum: 2eeb1c698293acf6f89fe5af33b4c20822b3cee3e4e910c43bbee098c8dde34232fc194d5c2bc02df72affada446a181784e24f7a46932af323706be029ed1ba
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"pause@npm:0.0.1":
  version: 0.0.1
  resolution: "pause@npm:0.0.1"
  checksum: f362655dfa7f44b946302c5a033148852ed5d05f744bd848b1c7eae6a543f743e79c7751ee896ba519fd802affdf239a358bb2ea5ca1b1c1e4e916279f83ab75
  languageName: node
  linkType: hard

"peek-readable@npm:^4.1.0":
  version: 4.1.0
  resolution: "peek-readable@npm:4.1.0"
  checksum: f9b81ce3eed185cc9ebbf7dff0b6e130dd6da7b05f1802bbf726a78e4d84990b0a65f8e701959c50eb1124cc2ad352205147954bf39793faba29bb00ce742a44
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"pidtree@npm:^0.5.0":
  version: 0.5.0
  resolution: "pidtree@npm:0.5.0"
  bin:
    pidtree: bin/pidtree.js
  checksum: 4004b1c7429d02be941ad7ca2eac3bd93afa5cd59119633113013a33de52d76887de09a06a81943475bc1de3efe0a639515a5fee314f5ba074e6d849499e4b4f
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 00d5fa51f8dded94d7429700fb91a0c1ead00ae2c7fd27089f0c5b63e6eca36197fe46384631872690a66f390c5e27198e99006ab77ae472692ab9c2ca903f36
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"pluralize@npm:8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 2044cfc34b2e8c88b73379ea4a36fc577db04f651c2909041b054c981cd863dd5373ebd030123ab058d194ae615d3a97cfdac653991e499d10caf592e8b3dc33
  languageName: node
  linkType: hard

"posix-character-classes@npm:^0.1.0":
  version: 0.1.1
  resolution: "posix-character-classes@npm:0.1.1"
  checksum: cce88011548a973b4af58361cd8f5f7b5a6faff8eef0901565802f067bcabf82597e920d4c97c22068464be3cbc6447af589f6cc8a7d813ea7165be60a0395bc
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 81e0027d731b7b3697ccd2129470ed9913ecb111e4ec175a12f0fcfab0096516373bf0af2fef132af50cafb0a905b74ff57996d615f59512bb9ac7378fcc64ab
  languageName: node
  linkType: hard

"prettier@npm:^2.3.2":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 463ea8f9a0946cd5b828d8cf27bd8b567345cf02f56562d5ecde198b91f47a76b7ac9eae0facd247ace70e927143af6135e8cf411986b8cb8478784a4d6d724a
  languageName: node
  linkType: hard

"pretty-format@npm:^28.0.0, pretty-format@npm:^28.1.3":
  version: 28.1.3
  resolution: "pretty-format@npm:28.1.3"
  dependencies:
    "@jest/schemas": ^28.1.3
    ansi-regex: ^5.0.1
    ansi-styles: ^5.0.0
    react-is: ^18.0.0
  checksum: 596d8b459b6fdac7dcbd70d40169191e889939c17ffbcc73eebe2a9a6f82cdbb57faffe190274e0a507d9ecdf3affadf8a9b43442a625eecfbd2813b9319660f
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: 9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: 16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pstree.remy@npm:^1.1.8":
  version: 1.1.8
  resolution: "pstree.remy@npm:1.1.8"
  checksum: 30f78c88ce6393cb3f7834216cb6e282eb83c92ccb227430d4590298ab2811bc4a4745f850a27c5178e79a8f3e316591de0fec87abc19da648c2b3c6eb766d14
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: 5ad655cb2a7738b4bcf6406b24ad0970d680649d996b55ad20d1be8e0c02394034e4c45ff7cd105d87f1e9b96a0e3d06fd28e11fae8875da26e7f7a8e2c9726f
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"q@npm:^1.5.1":
  version: 1.5.1
  resolution: "q@npm:1.5.1"
  checksum: 7855fbdba126cb7e92ef3a16b47ba998c0786ec7fface236e3eb0135b65df36429d91a86b1fff3ab0927b4ac4ee88a2c44527c7c3b8e2a37efbec9fe34803df4
  languageName: node
  linkType: hard

"qs@npm:6.11.0":
  version: 6.11.0
  resolution: "qs@npm:6.11.0"
  dependencies:
    side-channel: ^1.0.4
  checksum: 4e4875e4d7c7c31c233d07a448e7e4650f456178b9dd3766b7cfa13158fdb24ecb8c4f059fa91e820dc6ab9f2d243721d071c9c0378892dcdad86e9e9a27c68f
  languageName: node
  linkType: hard

"qs@npm:^6.11.0":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: ^1.1.0
  checksum: 8ea5d91bf34f440598ee389d4a7d95820e3b837d3fd9f433871f7924801becaa0cd3b3b4628d49a7784d06a8aea9bc4554d2b6d8d584e2d221dc06238a42909c
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"quick-lru@npm:^4.0.1":
  version: 4.0.1
  resolution: "quick-lru@npm:4.0.1"
  checksum: f9b1596fa7595a35c2f9d913ac312fede13d37dc8a747a51557ab36e11ce113bbe88ef4c0154968845559a7709cb6a7e7cbe75f7972182451cd45e7f057a334d
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: 50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.1":
  version: 2.5.1
  resolution: "raw-body@npm:2.5.1"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: 5dad5a3a64a023b894ad7ab4e5c7c1ce34d3497fc7138d02f8c88a3781e68d8a55aa7d4fd3a458616fa8647cc228be314a1c03fb430a07521de78b32c4dd09d2
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"read-pkg-up@npm:^7.0.1":
  version: 7.0.1
  resolution: "read-pkg-up@npm:7.0.1"
  dependencies:
    find-up: ^4.1.0
    read-pkg: ^5.2.0
    type-fest: ^0.8.1
  checksum: 82b3ac9fd7c6ca1bdc1d7253eb1091a98ff3d195ee0a45386582ce3e69f90266163c34121e6a0a02f1630073a6c0585f7880b3865efcae9c452fa667f02ca385
  languageName: node
  linkType: hard

"read-pkg@npm:^5.2.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^2.5.0
    parse-json: ^5.0.0
    type-fest: ^0.6.0
  checksum: b51a17d4b51418e777029e3a7694c9bd6c578a5ab99db544764a0b0f2c7c0f58f8a6bc101f86a6fceb8ba6d237d67c89acf6170f6b98695d0420ddc86cf109fb
  languageName: node
  linkType: hard

"readable-stream@npm:3, readable-stream@npm:^3.0.0, readable-stream@npm:^3.0.2, readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.0, readable-stream@npm:^2.0.2, readable-stream@npm:^2.0.5, readable-stream@npm:^2.2.2, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readable-stream@npm:^4.7.0":
  version: 4.7.0
  resolution: "readable-stream@npm:4.7.0"
  dependencies:
    abort-controller: ^3.0.0
    buffer: ^6.0.3
    events: ^3.3.0
    process: ^0.11.10
    string_decoder: ^1.3.0
  checksum: fd86d068da21cfdb10f7a4479f2e47d9c0a9b0c862fc0c840a7e5360201580a55ac399c764b12a4f6fa291f8cee74d9c4b7562e0d53b3c4b2769f2c98155d957
  languageName: node
  linkType: hard

"readable-web-to-node-stream@npm:^3.0.0":
  version: 3.0.4
  resolution: "readable-web-to-node-stream@npm:3.0.4"
  dependencies:
    readable-stream: ^4.7.0
  checksum: 2dc417d5d0b0c0191fcf57f87df3b2853db21d1da5554ec32b1e1c5a515e5a1243fc077a23f74046d711c2d736628f64b31054a8379b95bb016212430b5110c5
  languageName: node
  linkType: hard

"readdir-glob@npm:^1.1.2":
  version: 1.1.3
  resolution: "readdir-glob@npm:1.1.3"
  dependencies:
    minimatch: ^5.1.0
  checksum: a37e0716726650845d761f1041387acd93aa91b28dd5381950733f994b6c349ddc1e21e266ec7cc1f9b92e205a7a972232f9b89d5424d07361c2c3753d5dbace
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"rechoir@npm:^0.6.2":
  version: 0.6.2
  resolution: "rechoir@npm:0.6.2"
  dependencies:
    resolve: ^1.1.6
  checksum: 22c4bb32f4934a9468468b608417194f7e3ceba9a508512125b16082c64f161915a28467562368eeb15dc16058eb5b7c13a20b9eb29ff9927d1ebb3b5aa83e84
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: ^4.0.0
    strip-indent: ^3.0.0
  checksum: d64a6b5c0b50eb3ddce3ab770f866658a2b9998c678f797919ceb1b586bab9259b311407280bd80b804e2a7c7539b19238ae6a2a20c843f1a7fcff21d48c2eae
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.1.13":
  version: 0.1.14
  resolution: "reflect-metadata@npm:0.1.14"
  checksum: 3a6190c7f6cb224f26a012d11f9e329360c01c1945e2cbefea23976a8bacf9db6b794aeb5bf18adcb673c448a234fbc06fc41853c00a6c206b30f0777ecf019e
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.2.1":
  version: 0.2.2
  resolution: "reflect-metadata@npm:0.2.2"
  checksum: 1cd93a15ea291e420204955544637c264c216e7aac527470e393d54b4bb075f10a17e60d8168ec96600c7e0b9fcc0cb0bb6e91c3fbf5b0d8c9056f04e6ac1ec2
  languageName: node
  linkType: hard

"regex-not@npm:^1.0.0, regex-not@npm:^1.0.2":
  version: 1.0.2
  resolution: "regex-not@npm:1.0.2"
  dependencies:
    extend-shallow: ^3.0.2
    safe-regex: ^1.1.0
  checksum: a0f8d6045f63b22e9759db10e248369c443b41cedd7dba0922d002b66c2734bc2aef0d98c4d45772d1f756245f4c5203856b88b9624bba2a58708858a8d485d6
  languageName: node
  linkType: hard

"repeat-element@npm:^1.1.2":
  version: 1.1.4
  resolution: "repeat-element@npm:1.1.4"
  checksum: 81aa8d82bc845780803ef52df3533fa399974b99df571d0bb86e91f0ffca9ee4b9c4e8e5e72af087938cc28d2aef93d106a6d01da685d72ce96455b90a9f9f69
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 87fa21bfdb2fbdedc44b9a5b118b7c1239bdd2c2c1e42742ef9119b7d412a5137a1d23f1a83dc6bb686f4f27429ac6f542e3d923090b44181bafa41e8ac0174d
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: e608a3ebd15356264653c32d7ecbc8fd702f94c6703ea4ac2fb81d9c359180cba0ae2e6b71faa446631ed6145454d5a56b227efc33a2d40638ac13f8beb20ee4
  languageName: node
  linkType: hard

"resolve-dir@npm:^1.0.0, resolve-dir@npm:^1.0.1":
  version: 1.0.1
  resolution: "resolve-dir@npm:1.0.1"
  dependencies:
    expand-tilde: ^2.0.0
    global-modules: ^1.0.0
  checksum: 8197ed13e4a51d9cd786ef6a09fc83450db016abe7ef3311ca39389b3e508d77c26fe0cf0483a9b407b8caa2764bb5ccc52cf6a017ded91492a416475a56066f
  languageName: node
  linkType: hard

"resolve-from@npm:5.0.0, resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-global@npm:1.0.0, resolve-global@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-global@npm:1.0.0"
  dependencies:
    global-dirs: ^0.1.1
  checksum: fda6ba81a07a0124756ce956dd871ca83763973326d8617143dab38d9c9afc666926604bfe8f0bfd046a9a285347568f32ceb3d4c55a1cb9de5614cca001a21c
  languageName: node
  linkType: hard

"resolve-url@npm:^0.2.1":
  version: 0.2.1
  resolution: "resolve-url@npm:0.2.1"
  checksum: c285182cfcddea13a12af92129ce0569be27fb0074ffaefbd3ba3da2eac2acecdfc996d435c4982a9fa2b4708640e52837c9153a5ab9255886a00b0b9e8d2a54
  languageName: node
  linkType: hard

"resolve.exports@npm:^1.1.0":
  version: 1.1.1
  resolution: "resolve.exports@npm:1.1.1"
  checksum: 902ac0c643d03385b2719f3aed8c289e9d4b2dd42c993de946de5b882bc18b74fad07d672d29f71a63c251be107f6d0d343e2390ca224c04ba9a8b8e35d1653a
  languageName: node
  linkType: hard

"resolve@npm:^1.1.6, resolve@npm:^1.10.0, resolve@npm:^1.20.0":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.6#~builtin<compat/resolve>, resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=07638b"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: ^2.0.0
    signal-exit: ^3.0.2
  checksum: f5b335bee06f440445e976a7031a3ef53691f9b7c4a9d42a469a0edaf8a5508158a0d561ff2b26a1f4f38783bcca2c0e5c3a44f927326f6694d5b44d7a4993e6
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: 8051a371d6aa67ff21625fa94e2357bd81ffdc96267f3fb0fc4aaf4534028343836548ef34c240ffa8c25b280ca35eb36be00b3cb2133fa4f51896d7e73c6b4f
  languageName: node
  linkType: hard

"ret@npm:~0.1.10":
  version: 0.1.15
  resolution: "ret@npm:0.1.15"
  checksum: 01f77cad0f7ea4f955852c03d66982609893edc1240c0c964b4c9251d0f9fb6705150634060d169939b096d3b77f4c84d6b6098a5b5d340160898c8581f1f63f
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c19ef26e4e188f408922c46f7ff480d38e8dfc55d448310dfb518736b23ed2c4f547fb64a6ed5bdba92cd7e7ddc889d36ff78f794816d5e71498d645ef476107
  languageName: node
  linkType: hard

"rfdc@npm:^1.3.0":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 4614e4292356cafade0b6031527eea9bc90f2372a22c012313be1dcc69a3b90c7338158b414539be863fa95bfcb2ddcd0587be696841af4e6679d85e62c060c7
  languageName: node
  linkType: hard

"rimraf@npm:2":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: ./bin.js
  checksum: 4eef73d406c6940927479a3a9dee551e14a54faf54b31ef861250ac815172bade86cc6f7d64a4dc5e98b65e4b18a2e1c9ff3b68d296be0c748413f092bb0dd40
  languageName: node
  linkType: hard

"rimraf@npm:3.0.2, rimraf@npm:^3.0.0, rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"rimraf@npm:4.1.2":
  version: 4.1.2
  resolution: "rimraf@npm:4.1.2"
  bin:
    rimraf: dist/cjs/src/bin.js
  checksum: 71a492251d9553cb93a0e48029e70ab4d961bf56ceaeaed05dc0d7b7283f611bcc00ae66dfcbe428011ee445abc8fef849b72c6fd4ef108970954b97aca806b3
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 7da4fd0e15118ee05b918359462cfa1e7fe4b1228c7765195a45b55576e8c15b95db513b8466ec89129666f4af45ad978a3057a02139afba1a63512a2d9644cc
  languageName: node
  linkType: hard

"run-async@npm:^2.2.0, run-async@npm:^2.4.0":
  version: 2.4.1
  resolution: "run-async@npm:2.4.1"
  checksum: 35a68c8f1d9664f6c7c2e153877ca1d6e4f886e5ca067c25cdd895a6891ff3a1466ee07c63d6a9be306e9619ff7d509494e6d9c129516a36b9fd82263d579ee1
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: 200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"rxjs@npm:6.6.7, rxjs@npm:^6.4.0, rxjs@npm:^6.6.0":
  version: 6.6.7
  resolution: "rxjs@npm:6.6.7"
  dependencies:
    tslib: ^1.9.0
  checksum: e556a13a9aa89395e5c9d825eabcfa325568d9c9990af720f3f29f04a888a3b854f25845c2b55875d875381abcae2d8100af9cacdc57576e7ed6be030a01d2fe
  languageName: node
  linkType: hard

"rxjs@npm:7.8.1, rxjs@npm:^7.2.0, rxjs@npm:^7.5.5":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: ^2.1.0
  checksum: 3c49c1ecd66170b175c9cacf5cef67f8914dcbc7cd0162855538d365c83fea631167cacb644b3ce533b2ea0e9a4d0b12175186985f89d75abe73dbd8f7f06f68
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex@npm:1.1.0"
  dependencies:
    ret: ~0.1.10
  checksum: 547d58aa5184cbef368fd5ed5f28d20f911614748c5da6b35f53fd6626396707587251e6e3d1e3010fd3ff1212e413841b8825eaa5f317017ca62a30899af31a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"saxes@npm:^5.0.1":
  version: 5.0.1
  resolution: "saxes@npm:5.0.1"
  dependencies:
    xmlchars: ^2.2.0
  checksum: b7476c41dbe1c3a89907d2546fecfba234de5e66743ef914cde2603f47b19bed09732ab51b528ad0f98b958369d8be72b6f5af5c9cfad69972a73d061f0b3952
  languageName: node
  linkType: hard

"schema-utils@npm:^3.1.0, schema-utils@npm:^3.1.1":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: fafdbde91ad8aa1316bc543d4b61e65ea86970aebbfb750bfb6d8a6c287a23e415e0e926c2498696b242f63af1aab8e585252637fabe811fd37b604351da6500
  languageName: node
  linkType: hard

"schema-utils@npm:^4.3.0":
  version: 4.3.0
  resolution: "schema-utils@npm:4.3.0"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.9.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.1.0
  checksum: c23f0fa73ef71a01d4a2bb7af4c91e0d356ec640e071aa2d06ea5e67f042962bb7ac7c29a60a295bb0125878801bc3209197a2b8a833dd25bd38e37c3ed21427
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5, semver@npm:^5.4.1, semver@npm:^5.7.1":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:7.5.4":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 5160b06975a38b11c1ab55950cb5b8a23db78df88275d3d8a42ccf1f29e55112ac995b3a26a522c36e3b5f76b0445f1eef70d696b8c7862a2b4303d7b0e7609e
  languageName: node
  linkType: hard

"semver@npm:7.x, semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.3.8, semver@npm:^7.5.3, semver@npm:^7.5.4":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:~7.0.0":
  version: 7.0.0
  resolution: "semver@npm:7.0.0"
  bin:
    semver: bin/semver.js
  checksum: 7fd341680a967a0abfd66f3a7d36ba44e52ff5d3e799e9a6cdb01a68160b64ef09be82b4af05459effeecdd836f002c2462555d2821cd890dfdfe36a0d9f56a5
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 0eb134d6a51fc13bbcb976a1f4214ea1e33f242fae046efc311e80aff66c7a43603e26a79d9d06670283a13000e51be6e0a2cb80ff0942eaf9f1cd30b7ae736a
  languageName: node
  linkType: hard

"seq-queue@npm:^0.0.5":
  version: 0.0.5
  resolution: "seq-queue@npm:0.0.5"
  checksum: ec870fc392f0e6e99ec0e551c3041c1a66144d1580efabae7358e572de127b0ad2f844c95a4861d2e6203f836adea4c8196345b37bed55331ead8f22d99ac84c
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: ^2.1.0
  checksum: 2dd09ef4b65a1289ba24a788b1423a035581bef60817bea1f01eda8e3bda623f86357665fe7ac1b50f6d4f583f97db9615b3f07b2a2e8cbcb75033965f771dd2
  languageName: node
  linkType: hard

"serve-static@npm:1.15.0":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.18.0
  checksum: fa9f0e21a540a28f301258dfe1e57bb4f81cd460d28f0e973860477dd4acef946a1f41748b5bd41c73b621bea2029569c935faa38578fd34cd42a9b4947088ba
  languageName: node
  linkType: hard

"service-taa@workspace:service":
  version: 0.0.0-use.local
  resolution: "service-taa@workspace:service"
  dependencies:
    "@aws-sdk/client-s3": ^3.804.0
    "@nestjs/axios": ^3.0.0
    "@nestjs/cli": ^8.0.0
    "@nestjs/common": ^9.0.0
    "@nestjs/config": ^4.0.2
    "@nestjs/core": ^9.0.0
    "@nestjs/jwt": ^11.0.0
    "@nestjs/passport": ^11.0.5
    "@nestjs/platform-express": ^9.0.0
    "@nestjs/schematics": ^8.0.0
    "@nestjs/swagger": ^6.2.1
    "@nestjs/testing": ^9.0.0
    "@nestjs/typeorm": ^9.0.1
    "@types/bcrypt": ^5.0.2
    "@types/cookie-parser": 1.4.3
    "@types/express": ^4.17.13
    "@types/jest": 28.1.4
    "@types/jsonwebtoken": ^9.0.9
    "@types/lodash": ^4.14.191
    "@types/multer": ^1.4.12
    "@types/node": ^16.0.0
    "@types/nodemailer": ^6.4.17
    "@types/supertest": ^2.0.11
    "@typescript-eslint/eslint-plugin": ^5.0.0
    "@typescript-eslint/parser": ^5.0.0
    axios: ^1.8.4
    bcrypt: ^5.1.1
    class-transformer: ^0.5.1
    class-validator: ^0.14.1
    cookie-parser: 1.4.6
    eslint: ^8.0.1
    eslint-config-prettier: ^8.3.0
    eslint-plugin-prettier: ^4.0.0
    eslint-plugin-simple-import-sort: 10.0.0
    eslint-plugin-unused-imports: 2.0.0
    exceljs: ^4.4.0
    google-auth-library: ^9.9.0
    jest: 28.1.2
    jsonwebtoken: ^9.0.2
    lodash: ^4.17.21
    mysql2: ^3.14.0
    nestjs-form-data: ^1.8.7
    nodemailer: ^6.10.0
    nodemon: ^2.0.20
    passport: ^0.7.0
    passport-jwt: ^4.0.1
    prettier: ^2.3.2
    reflect-metadata: ^0.1.13
    rimraf: ^3.0.2
    rxjs: ^7.2.0
    source-map-support: ^0.5.20
    supertest: ^6.1.3
    ts-jest: 28.0.5
    ts-loader: ^9.2.3
    ts-node: ^10.0.0
    tsconfig-paths: 4.0.0
    typeorm: ^0.3.12
    typeorm-transactional: ^0.4.1
    typescript: ^4.3.5
    uuid: ^11.1.0
  languageName: unknown
  linkType: soft

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-value@npm:^2.0.0, set-value@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-value@npm:2.0.1"
  dependencies:
    extend-shallow: ^2.0.1
    is-extendable: ^0.1.1
    is-plain-object: ^2.0.3
    split-string: ^3.0.1
  checksum: 4c40573c4f6540456e4b38b95f570272c4cfbe1d12890ad4057886da8535047cd772dfadf5b58e2e87aa244dfb4c57e3586f6716b976fc47c5144b6b09e1811b
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5, setimmediate@npm:~1.0.4":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.11":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  bin:
    sha.js: ./bin.js
  checksum: b7a371bca8821c9cc98a0aeff67444a03d48d745cb103f17228b96793f455f0eb0a691941b89ea1e60f6359207e36081d9be193252b0f128e0daf9cfea2815a5
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shelljs@npm:0.8.5":
  version: 0.8.5
  resolution: "shelljs@npm:0.8.5"
  dependencies:
    glob: ^7.0.0
    interpret: ^1.0.0
    rechoir: ^0.6.2
  bin:
    shjs: bin/shjs
  checksum: feb25289a12e4bcd04c40ddfab51aff98a3729f5c2602d5b1a1b95f6819ec7804ac8147ebd8d9a85dfab69d501bcf92d7acef03247320f51c1552cec8d8e2382
  languageName: node
  linkType: hard

"shimmer@npm:^1.2.0":
  version: 1.2.1
  resolution: "shimmer@npm:1.2.1"
  checksum: ae8b27c389db2a00acfc8da90240f11577685a8f3e40008f826a3bea8b4f3b3ecd305c26be024b4a0fd3b123d132c1569d6e238097960a9a543b6c60760fb46a
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: 71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-update-notifier@npm:^1.0.7":
  version: 1.1.0
  resolution: "simple-update-notifier@npm:1.1.0"
  dependencies:
    semver: ~7.0.0
  checksum: 3cbbbc71a5d9a2924f0e3f42fbf3cbe1854bfe142203456b00d5233bdbbdeb5091b8067cd34fb00f81dbfbc29fc30dbb6e026b3d58ea0551e3f26c0e64082092
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slice-ansi@npm:^3.0.0":
  version: 3.0.0
  resolution: "slice-ansi@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 88083c9d0ca67d09f8b4c78f68833d69cabbb7236b74df5d741ad572bbf022deaf243fa54009cd434350622a1174ab267710fcc80a214ecc7689797fe00cb27c
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 6c25678db1270d4793e0327620f1e0f9f5bea4630123f51e9e399191bc52c87d6e6de53ed33538609e5eacbd1fab769fae00f3705d08d029f02102a540648918
  languageName: node
  linkType: hard

"slice-ansi@npm:^5.0.0":
  version: 5.0.0
  resolution: "slice-ansi@npm:5.0.0"
  dependencies:
    ansi-styles: ^6.0.0
    is-fullwidth-code-point: ^4.0.0
  checksum: 2d4d40b2a9d5cf4e8caae3f698fe24ae31a4d778701724f578e984dcb485ec8c49f0c04dab59c401821e80fcdfe89cace9c66693b0244e40ec485d72e543914f
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"snapdragon-node@npm:^2.0.1":
  version: 2.1.1
  resolution: "snapdragon-node@npm:2.1.1"
  dependencies:
    define-property: ^1.0.0
    isobject: ^3.0.0
    snapdragon-util: ^3.0.1
  checksum: 7616e6a1ca054afe3ad8defda17ebe4c73b0800d2e0efd635c44ee1b286f8ac7900517314b5330862ce99b28cd2782348ee78bae573ff0f55832ad81d9657f3f
  languageName: node
  linkType: hard

"snapdragon-util@npm:^3.0.1":
  version: 3.0.1
  resolution: "snapdragon-util@npm:3.0.1"
  dependencies:
    kind-of: ^3.2.0
  checksum: 4441856d343399ba7f37f79681949d51b922e290fcc07e7bc94655a50f584befa4fb08f40c3471cd160e004660161964d8ff140cba49baa59aa6caba774240e3
  languageName: node
  linkType: hard

"snapdragon@npm:^0.8.1":
  version: 0.8.2
  resolution: "snapdragon@npm:0.8.2"
  dependencies:
    base: ^0.11.1
    debug: ^2.2.0
    define-property: ^0.2.5
    extend-shallow: ^2.0.1
    map-cache: ^0.2.2
    source-map: ^0.5.6
    source-map-resolve: ^0.5.0
    use: ^3.1.0
  checksum: dfdac1f73d47152d72fc07f4322da09bbddfa31c1c9c3ae7346f252f778c45afa5b03e90813332f02f04f6de8003b34a168c456f8bb719024d092f932520ffca
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: 5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"source-map-resolve@npm:^0.5.0":
  version: 0.5.3
  resolution: "source-map-resolve@npm:0.5.3"
  dependencies:
    atob: ^2.1.2
    decode-uri-component: ^0.2.0
    resolve-url: ^0.2.1
    source-map-url: ^0.4.0
    urix: ^0.1.0
  checksum: 410acbe93882e058858d4c1297be61da3e1533f95f25b95903edddc1fb719654e705663644677542d1fb78a66390238fad1a57115fc958a0724cf9bb509caf57
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 137539f8c453fa0f496ea42049ab5da4569f96781f6ac8e5bfda26937be9494f4e8891f523c5f98f0e85f71b35d74127a00c46f83f6a4f54672b58d53202565e
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.21, source-map-support@npm:^0.5.20, source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map-url@npm:^0.4.0":
  version: 0.4.1
  resolution: "source-map-url@npm:0.4.1"
  checksum: f8af0678500d536c7f643e32094d6718a4070ab4ca2d2326532512cfbe2d5d25a45849b4b385879326f2d7523bb3b686d0360dd347a3cda09fd89a5c28d4bc58
  languageName: node
  linkType: hard

"source-map@npm:0.7.3":
  version: 0.7.3
  resolution: "source-map@npm:0.7.3"
  checksum: 7d2ddb51f3d2451847692a9ac7808da2b2b3bf7aef92ece33128919040a7e74d9a5edfde7a781f035c974deff876afaf83f2e30484faffffb86484e7408f5d7c
  languageName: node
  linkType: hard

"source-map@npm:0.7.4, source-map@npm:^0.7.4":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: dc0cf3768fe23c345ea8760487f8c97ef6fca8a73c83cd7c9bf2fde8bc2c34adb9c0824d6feb14bc4f9e37fb522e18af621543f1289038a66ac7586da29aa7dc
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"sourcemap-codec@npm:^1.4.4":
  version: 1.4.8
  resolution: "sourcemap-codec@npm:1.4.8"
  checksum: f099279fdaae070ff156df7414bbe39aad69cdd615454947ed3e19136bfdfcb4544952685ee73f56e17038f4578091e12b17b283ed8ac013882916594d95b9e6
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: 49208f008618b9119208b0dadc9208a3a55053f4fd6a0ae8116861bd22696fc50f4142a35ebfdb389e05ccf2de8ad142573fefc9e26f670522d899f7b2fe7386
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: 37217b7762ee0ea0d8b7d0c29fd48b7e4dfb94096b109d6255b589c561f57da93bf4e328c0290046115961b9209a8051ad9f525e48d433082fc79f496a4ea940
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: 6f8a41c87759fa184a58713b86c6a8b028250f158159f1d03ed9d1b6ee4d9eefdc74181c8ddc581a341aa971c3e7b79e30b59c23b05d2436d5de1c30bdef7171
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.21
  resolution: "spdx-license-ids@npm:3.0.21"
  checksum: ecb24c698d8496aa9efe23e0b1f751f8a7a89faedcdfcbfabae772b546c2db46ccde8f3bc447a238eb86bbcd4f73fea88720ef3b8394f7896381bec3d7736411
  languageName: node
  linkType: hard

"split-string@npm:^3.0.1, split-string@npm:^3.0.2":
  version: 3.1.0
  resolution: "split-string@npm:3.1.0"
  dependencies:
    extend-shallow: ^3.0.0
  checksum: 72d7cd625445c7af215130e1e2bc183013bb9dd48a074eda1d35741e2b0dcb355e6df5b5558a62543a24dcec37dd1d6eb7a6228ff510d3c9de0f3dc1d1da8a70
  languageName: node
  linkType: hard

"split2@npm:^3.0.0, split2@npm:^3.2.2":
  version: 3.2.2
  resolution: "split2@npm:3.2.2"
  dependencies:
    readable-stream: ^3.0.0
  checksum: 2dad5603c52b353939befa3e2f108f6e3aff42b204ad0f5f16dd12fd7c2beab48d117184ce6f7c8854f9ee5ffec6faae70d243711dd7d143a9f635b4a285de4e
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"sqlstring@npm:^2.3.2":
  version: 2.3.3
  resolution: "sqlstring@npm:2.3.3"
  checksum: 3b5dd7badb3d6312f494cfa6c9a381ee630fbe3dbd571c4c9eb8ecdb99a7bf5a1f7a5043191d768797f6b3c04eed5958ac6a5f948b998f0a138294c6d3125fbd
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stack-chain@npm:^1.3.7":
  version: 1.3.7
  resolution: "stack-chain@npm:1.3.7"
  checksum: 3a8693834646c2007d74d3466d6972dc2cfe137cbec40552ded9e3b99607e0037d893b3cbf631f1ff3035ec125772e195f2e0bea2c3d7f126d0e3171db69dc19
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: ^2.0.0
  checksum: 651c9f87667e077584bbe848acaecc6049bc71979f1e9a46c7b920cad4431c388df0f51b8ad7cfd6eed3db97a2878d0fc8b3122979439ea8bac29c61c95eec8a
  languageName: node
  linkType: hard

"static-extend@npm:^0.1.1":
  version: 0.1.2
  resolution: "static-extend@npm:0.1.2"
  dependencies:
    define-property: ^0.2.5
    object-copy: ^0.1.0
  checksum: 284f5865a9e19d079f1badbcd70d5f9f82e7a08393f818a220839cd5f71729e89105e1c95322bd28e833161d484cee671380ca443869ae89578eef2bf55c0653
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: fbd9aecc2621364384d157f7e59426f4bfd385e8b424b5aaa79c83a6f5a1c8fd2e4e3289e95de1eb3511cb96bb333d6281a9919fafce760e4edb35b2cd2facab
  languageName: node
  linkType: hard

"string-argv@npm:^0.3.1":
  version: 0.3.2
  resolution: "string-argv@npm:0.3.2"
  checksum: 75c02a83759ad1722e040b86823909d9a2fc75d15dd71ec4b537c3560746e33b5f5a07f7332d1e3f88319909f82190843aa2f0a0d8c8d591ec08e93d5b8dec82
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: ^1.0.2
    strip-ansi: ^6.0.0
  checksum: 1cd77409c3d7db7bc59406f6bcc9ef0783671dcbabb23597a1177c166906ef2ee7c8290f78cae73a8aec858768f189d2cb417797df5e15ec4eb5e16b3346340c
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: 1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^2.1.0":
  version: 2.1.1
  resolution: "string-width@npm:2.1.1"
  dependencies:
    is-fullwidth-code-point: ^2.0.0
    strip-ansi: ^4.0.0
  checksum: e5f2b169fcf8a4257a399f95d069522f056e92ec97dbdcb9b0cdf14d688b7ca0b1b1439a1c7b9773cd79446cbafd582727279d6bfdd9f8edd306ea5e90e5b610
  languageName: node
  linkType: hard

"string-width@npm:^5.0.0, string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: 1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-ansi@npm:4.0.0"
  dependencies:
    ansi-regex: ^3.0.0
  checksum: d75d9681e0637ea316ddbd7d4d3be010b1895a17e885155e0ed6a39755ae0fd7ef46e14b22162e66a62db122d3a98ab7917794e255532ab461bb0a04feb03e7d
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.1.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: ^4.1.0
  checksum: de4658c8a097ce3b15955bc6008f67c0790f85748bdc025b7bc8c52c7aee94bc4f9e50624516150ed173c3db72d851826cd57e7a85fe4e4bb6dbbebd5d297fdf
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:4.0.0, strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 26abad1172d6bc48985ab9a5f96c21e440f6e7e476686de49be813b5a59b3566dccb5c525b831ec54fe348283b47f3ffb8e080bc3f965fde12e84df23f6bb7ef
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 51201f50e021ef16672593d7434ca239441b7b760e905d9f33df6e4f3954ff54ec0e0a06f100d028af0982d6f25c35cd5cda2ce34eaebccd0250b8befb90d8f1
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: ^1.0.0
  checksum: ae0deaf41c8d1001c5d4fbe16cb553865c1863da4fae036683b474fa926af9fc121e155cb3fc57a68262b2ae7d5b8420aa752c97a6428c315d00efe2a3875679
  languageName: node
  linkType: hard

"strip-json-comments@npm:3.0.1":
  version: 3.0.1
  resolution: "strip-json-comments@npm:3.0.1"
  checksum: 8ebd59befd19211d055a1236aaf7452041d1a532dc1ace461fc97c2105f53a341d302bec4bacdbdbd36faa5e95d30d38fe89835f2fe4b4e61f3c17b26196f1c7
  languageName: node
  linkType: hard

"strip-json-comments@npm:3.1.1, strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strnum@npm:^1.0.5":
  version: 1.1.2
  resolution: "strnum@npm:1.1.2"
  checksum: a0fce2498fa3c64ce64a40dada41beb91cabe3caefa910e467dc0518ef2ebd7e4d10f8c2202a6104f1410254cae245066c0e94e2521fb4061a5cb41831952392
  languageName: node
  linkType: hard

"strtok3@npm:^6.2.4":
  version: 6.3.0
  resolution: "strtok3@npm:6.3.0"
  dependencies:
    "@tokenizer/token": ^0.3.0
    peek-readable: ^4.1.0
  checksum: 8f1483a2a6758404502f2fc431586fcf37d747b10b125596ab5ec92319c247dd1195f82ba0bc2eaa582db3d807b5cca4b67ff61411756fec6622d051f8e255c2
  languageName: node
  linkType: hard

"superagent@npm:^8.1.2":
  version: 8.1.2
  resolution: "superagent@npm:8.1.2"
  dependencies:
    component-emitter: ^1.3.0
    cookiejar: ^2.1.4
    debug: ^4.3.4
    fast-safe-stringify: ^2.1.1
    form-data: ^4.0.0
    formidable: ^2.1.2
    methods: ^1.1.2
    mime: 2.6.0
    qs: ^6.11.0
    semver: ^7.3.8
  checksum: 016416fc9c3d3a04fb648bc0efb3d3d5c9d96da00de47e4a625d9976d28c6c37ab0a7f185f2c3ec6d653ee8bb522f70fba0c1072aea7774341a6c0269a9fa77f
  languageName: node
  linkType: hard

"supertest@npm:^6.1.3":
  version: 6.3.4
  resolution: "supertest@npm:6.3.4"
  dependencies:
    methods: ^1.1.2
    superagent: ^8.1.2
  checksum: f8c0b6c73b5e87da31feee6ccb36e7af766a438513cad89d6907f22c97edd83b1e765b4c8de955d5f7af4bca5fd0aaf9149ff48e21567dd290b326a8633af2a7
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0, supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-color@npm:^9.2.1":
  version: 9.4.0
  resolution: "supports-color@npm:9.4.0"
  checksum: 6c24e6b2b64c6a60e5248490cfa50de5924da32cf09ae357ad8ebbf305cc5d2717ba705a9d4cb397d80bbf39417e8fdc8d7a0ce18bd0041bf7b5b456229164e4
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.0.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: ^4.0.0
    supports-color: ^7.0.0
  checksum: 4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"swagger-ui-dist@npm:4.18.2":
  version: 4.18.2
  resolution: "swagger-ui-dist@npm:4.18.2"
  checksum: 63dd5e0af727edc5e10d393c6574bec248b0c812d68bbfbad1681512538a78726c77a71e248a7eb3afa78f8dad97ee00b8ec7a9bc69e13b7064676b7e6032ee6
  languageName: node
  linkType: hard

"symbol-observable@npm:4.0.0":
  version: 4.0.0
  resolution: "symbol-observable@npm:4.0.0"
  checksum: 5e9a3ab08263a6be8cbee76587ad5880dcc62a47002787ed5ebea56b1eb30dc87da6f0183d67e88286806799fbe21c69077fbd677be4be2188e92318d6c6f31d
  languageName: node
  linkType: hard

"taa-service@workspace:.":
  version: 0.0.0-use.local
  resolution: "taa-service@workspace:."
  dependencies:
    "@commitlint/cli": 17.1.2
    "@commitlint/config-conventional": 17.1.0
    "@commitlint/prompt": 17.1.2
    "@nestjs/cli": 9.2.0
    commitizen: 4.1.2
    husky: 7.0.4
    lint-staged: 12.3.7
    ts-node: 10.9.1
  languageName: unknown
  linkType: soft

"tapable@npm:^2.1.1, tapable@npm:^2.2.0, tapable@npm:^2.2.1":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: bc40e6efe1e554d075469cedaba69a30eeb373552aaf41caeaaa45bf56ffacc2674261b106245bd566b35d8f3329b52d838e851ee0a852120acae26e622925c9
  languageName: node
  linkType: hard

"tar-stream@npm:^2.2.0":
  version: 2.2.0
  resolution: "tar-stream@npm:2.2.0"
  dependencies:
    bl: ^4.0.3
    end-of-stream: ^1.4.1
    fs-constants: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^3.1.1
  checksum: 2f4c910b3ee7196502e1ff015a7ba321ec6ea837667220d7bcb8d0852d51cb04b87f7ae471008a6fb8f5b1a1b5078f62f3a82d30c706f20ada1238ac797e7692
  languageName: node
  linkType: hard

"tar@npm:^6.1.11":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"terminal-link@npm:^2.0.0":
  version: 2.1.1
  resolution: "terminal-link@npm:2.1.1"
  dependencies:
    ansi-escapes: ^4.2.1
    supports-hyperlinks: ^2.0.0
  checksum: 947458a5cd5408d2ffcdb14aee50bec8fb5022ae683b896b2f08ed6db7b2e7d42780d5c8b51e930e9c322bd7c7a517f4fa7c76983d0873c83245885ac5ee13e3
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.1.3":
  version: 5.3.11
  resolution: "terser-webpack-plugin@npm:5.3.11"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.25
    jest-worker: ^27.4.5
    schema-utils: ^4.3.0
    serialize-javascript: ^6.0.2
    terser: ^5.31.1
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 4794274f445dc589f4c113c75a55ce51364ccf09bfe8a545cdb462e3f752bf300ea91f072fa28bbed291bbae03274da06fe4eca180e784fb8a43646aa7dbcaef
  languageName: node
  linkType: hard

"terser@npm:^5.31.1":
  version: 5.39.0
  resolution: "terser@npm:5.39.0"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: 83326545ea1aecd6261030568b6191ccfa4cb6aa61d9ea41746a52479f50017a78b77e4725fbbc207c5df841ffa66a773c5ac33636e95c7ab94fe7e0379ae5c7
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": ^0.1.2
    glob: ^7.1.4
    minimatch: ^3.0.4
  checksum: 019d33d81adff3f9f1bfcff18125fb2d3c65564f437d9be539270ee74b994986abb8260c7c2ce90e8f30162178b09dbbce33c6389273afac4f36069c48521f57
  languageName: node
  linkType: hard

"text-extensions@npm:^1.0.0":
  version: 1.9.0
  resolution: "text-extensions@npm:1.9.0"
  checksum: 9ad5a9f723a871e2d884e132d7e93f281c60b5759c95f3f6b04704856548715d93a36c10dbaf5f12b91bf405f0cf3893bf169d4d143c0f5509563b992d385443
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 02805740c12851ea5982686810702e2f14369a5f4c5c40a836821e3eefc65ffeec3131ba324692a37608294b0fd8c1e55a2dd571ffed4909822787668ddbee5c
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: 9b896a22735e8122754fe70f1d65f7ee691c1d70b1f116fda04fea103d0f9b356e3676cb789506e3909ae0486a79a476e4914b0f92472c2e093d206aed4b7d6b
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: f375aeb2b05c100a456a30bc3ed07ef03a39cbdefe02e0403fb714b8c7e57eeaad1a2f5c4ecfb9ce554ce3db9c2b024eba144843cd9e344566d9fcee73b04767
  languageName: node
  linkType: hard

"through2@npm:^4.0.0":
  version: 4.0.2
  resolution: "through2@npm:4.0.2"
  dependencies:
    readable-stream: 3
  checksum: 3741564ae99990a4a79097fe7a4152c22348adc4faf2df9199a07a66c81ed2011da39f631e479fdc56483996a9d34a037ad64e76d79f18c782ab178ea9b6778c
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3, through@npm:^2.3.6, through@npm:^2.3.8":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: 4b09f3774099de0d4df26d95c5821a62faee32c7e96fb1f4ebd54a2d7c11c57fe88b0a0d49cf375de5fee5ae6bf4eb56dbbf29d07366864e2ee805349970d3cc
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 69863947b8c29cabad43fe0ce65cec5bb4b481d15d4b4b21e036b060b3edbf3bc7a5541de1bacb437bb3f7c4538f669752627fdf9b4aaf034cebd172ba373408
  languageName: node
  linkType: hard

"tmp@npm:^0.2.0":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 3e809d9c2f46817475b452725c2aaa5d11985cf18d32a7a970ff25b568438e2c076c2e8609224feef3b7923fa9749b74428e3e634f6b8e520c534eef2fd24125
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: f935537799c2d1922cb5d6d3805f594388f75338fe7a4a9dac41504dd539704ca4db45b883b52e7b0aa5b2fd5ddadb1452bf95cd23a69da2f793a843f9451cc9
  languageName: node
  linkType: hard

"to-object-path@npm:^0.3.0":
  version: 0.3.0
  resolution: "to-object-path@npm:0.3.0"
  dependencies:
    kind-of: ^3.0.2
  checksum: 731832a977614c03a770363ad2bd9e9c82f233261861724a8e612bb90c705b94b1a290a19f52958e8e179180bb9b71121ed65e245691a421467726f06d1d7fc3
  languageName: node
  linkType: hard

"to-regex-range@npm:^2.1.0":
  version: 2.1.1
  resolution: "to-regex-range@npm:2.1.1"
  dependencies:
    is-number: ^3.0.0
    repeat-string: ^1.6.1
  checksum: 440d82dbfe0b2e24f36dd8a9467240406ad1499fc8b2b0f547372c22ed1d092ace2a3eb522bb09bfd9c2f39bf1ca42eb78035cf6d2b8c9f5c78da3abc96cd949
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: 487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"to-regex@npm:^3.0.1, to-regex@npm:^3.0.2":
  version: 3.0.2
  resolution: "to-regex@npm:3.0.2"
  dependencies:
    define-property: ^2.0.2
    extend-shallow: ^3.0.2
    regex-not: ^1.0.2
    safe-regex: ^1.1.0
  checksum: 99d0b8ef397b3f7abed4bac757b0f0bb9f52bfd39167eb7105b144becfaa9a03756892352d01ac6a911f0c1ceef9f81db68c46899521a3eed054082042796120
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"token-types@npm:^4.1.1":
  version: 4.2.1
  resolution: "token-types@npm:4.2.1"
  dependencies:
    "@tokenizer/token": ^0.3.0
    ieee754: ^1.2.1
  checksum: e9a4a139deba9515770cd7ac36a8f53f953b9d035d309e88a66d706760dba0df420753f2b8bdee6b9f3cbff8d66b24e69571e8dea27baa7b378229ab1bcca399
  languageName: node
  linkType: hard

"touch@npm:^3.1.0":
  version: 3.1.1
  resolution: "touch@npm:3.1.1"
  bin:
    nodetouch: bin/nodetouch.js
  checksum: d2e4d269a42c846a22a29065b9af0b263de58effc85a1764bb7a2e8fc4b47700e9e2fcbd7eb1f5bffbb7c73d860f93600cef282b93ddac8f0b62321cb498b36e
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"traverse@npm:>=0.3.0 <0.4":
  version: 0.3.9
  resolution: "traverse@npm:0.3.9"
  checksum: 05f04ff1002f08f19b033187124764e2713186c7a7c0ad88172368df993edc4fa7580e829e252cef6b38375317b69671932ee3820381398a9e375aad3797f607
  languageName: node
  linkType: hard

"tree-kill@npm:1.2.2":
  version: 1.2.2
  resolution: "tree-kill@npm:1.2.2"
  bin:
    tree-kill: cli.js
  checksum: 7b1b7c7f17608a8f8d20a162e7957ac1ef6cd1636db1aba92f4e072dc31818c2ff0efac1e3d91064ede67ed5dc57c565420531a8134090a12ac10cf792ab14d2
  languageName: node
  linkType: hard

"trim-newlines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-newlines@npm:3.0.1"
  checksum: 03cfefde6c59ff57138412b8c6be922ecc5aec30694d784f2a65ef8dcbd47faef580b7de0c949345abdc56ec4b4abf64dd1e5aea619b200316e471a3dd5bf1f6
  languageName: node
  linkType: hard

"ts-jest@npm:28.0.5":
  version: 28.0.5
  resolution: "ts-jest@npm:28.0.5"
  dependencies:
    bs-logger: 0.x
    fast-json-stable-stringify: 2.x
    jest-util: ^28.0.0
    json5: ^2.2.1
    lodash.memoize: 4.x
    make-error: 1.x
    semver: 7.x
    yargs-parser: ^21.0.1
  peerDependencies:
    "@babel/core": ">=7.0.0-beta.0 <8"
    babel-jest: ^28.0.0
    jest: ^28.0.0
    typescript: ">=4.3"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-jest:
      optional: true
    esbuild:
      optional: true
  bin:
    ts-jest: cli.js
  checksum: 614ccbbc8ca55f149bdb8bb7448d2c4344b8f741933ac7a95af35d679aef1d3caef6e84ff3859fac3f17613361a0620f1ba532eefb9d3a8b8f6ab9d325036b67
  languageName: node
  linkType: hard

"ts-loader@npm:^9.2.3":
  version: 9.5.2
  resolution: "ts-loader@npm:9.5.2"
  dependencies:
    chalk: ^4.1.0
    enhanced-resolve: ^5.0.0
    micromatch: ^4.0.0
    semver: ^7.3.4
    source-map: ^0.7.4
  peerDependencies:
    typescript: "*"
    webpack: ^5.0.0
  checksum: d4f4e67f1365a8c4a929d26148611b6a82a9241bd988863386c9cc0c034eec8b14562206e09540fae38154595e0b3b9520b701b5c83c0e5d743c4016cd91d9f1
  languageName: node
  linkType: hard

"ts-node@npm:10.9.1":
  version: 10.9.1
  resolution: "ts-node@npm:10.9.1"
  dependencies:
    "@cspotcode/source-map-support": ^0.8.0
    "@tsconfig/node10": ^1.0.7
    "@tsconfig/node12": ^1.0.7
    "@tsconfig/node14": ^1.0.0
    "@tsconfig/node16": ^1.0.2
    acorn: ^8.4.1
    acorn-walk: ^8.1.1
    arg: ^4.1.0
    create-require: ^1.1.0
    diff: ^4.0.1
    make-error: ^1.1.1
    v8-compile-cache-lib: ^3.0.1
    yn: 3.1.1
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 95187932fb83f3901e22546bd2feeac7d2feb4f412f42ac3a595f049a23e8dcf70516dffb51866391228ea2dbcfaea039e250fb2bb334d48a86ab2b6aea0ae2d
  languageName: node
  linkType: hard

"ts-node@npm:^10.0.0, ts-node@npm:^10.8.1":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": ^0.8.0
    "@tsconfig/node10": ^1.0.7
    "@tsconfig/node12": ^1.0.7
    "@tsconfig/node14": ^1.0.0
    "@tsconfig/node16": ^1.0.2
    acorn: ^8.4.1
    acorn-walk: ^8.1.1
    arg: ^4.1.0
    create-require: ^1.1.0
    diff: ^4.0.1
    make-error: ^1.1.1
    v8-compile-cache-lib: ^3.0.1
    yn: 3.1.1
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 5f29938489f96982a25ba650b64218e83a3357d76f7bede80195c65ab44ad279c8357264639b7abdd5d7e75fc269a83daa0e9c62fd8637a3def67254ecc9ddc2
  languageName: node
  linkType: hard

"tsconfig-paths-webpack-plugin@npm:3.5.2":
  version: 3.5.2
  resolution: "tsconfig-paths-webpack-plugin@npm:3.5.2"
  dependencies:
    chalk: ^4.1.0
    enhanced-resolve: ^5.7.0
    tsconfig-paths: ^3.9.0
  checksum: 8b964284cfc58b5d3c4b13c31d7032d6774076d0af78f215644abd10da5b2c1b47eb91f9c084e709c84a70918fbc1cdb8730eba489c3f9be8039bf1e8daebcd4
  languageName: node
  linkType: hard

"tsconfig-paths-webpack-plugin@npm:4.0.0":
  version: 4.0.0
  resolution: "tsconfig-paths-webpack-plugin@npm:4.0.0"
  dependencies:
    chalk: ^4.1.0
    enhanced-resolve: ^5.7.0
    tsconfig-paths: ^4.0.0
  checksum: 60e84638014ace799b53db656105952497910c9bca1a80d01a4c079bed6a27c0c6d86e63b55fe58a5155920befb1a21fad291d2d48240e0322d02030783be1a5
  languageName: node
  linkType: hard

"tsconfig-paths@npm:3.14.1":
  version: 3.14.1
  resolution: "tsconfig-paths@npm:3.14.1"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.1
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 67cd2e400119a0063514782176a9e5c3420d43b7a550804ae65d833027379c0559dec44d21c93791825a3be3c2ec593f07cba658c4167dcbbadb048cb3d36fa3
  languageName: node
  linkType: hard

"tsconfig-paths@npm:4.0.0":
  version: 4.0.0
  resolution: "tsconfig-paths@npm:4.0.0"
  dependencies:
    json5: ^2.2.1
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 56e5e844dc148030743aedf79c4ee5e7490abaa7c68f41a3b88a13b9c15f96a35a39da4a8d378a1c1825d19ab9617a413a2274ae4ac05d71894301fd3993a6f2
  languageName: node
  linkType: hard

"tsconfig-paths@npm:4.1.2":
  version: 4.1.2
  resolution: "tsconfig-paths@npm:4.1.2"
  dependencies:
    json5: ^2.2.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 8993f3e160aaca196a5e1e65c26167a6d026cb48c8b80bfe41c1a37a280a471a23611a9ee85ae913714968a75f75314d580726b6b8f08486fe08a0f0161f1930
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.9.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 5b4f301a2b7a3766a986baf8fc0e177eb80bdba6e396792ff92dc23b5bca8bb279fc96517dcaaef63a3b49bebc6c4c833653ec58155780bc906bdbcf7dda0ef5
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^4.0.0":
  version: 4.2.0
  resolution: "tsconfig-paths@npm:4.2.0"
  dependencies:
    json5: ^2.2.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 09a5877402d082bb1134930c10249edeebc0211f36150c35e1c542e5b91f1047b1ccf7da1e59babca1ef1f014c525510f4f870de7c9bda470c73bb4e2721b3ea
  languageName: node
  linkType: hard

"tslib@npm:2.5.3":
  version: 2.5.3
  resolution: "tslib@npm:2.5.3"
  checksum: 4cb1817d34fae5b27d146e6c4a468d4155097d95c1335d0bc9690f11f33e63844806bf4ed6d97c30c72b8d85261b66cbbe16d871d9c594ac05701ec83e62a607
  languageName: node
  linkType: hard

"tslib@npm:^1.8.1, tslib@npm:^1.9.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.1.0, tslib@npm:^2.5.0, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tsutils@npm:^3.21.0":
  version: 3.21.0
  resolution: "tsutils@npm:3.21.0"
  dependencies:
    tslib: ^1.8.1
  peerDependencies:
    typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"
  checksum: 02f19e458ec78ead8fffbf711f834ad8ecd2cc6ade4ec0320790713dccc0a412b99e7fd907c4cda2a1dc602c75db6f12e0108e87a5afad4b2f9e90a24cabd5a2
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: 7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.18.0":
  version: 0.18.1
  resolution: "type-fest@npm:0.18.1"
  checksum: 303f5ecf40d03e1d5b635ce7660de3b33c18ed8ebc65d64920c02974d9e684c72483c23f9084587e9dd6466a2ece1da42ddc95b412a461794dd30baca95e2bac
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: dea9df45ea1f0aaa4e2d3bed3f9a0bfe9e5b2592bddb92eb1bf06e50bcf98dbb78189668cd8bc31a0511d3fc25539b4cd5c704497e53e93e2d40ca764b10bfc3
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: 0c585c26416fce9ecb5691873a1301b5aff54673c7999b6f925691ed01f5b9232db408cdbb0bd003d19f5ae284322523f44092d1f81ca0a48f11f7cf0be8cd38
  languageName: node
  linkType: hard

"type-fest@npm:^0.8.1":
  version: 0.8.1
  resolution: "type-fest@npm:0.8.1"
  checksum: dffbb99329da2aa840f506d376c863bd55f5636f4741ad6e65e82f5ce47e6914108f44f340a0b74009b0cb5d09d6752ae83203e53e98b1192cf80ecee5651636
  languageName: node
  linkType: hard

"type-is@npm:^1.6.18, type-is@npm:^1.6.4, type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 6005cb31df50eef8b1f3c780eb71a17925f3038a100d82f9406ac2ad1de5eb59f8e6decbdc145b3a1f8e5836e17b0c0002fb698b9fe2516b8f9f9ff602d36412
  languageName: node
  linkType: hard

"typeorm-transactional@npm:^0.4.1":
  version: 0.4.1
  resolution: "typeorm-transactional@npm:0.4.1"
  dependencies:
    "@types/cls-hooked": ^4.3.3
    cls-hooked: ^4.2.2
  peerDependencies:
    reflect-metadata: ">= 0.1.12"
    typeorm: ">= 0.2.8"
  checksum: af815e0a98a1ab70fe9c541295494bb050c6716662dab50928dafe27e98ec093545e75a0ddc9d3dd4ba71a67db56b884603393e79e2589e60a8d692ebfec3db5
  languageName: node
  linkType: hard

"typeorm@npm:^0.3.12":
  version: 0.3.20
  resolution: "typeorm@npm:0.3.20"
  dependencies:
    "@sqltools/formatter": ^1.2.5
    app-root-path: ^3.1.0
    buffer: ^6.0.3
    chalk: ^4.1.2
    cli-highlight: ^2.1.11
    dayjs: ^1.11.9
    debug: ^4.3.4
    dotenv: ^16.0.3
    glob: ^10.3.10
    mkdirp: ^2.1.3
    reflect-metadata: ^0.2.1
    sha.js: ^2.4.11
    tslib: ^2.5.0
    uuid: ^9.0.0
    yargs: ^17.6.2
  peerDependencies:
    "@google-cloud/spanner": ^5.18.0
    "@sap/hana-client": ^2.12.25
    better-sqlite3: ^7.1.2 || ^8.0.0 || ^9.0.0
    hdb-pool: ^0.1.6
    ioredis: ^5.0.4
    mongodb: ^5.8.0
    mssql: ^9.1.1 || ^10.0.1
    mysql2: ^2.2.5 || ^3.0.1
    oracledb: ^6.3.0
    pg: ^8.5.1
    pg-native: ^3.0.0
    pg-query-stream: ^4.0.0
    redis: ^3.1.1 || ^4.0.0
    sql.js: ^1.4.0
    sqlite3: ^5.0.3
    ts-node: ^10.7.0
    typeorm-aurora-data-api-driver: ^2.0.0
  peerDependenciesMeta:
    "@google-cloud/spanner":
      optional: true
    "@sap/hana-client":
      optional: true
    better-sqlite3:
      optional: true
    hdb-pool:
      optional: true
    ioredis:
      optional: true
    mongodb:
      optional: true
    mssql:
      optional: true
    mysql2:
      optional: true
    oracledb:
      optional: true
    pg:
      optional: true
    pg-native:
      optional: true
    pg-query-stream:
      optional: true
    redis:
      optional: true
    sql.js:
      optional: true
    sqlite3:
      optional: true
    ts-node:
      optional: true
    typeorm-aurora-data-api-driver:
      optional: true
  bin:
    typeorm: cli.js
    typeorm-ts-node-commonjs: cli-ts-node-commonjs.js
    typeorm-ts-node-esm: cli-ts-node-esm.js
  checksum: 7e4be724641beef86ae36289c87b6e66bfaf19a4313f089926d36d2d6f0d67f9314d942711c9d83ab8a174b8622148c2f7e83e6c1448d638ee3ab24469257814
  languageName: node
  linkType: hard

"typescript@npm:4.7.4":
  version: 4.7.4
  resolution: "typescript@npm:4.7.4"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 8c1c4007b6ce5b24c49f0e89173ab9e82687cc6ae54418d1140bb63b82d6598d085ac0f993fe3d3d1fbf87a2c76f1f81d394dc76315bc72c7a9f8561c5d8d205
  languageName: node
  linkType: hard

"typescript@npm:4.9.5, typescript@npm:^4.3.5":
  version: 4.9.5
  resolution: "typescript@npm:4.9.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 5f6cad2e728a8a063521328e612d7876e12f0d8a8390d3b3aaa452a6a65e24e9ac8ea22beb72a924fd96ea0a49ea63bb4e251fb922b12eedfb7f7a26475e5c56
  languageName: node
  linkType: hard

"typescript@npm:^4.6.4 || ^5.2.2":
  version: 5.7.3
  resolution: "typescript@npm:5.7.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b7580d716cf1824736cc6e628ab4cd8b51877408ba2be0869d2866da35ef8366dd6ae9eb9d0851470a39be17cbd61df1126f9e211d8799d764ea7431d5435afa
  languageName: node
  linkType: hard

"typescript@patch:typescript@4.7.4#~builtin<compat/typescript>":
  version: 4.7.4
  resolution: "typescript@patch:typescript@npm%3A4.7.4#~builtin<compat/typescript>::version=4.7.4&hash=a1c5e5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 2eb6e31b04fabec84a4d07b5d567deb5ef0a2971d89d9adb16895f148f7d8508adfb12074abc2efc6966805d3664e68ab67925060e5b0ebd8da616db4b151906
  languageName: node
  linkType: hard

"typescript@patch:typescript@4.9.5#~builtin<compat/typescript>, typescript@patch:typescript@^4.3.5#~builtin<compat/typescript>":
  version: 4.9.5
  resolution: "typescript@patch:typescript@npm%3A4.9.5#~builtin<compat/typescript>::version=4.9.5&hash=a1c5e5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 897c8ac656e01b132fa82be6a8e34c4507b19be63dbf1ff1d8287d775519081a7c91dd0ca3ec62536c8137228141d65b238dfb2e8987a3f5182818f58f83e7d7
  languageName: node
  linkType: hard

"typescript@patch:typescript@^4.6.4 || ^5.2.2#~builtin<compat/typescript>":
  version: 5.7.3
  resolution: "typescript@patch:typescript@npm%3A5.7.3#~builtin<compat/typescript>::version=5.7.3&hash=a1c5e5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 3b56d6afa03d9f6172d0b9cdb10e6b1efc9abc1608efd7a3d2f38773d5d8cfb9bbc68dfb72f0a7de5e8db04fc847f4e4baeddcd5ad9c9feda072234f0d788896
  languageName: node
  linkType: hard

"uid@npm:2.0.2, uid@npm:^2.0.0":
  version: 2.0.2
  resolution: "uid@npm:2.0.2"
  dependencies:
    "@lukeed/csprng": ^1.0.0
  checksum: e9d02d0562c74e74b5a2519e586db9d7f8204978e476cddd191ee1a9efb85efafdbab2dbf3fc3dde0f5da01fd9da161f37d604dabf513447fd2c03d008f1324c
  languageName: node
  linkType: hard

"undefsafe@npm:^2.0.5":
  version: 2.0.5
  resolution: "undefsafe@npm:2.0.5"
  checksum: 96c0466a5fbf395917974a921d5d4eee67bca4b30d3a31ce7e621e0228c479cf893e783a109af6e14329b52fe2f0cb4108665fad2b87b0018c0df6ac771261d5
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 68e659a98898d6a836a9a59e6adf14a5d799707f5ea629433e025ac90d239f75e408e2e5ff086afc3cace26f8b26ee52155293564593fbb4a2f666af57fc59bf
  languageName: node
  linkType: hard

"union-value@npm:^1.0.0":
  version: 1.0.1
  resolution: "union-value@npm:1.0.1"
  dependencies:
    arr-union: ^3.1.0
    get-value: ^2.0.6
    is-extendable: ^0.1.1
    set-value: ^2.0.1
  checksum: 8758d880cb9545f62ce9cfb9b791b2b7a206e0ff5cc4b9d7cd6581da2c6839837fbb45e639cf1fd8eef3cae08c0201b614b7c06dd9f5f70d9dbe7c5fe2fbf592
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unset-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "unset-value@npm:1.0.0"
  dependencies:
    has-value: ^0.3.1
    isobject: ^3.0.0
  checksum: 68a796dde4a373afdbf017de64f08490a3573ebee549136da0b3a2245299e7f65f647ef70dc13c4ac7f47b12fba4de1646fa0967a365638578fedce02b9c0b1f
  languageName: node
  linkType: hard

"unzipper@npm:^0.10.11":
  version: 0.10.14
  resolution: "unzipper@npm:0.10.14"
  dependencies:
    big-integer: ^1.6.17
    binary: ~0.3.0
    bluebird: ~3.4.1
    buffer-indexof-polyfill: ~1.0.0
    duplexer2: ~0.1.4
    fstream: ^1.0.12
    graceful-fs: ^4.2.2
    listenercount: ~1.0.1
    readable-stream: ~2.3.6
    setimmediate: ~1.0.4
  checksum: 0d9d0bdb566581534fba4ad88cbf037f3c1d9aa97fcd26ca52d30e7e198a3c6cb9e315deadc59821647c98657f233601cb9ebfc92f59228a1fe594197061760e
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.2
  resolution: "update-browserslist-db@npm:1.1.2"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 9cb353998d6d7d6ba1e46b8fa3db888822dd972212da4eda609d185eb5c3557a93fd59780ceb757afd4d84240518df08542736969e6a5d6d6ce2d58e9363aac6
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"urix@npm:^0.1.0":
  version: 0.1.0
  resolution: "urix@npm:0.1.0"
  checksum: 264f1b29360c33c0aec5fb9819d7e28f15d1a3b83175d2bcc9131efe8583f459f07364957ae3527f1478659ec5b2d0f1ad401dfb625f73e4d424b3ae35fc5fc0
  languageName: node
  linkType: hard

"use@npm:^3.1.0":
  version: 3.1.1
  resolution: "use@npm:3.1.1"
  checksum: 75b48673ab80d5139c76922630d5a8a44e72ed58dbaf54dee1b88352d10e1c1c1fc332066c782d8ae9a56503b85d3dc67ff6d2ffbd9821120466d1280ebb6d6e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1, utils-merge@npm:^1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:8.3.2, uuid@npm:^8.3.0":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: bcbb807a917d374a49f475fae2e87fdca7da5e5530820ef53f65ba1d12131bd81a92ecf259cc7ce317cbe0f289e7d79fdfebcef9bfa3087c8c8a2fa304c9be54
  languageName: node
  linkType: hard

"uuid@npm:^11.1.0":
  version: 11.1.0
  resolution: "uuid@npm:11.1.0"
  bin:
    uuid: dist/esm/bin/uuid
  checksum: 34aa51b9874ae398c2b799c88a127701408cd581ee89ec3baa53509dd8728cbb25826f2a038f9465f8b7be446f0fbf11558862965b18d21c993684297628d4d3
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0, uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: bdc36fb8095d3b41df197f5fb6f11e3a26adf4059df3213e3baa93810d8f0cc76f9a74aaefc18b73e91fe7e19154ed6f134eda6fded2e0f1c8d2272ed2d2d391
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.12
    "@types/istanbul-lib-coverage": ^2.0.1
    convert-source-map: ^2.0.0
  checksum: 968bcf1c7c88c04df1ffb463c179558a2ec17aa49e49376120504958239d9e9dad5281aa05f2a78542b8557f2be0b0b4c325710262f3b838b40d703d5ed30c23
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 7b91e455a8de9a0beaa9fe961e536b677da7f48c9a493edf4d4d4a87fd80a7a10267d438723364e432c2fcd00b5650b5378275cded362383ef570276e6312f4f
  languageName: node
  linkType: hard

"validator@npm:^13.9.0":
  version: 13.12.0
  resolution: "validator@npm:13.12.0"
  checksum: 21d48a7947c9e8498790550f56cd7971e0e3d724c73388226b109c1bac2728f4f88caddfc2f7ed4b076f9b0d004316263ac786a17e9c4edf075741200718cd32
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: 1.0.12
  checksum: a17e037bccd3ca8a25a80cb850903facdfed0de4864bd8728f1782370715d679fa72e0a0f5da7c1c1379365159901e5935f35be531229da53bbfc0efdabdb48e
  languageName: node
  linkType: hard

"watchpack@npm:^2.3.1, watchpack@npm:^2.4.0":
  version: 2.4.2
  resolution: "watchpack@npm:2.4.2"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: ec60a5f0e9efaeca0102fd9126346b3b2d523e01c34030d3fddf5813a7125765121ebdc2552981136dcd2c852deb1af0b39340f2fcc235f292db5399d0283577
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webpack-node-externals@npm:3.0.0":
  version: 3.0.0
  resolution: "webpack-node-externals@npm:3.0.0"
  checksum: 9f645a4dc8e122dac43cdc8c1367d4b44af20c79632438b633acc1b4fe64ea7ba1ad6ab61bd0fc46e1b873158c48d8c7a25a489cdab1f31299f00eb3b81cfc61
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 2ef63d77c4fad39de4a6db17323d75eb92897b32674e97d76f0a1e87c003882fc038571266ad0ef581ac734cbe20952912aaa26155f1905e96ce251adbb1eb4e
  languageName: node
  linkType: hard

"webpack@npm:5.73.0":
  version: 5.73.0
  resolution: "webpack@npm:5.73.0"
  dependencies:
    "@types/eslint-scope": ^3.7.3
    "@types/estree": ^0.0.51
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/wasm-edit": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    acorn: ^8.4.1
    acorn-import-assertions: ^1.7.6
    browserslist: ^4.14.5
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.9.3
    es-module-lexer: ^0.9.0
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.9
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.1.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.1.3
    watchpack: ^2.3.1
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 761add2395e37c7bd0b0727bea327496b8a2f5a702b29bd4907f9e80f6e7ea17ecdeefabf0683fdefe1148f725cd51eb81c4a73b4646eda9eb1a8d1a74ac8cae
  languageName: node
  linkType: hard

"webpack@npm:5.75.0":
  version: 5.75.0
  resolution: "webpack@npm:5.75.0"
  dependencies:
    "@types/eslint-scope": ^3.7.3
    "@types/estree": ^0.0.51
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/wasm-edit": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    acorn: ^8.7.1
    acorn-import-assertions: ^1.7.6
    browserslist: ^4.14.5
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.10.0
    es-module-lexer: ^0.9.0
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.9
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.1.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.1.3
    watchpack: ^2.4.0
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 0160331d6255bdb8027f2589458514709a4a6555e2868adb6356a309d3f7b2212cb129a00f343fe0f94f54a31b4677507a3adf9ae73badc1216105ac548681ea
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: 1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:^1.2.14":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.2":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: 1d9c2a3e36dfb09832f38e2e699c367ef190f96b82c71f809bc0822c306f5379df87bab47bed27ea99106d86447e50eb972d3c516c2f95782807a9d082fbea95
  languageName: node
  linkType: hard

"windows-release@npm:^4.0.0":
  version: 4.0.0
  resolution: "windows-release@npm:4.0.0"
  dependencies:
    execa: ^4.0.2
  checksum: 5c0ce2603a85e25e9a5c78eb1ef646aac7036da2fb942643f2120b11fc33ed94fbcdd340b2abbf27daa522efc9e52df36fe95b1c03cd9acd8d6c6c39f88f106b
  languageName: node
  linkType: hard

"word-wrap@npm:^1.0.3, word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: baad244e6e33335ea24e86e51868fe6823626e3a3c88d9a6674642afff1d34d9a154c917e74af8d845fd25d170c4ea9cf69a47133c3f3656e1252b3d462d9f6c
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.1":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^3.0.7
  checksum: a2c282c95ef5d8e1c27b335ae897b5eca00e85590d92a3fd69a437919b7b93ff36a69ea04145da55829d2164e724bc62202cdb5f4b208b425aba0807889375c7
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: b64b535861a6f310c5d9bfa10834cf49127c71922c297da9d4d1b45eeaae40bf9b4363275876088fbe2667e5db028d2cd4f8ee72eed9bede840a67d57dab7593
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0, yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard

"yargs-parser@npm:21.1.1, yargs-parser@npm:^21.0.1, yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2, yargs-parser@npm:^20.2.3":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 0685a8e58bbfb57fab6aefe03c6da904a59769bd803a722bb098bd5b0f29d274a1357762c7258fb487512811b8063fb5d2824a3415a0a4540598335b3b086c72
  languageName: node
  linkType: hard

"yargs@npm:^16.0.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: ^7.0.2
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.0
    y18n: ^5.0.5
    yargs-parser: ^20.2.2
  checksum: b1dbfefa679848442454b60053a6c95d62f2d2e21dd28def92b647587f415969173c6e99a0f3bab4f1b67ee8283bf735ebe3544013f09491186ba9e8a9a2b651
  languageName: node
  linkType: hard

"yargs@npm:^17.0.0, yargs@npm:^17.3.1, yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 0732468dd7622ed8a274f640f191f3eaf1f39d5349a1b72836df484998d7d9807fbea094e2f5486d6b0cd2414aad5775972df0e68f8604db89a239f0f4bf7443
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zip-stream@npm:^4.1.0":
  version: 4.1.1
  resolution: "zip-stream@npm:4.1.1"
  dependencies:
    archiver-utils: ^3.0.4
    compress-commons: ^4.1.2
    readable-stream: ^3.6.0
  checksum: 38f91ca116a38561cf184c29e035e9453b12c30eaf574e0993107a4a5331882b58c9a7f7b97f63910664028089fbde3296d0b3682d1ccb2ad96929e68f1b2b89
  languageName: node
  linkType: hard
