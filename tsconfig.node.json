{"extends": "@tsconfig/node22/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "eslint.config.*"], "compilerOptions": {"noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "skipLibCheck": true, "noImplicitAny": false, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"]}}