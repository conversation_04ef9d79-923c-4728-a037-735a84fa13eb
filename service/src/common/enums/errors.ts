export enum AuthErrors {
  UserNotFound = 'user_not_found',
  UserRoleNotFound = 'user_role_not_found',
  UnsupportedUserType = 'unsupported_user_type',
  RoleNotFound = 'role_not_found',
  OrganizationNotFound = 'organization_not_found',
  OrganizationTagNotFound = 'organization_tag_not_found',
  PhoneAlreadyExists = 'phone_already_exists',
  EmailAlreadyExists = 'email_already_exists',
  ProductNotFound = 'product_not_found',
  ActivityNotFound = 'activity_not_found',
  OrganizationActivityNotFound = 'organization_activity_not_found',
  OrganizationKpiNotFound = 'organization_kpi_not_found',
  OrganizationAttributeNotFound = 'organization_attribute_not_found',
  OrganizationResearchDevelopmentNotFound = 'organization_research_development_not_found',
  OrganizationRDProjectNotFound = 'organization_rd_project_not_found',
  OrganizationInitiativeNotFound = 'organization_initiative_not_found',
  OrganizationContractNotFound = 'organization_contract_not_found',
  OrganizationFormationNotFound = 'organization_formation_not_found',
  OrganizationSiteNotFound = 'organization_site_not_found',
  OrganizationEnvironmentNotFound = 'organization_environment_not_found',
  OrganizationWasteDistributionNotFound = 'organization_waste_distribution_not_found',
  OrganizationQuestionNotFound = 'organization_question_not_found',
  OrganizationOpportunityNotFound = 'organization_opportunity_not_found',
  OrganizationViewNotFound = 'organization_view_not_found',
  TokenNotFound = 'token_not_found',
  WrongCredentials = 'wrong_user_credentials',
  NoTokenProvided = 'no_token_provided',
  ExpiredToken = 'expired_token',
  InvalidToken = 'invalid_token',
  AccessDenied = 'access_denied',
  LinkExpired = 'link_expired',
  InvalidPercentageSum = 'invalid_percentage_sum',
}
