{"name": "service-taa", "version": "0.0.1", "description": "TAA service", "private": true, "license": "MIT", "scripts": {"dev": "nodemon main", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:migration:generate": "../scripts/generate-migration.sh", "db:migration:run": "ts-node src/main migrate", "db:migration:revert": "../scripts/revert-migration.sh"}, "dependencies": {"@aws-sdk/client-s3": "^3.804.0", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^9.0.1", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "1.4.3", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "axios": "^1.8.4", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "1.4.6", "exceljs": "^4.4.0", "google-auth-library": "^9.9.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mysql2": "^3.14.0", "nestjs-form-data": "^1.8.7", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "typeorm": "^0.3.12", "typeorm-transactional": "^0.4.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/lodash": "^4.14.191", "@types/multer": "^1.4.12", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-simple-import-sort": "10.0.0", "eslint-plugin-unused-imports": "2.0.0", "jest": "28.1.2", "nodemon": "^2.0.20", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}